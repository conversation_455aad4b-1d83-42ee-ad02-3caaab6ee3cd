import { ItemView, WorkspaceLeaf } from 'obsidian';
import type { SvelteComponent } from 'svelte';
import type GhostSyncPlugin from '../main';
import { setPluginContext } from './context';

export abstract class SvelteView extends ItemView {
  protected component: SvelteComponent | null = null;
  protected plugin: GhostSyncPlugin;

  constructor(leaf: WorkspaceLeaf, plugin: GhostSyncPlugin) {
    super(leaf);
    this.plugin = plugin;
  }

  async onOpen() {
    const container = this.contentEl;
    container.empty();
    container.addClass('svelte-view-container');

    // Create the Svelte component
    this.component = this.createComponent(container);
  }

  async onClose() {
    // Cleanup Svelte component
    if (this.component) {
      this.component.$destroy();
      this.component = null;
    }
  }

  protected abstract createComponent(container: HTMLElement): SvelteComponent;

  protected createSvelteComponent<T extends SvelteComponent>(
    ComponentClass: new (options: any) => T,
    container: HTMLElement,
    props: Record<string, any> = {}
  ): T {
    return new ComponentClass({
      target: container,
      props: {
        ...props,
        plugin: this.plugin
      },
      context: new Map([
        ['ghost-sync-plugin', { plugin: this.plugin }]
      ])
    });
  }
}
