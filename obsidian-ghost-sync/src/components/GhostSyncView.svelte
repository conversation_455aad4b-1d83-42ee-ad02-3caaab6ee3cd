<script lang="ts">
  import { onMount, createEventDispatcher } from 'svelte';
  import { TFile } from 'obsidian';
  import PropertyDisplay from './PropertyDisplay.svelte';
  import { getPluginContext } from './context';
  import type { SyncStatusData } from './types';
  import * as path from 'path';

  export let currentFile: TFile | null = null;
  export let syncStatus: SyncStatusData;

  const { plugin } = getPluginContext();
  const dispatch = createEventDispatcher();

  let isInArticlesDir = false;
  let articlesPath = '';

  $: {
    if (currentFile && plugin.settings.articlesDir) {
      articlesPath = path.normalize(plugin.settings.articlesDir);
      const filePath = path.normalize(currentFile.path);
      isInArticlesDir = filePath.startsWith(articlesPath);
    } else {
      isInArticlesDir = false;
    }
  }

  async function handleSyncToGhost() {
    await plugin.syncCurrentPostToGhost();
    dispatch('refresh');
  }

  async function handleSyncFromGhost() {
    dispatch('syncFromGhost');
  }

  function handleRefresh() {
    dispatch('refresh');
  }

  function handlePublish() {
    dispatch('publish');
  }

  function handleBrowsePosts() {
    dispatch('browsePosts');
  }
</script>

<div class="ghost-sync-status-view">
  <!-- Header -->
  <div class="ghost-sync-header">
    <h3>Ghost</h3>
  </div>

  {#if !currentFile}
    <p class="ghost-sync-no-file">No file selected</p>
  {:else if !isInArticlesDir}
    <p class="ghost-sync-not-article">
      File must be in {plugin.settings.articlesDir} directory
    </p>
  {:else}
    <!-- Feature Image Preview -->
    {#if syncStatus.ghostPost?.feature_image}
      <div class="ghost-sync-feature-image-container">
        <img 
          src={syncStatus.ghostPost.feature_image} 
          alt="Feature image"
          class="ghost-sync-feature-image"
        />
      </div>
    {/if}

    <!-- Status List -->
    <div class="ghost-sync-status-list">
      <PropertyDisplay 
        label="Title" 
        status={syncStatus.title} 
        value={syncStatus.ghostPost?.title} 
      />
      <PropertyDisplay 
        label="Slug" 
        status={syncStatus.slug} 
        value={syncStatus.ghostPost?.slug} 
      />
      <PropertyDisplay 
        label="Status" 
        status={syncStatus.status} 
        value={syncStatus.ghostPost?.status} 
      />
      <PropertyDisplay 
        label="Tags" 
        status={syncStatus.tags} 
        value={syncStatus.ghostPost?.tags} 
        isTagsList={true}
      />
      <PropertyDisplay 
        label="Primary Tag" 
        status={syncStatus.primary_tag} 
        value={syncStatus.ghostPost?.primary_tag?.name} 
      />
      <PropertyDisplay 
        label="Visibility" 
        status={syncStatus.visibility} 
        value={syncStatus.ghostPost?.visibility} 
      />
      <PropertyDisplay 
        label="Featured" 
        status={syncStatus.featured} 
        value={syncStatus.ghostPost?.featured} 
      />
      <PropertyDisplay 
        label="Feature Image" 
        status={syncStatus.feature_image} 
        value={syncStatus.ghostPost?.feature_image ? 'Set' : 'None'} 
      />
      <PropertyDisplay 
        label="Newsletter" 
        status={syncStatus.newsletter} 
        value={syncStatus.ghostPost?.newsletter?.name || 'None'} 
      />
      <PropertyDisplay 
        label="Email Sent" 
        status={syncStatus.email_sent} 
        value={syncStatus.ghostPost?.email ? 'Yes' : 'No'} 
      />
    </div>

    <!-- Buttons -->
    <div class="ghost-sync-buttons">
      <button 
        class="ghost-sync-btn" 
        on:click={handleSyncToGhost}
      >
        Sync Now
      </button>
      
      {#if syncStatus.ghostPost}
        <button 
          class="ghost-sync-btn mod-cta" 
          on:click={handlePublish}
        >
          Publish
        </button>
      {/if}
      
      <button 
        class="ghost-sync-btn" 
        on:click={handleBrowsePosts}
      >
        Browse Posts
      </button>
      
      <button 
        class="ghost-sync-btn" 
        on:click={handleRefresh}
      >
        Refresh
      </button>
    </div>

    <!-- Controls -->
    <div class="ghost-sync-controls">
      <button 
        class="mod-cta ghost-sync-btn" 
        on:click={handleSyncToGhost}
      >
        Sync to Ghost
      </button>
      
      <button 
        class="ghost-sync-btn" 
        on:click={handleSyncFromGhost}
      >
        Sync from Ghost
      </button>
      
      <button 
        class="ghost-sync-btn" 
        on:click={handleRefresh}
      >
        Refresh
      </button>
    </div>
  {/if}
</div>

<style>
  .ghost-sync-feature-image-container {
    margin-bottom: 16px;
  }
  
  .ghost-sync-feature-image {
    width: 100%;
    max-height: 200px;
    object-fit: cover;
    border-radius: 4px;
    border: 1px solid var(--background-modifier-border);
  }
</style>
