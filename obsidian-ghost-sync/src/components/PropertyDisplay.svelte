<script lang="ts">
  import StatusBadge from './StatusBadge.svelte';
  import type { SyncStatus } from './types';

  export let label: string;
  export let status: SyncStatus;
  export let value: any;
  export let isTagsList: boolean = false;

  $: displayValue = formatValue(value, isTagsList);

  function formatValue(val: any, isTags: boolean): string {
    if (val === null || val === undefined) {
      return 'None';
    }
    
    if (typeof val === 'boolean') {
      return val ? 'Yes' : 'No';
    }
    
    if (isTags && Array.isArray(val)) {
      if (val.length === 0) {
        return 'None';
      }
      return val.map(tag => tag.name || tag).join(', ');
    }
    
    if (typeof val === 'object' && val.name) {
      return val.name;
    }
    
    return String(val);
  }
</script>

<div class="ghost-sync-compact-item">
  <StatusBadge {status} compact={true} />
  
  <span class="ghost-sync-compact-content">
    <strong>{label}: </strong>
    <span class="ghost-sync-value">"{displayValue}"</span>
  </span>
</div>

<style>
  /* Component-specific styles can go here if needed */
  /* Most styles are inherited from the global CSS */
</style>
