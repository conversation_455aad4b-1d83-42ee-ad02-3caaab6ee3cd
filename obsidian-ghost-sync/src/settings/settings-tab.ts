import { App, PluginSettingTab, Setting } from "obsidian";
import type GhostSyncPlugin from "../main";

export class GhostSyncSettingTab extends PluginSettingTab {
  plugin: GhostSyncPlugin;

  constructor(app: App, plugin: GhostSyncPlugin) {
    super(app, plugin);
    this.plugin = plugin;
  }

  display(): void {
    const { containerEl } = this;

    containerEl.empty();

    containerEl.createEl("h2", { text: "Ghost Sync Settings" });

    new Setting(containerEl)
      .setName("Ghost site URL")
      .setDesc("Your Ghost site URL (e.g., https://your-site.ghost.io)")
      .addText(text => text
        .setPlaceholder("https://your-site.ghost.io")
        .setValue(this.plugin.settings.ghostUrl)
        .onChange(async (value) => {
          this.plugin.settings.ghostUrl = value;
          await this.plugin.saveSettings();
        }));

    new Setting(containerEl)
      .setName("Ghost Admin API Key")
      .setDesc("Your Ghost Admin API key (format: id:secret)")
      .addText(text => text
        .setPlaceholder("id:secret")
        .setValue(this.plugin.settings.ghostAdminApiKey)
        .onChange(async (value) => {
          this.plugin.settings.ghostAdminApiKey = value;
          await this.plugin.saveSettings();
        }));

    new Setting(containerEl)
      .setName("Articles directory")
      .setDesc("Directory where your articles are stored")
      .addText(text => text
        .setPlaceholder("articles")
        .setValue(this.plugin.settings.articlesDir)
        .onChange(async (value) => {
          this.plugin.settings.articlesDir = value;
          await this.plugin.saveSettings();
        }));

    new Setting(containerEl)
      .setName("Verbose output")
      .setDesc("Show detailed output in console")
      .addToggle(toggle => toggle
        .setValue(this.plugin.settings.verbose)
        .onChange(async (value) => {
          this.plugin.settings.verbose = value;
          await this.plugin.saveSettings();
        }));
  }
}
