import { Workspace<PERSON>eaf, TFile, Notice } from "obsidian";
import type GhostSyncPlugin from "../main";
import type { GhostPost } from "../types";
import { ContentConverter } from "../utils/content-converter";
import { SvelteView } from "../components/SvelteView";
import GhostSyncView from "../components/GhostSyncView.svelte";
import PublishDialog from "../components/PublishDialog.svelte";
import PostBrowser from "../components/PostBrowser.svelte";
import type { SyncStatusData, PublishOptions } from "../components/types";
import { ObsidianGhostAPI } from "../api/ghost-api";
import * as path from "path";

export const VIEW_TYPE_GHOST_SYNC_STATUS = 'ghost-sync-status';

export class SvelteSyncStatusView extends SvelteView {
  private currentFile: TFile | null = null;
  private syncStatus: SyncStatusData = {
    title: 'unknown',
    slug: 'unknown',
    status: 'unknown',
    tags: 'unknown',
    featured: 'unknown',
    feature_image: 'unknown',
    visibility: 'unknown',
    primary_tag: 'unknown',
    created_at: 'unknown',
    updated_at: 'unknown',
    published_at: 'unknown',
    newsletter: 'unknown',
    email_sent: 'unknown'
  };

  private publishDialog: PublishDialog | null = null;
  private postBrowser: PostBrowser | null = null;

  getViewType() {
    return VIEW_TYPE_GHOST_SYNC_STATUS;
  }

  getDisplayText() {
    return 'Ghost';
  }

  getIcon() {
    return 'sync';
  }

  async onOpen() {
    await super.onOpen();

    // Listen for active file changes
    this.registerEvent(
      this.app.workspace.on('active-leaf-change', () => {
        this.updateCurrentFile();
      })
    );

    // Listen for file open events
    this.registerEvent(
      this.app.workspace.on('file-open', () => {
        this.updateCurrentFile();
      })
    );

    // Listen for file changes
    this.registerEvent(
      this.app.vault.on('modify', (file) => {
        if (file === this.currentFile) {
          this.updateSyncStatus();
        }
      })
    );

    this.updateCurrentFile();
  }

  protected createComponent(container: HTMLElement) {
    const component = this.createSvelteComponent(
      GhostSyncView,
      container,
      {
        currentFile: this.currentFile,
        syncStatus: this.syncStatus
      }
    );

    // Listen to component events
    component.$on('refresh', () => {
      this.updateSyncStatus();
    });

    component.$on('syncFromGhost', async () => {
      await this.syncFromGhost();
    });

    component.$on('publish', () => {
      this.showPublishDialog();
    });

    component.$on('browsePosts', () => {
      this.showPostBrowser();
    });

    return component;
  }

  private updateCurrentFile() {
    const activeFile = this.app.workspace.getActiveFile();
    if (activeFile !== this.currentFile) {
      this.currentFile = activeFile;
      this.updateSyncStatus();
      
      // Update component props
      if (this.component) {
        this.component.$set({ currentFile: this.currentFile });
      }
    }
  }

  private async updateSyncStatus() {
    if (!this.currentFile) {
      return;
    }

    try {
      // Get file content and frontmatter
      const content = await this.app.vault.read(this.currentFile);
      const frontmatter = this.app.metadataCache.getFileCache(this.currentFile)?.frontmatter;

      if (!frontmatter?.slug) {
        // Reset status if no slug
        this.resetSyncStatus();
        return;
      }

      // Get Ghost post
      const ghostAPI = new ObsidianGhostAPI(this.plugin.settings.ghostUrl, this.plugin.settings.ghostAdminApiKey);
      const ghostPost = await ghostAPI.getPostBySlug(frontmatter.slug);

      if (!ghostPost) {
        this.resetSyncStatus();
        return;
      }

      // Compare and update status
      this.syncStatus = {
        title: this.compareValues(frontmatter.title, ghostPost.title),
        slug: this.compareValues(frontmatter.slug, ghostPost.slug),
        status: this.compareValues(frontmatter.status, ghostPost.status),
        tags: this.compareTags(frontmatter.tags, ghostPost.tags),
        featured: this.compareValues(frontmatter.featured, ghostPost.featured),
        feature_image: this.compareValues(frontmatter.feature_image, ghostPost.feature_image),
        visibility: this.compareValues(frontmatter.visibility, ghostPost.visibility),
        primary_tag: this.compareValues(frontmatter.primary_tag, ghostPost.primary_tag?.name),
        created_at: this.compareValues(frontmatter.created_at, ghostPost.created_at),
        updated_at: this.compareValues(frontmatter.updated_at, ghostPost.updated_at),
        published_at: this.compareValues(frontmatter.published_at, ghostPost.published_at),
        newsletter: this.compareValues(frontmatter.newsletter, ghostPost.newsletter?.name),
        email_sent: this.compareValues(frontmatter.email_sent, ghostPost.email ? 'Yes' : 'No'),
        ghostPost
      };

      // Update component
      if (this.component) {
        this.component.$set({ syncStatus: this.syncStatus });
      }

    } catch (error) {
      console.error('Error updating sync status:', error);
      this.resetSyncStatus();
    }
  }

  private compareValues(local: any, ghost: any): 'synced' | 'different' | 'unknown' {
    if (local === undefined || local === null) return 'unknown';
    if (ghost === undefined || ghost === null) return 'unknown';
    return local === ghost ? 'synced' : 'different';
  }

  private compareTags(localTags: string[] | undefined, ghostTags: any[] | undefined): 'synced' | 'different' | 'unknown' {
    if (!localTags && !ghostTags) return 'synced';
    if (!localTags || !ghostTags) return 'unknown';
    
    const localTagNames = localTags.sort();
    const ghostTagNames = ghostTags.map(t => t.name).sort();
    
    if (localTagNames.length !== ghostTagNames.length) return 'different';
    
    for (let i = 0; i < localTagNames.length; i++) {
      if (localTagNames[i] !== ghostTagNames[i]) return 'different';
    }
    
    return 'synced';
  }

  private resetSyncStatus() {
    this.syncStatus = {
      title: 'unknown',
      slug: 'unknown',
      status: 'unknown',
      tags: 'unknown',
      featured: 'unknown',
      feature_image: 'unknown',
      visibility: 'unknown',
      primary_tag: 'unknown',
      created_at: 'unknown',
      updated_at: 'unknown',
      published_at: 'unknown',
      newsletter: 'unknown',
      email_sent: 'unknown'
    };

    if (this.component) {
      this.component.$set({ syncStatus: this.syncStatus });
    }
  }

  private async syncFromGhost() {
    if (!this.currentFile || !this.syncStatus.ghostPost) {
      new Notice('No Ghost post to sync from');
      return;
    }

    try {
      new Notice('Syncing from Ghost...');

      const articleContent = ContentConverter.convertGhostPostToArticle(this.syncStatus.ghostPost);
      await this.app.vault.modify(this.currentFile, articleContent);

      new Notice('Synced from Ghost successfully');
      setTimeout(() => this.updateSyncStatus(), 1000);
    } catch (error) {
      console.error('Error syncing from Ghost:', error);
      new Notice(`Error syncing from Ghost: ${error.message}`);
    }
  }

  private showPublishDialog() {
    if (!this.syncStatus.ghostPost) {
      new Notice('No Ghost post to publish');
      return;
    }

    // Create modal container
    const modalContainer = document.body.createDiv();
    
    this.publishDialog = new PublishDialog({
      target: modalContainer,
      props: {
        ghostPost: this.syncStatus.ghostPost,
        show: true
      },
      context: new Map([
        ['ghost-sync-plugin', { plugin: this.plugin }]
      ])
    });

    this.publishDialog.$on('confirm', async (event) => {
      await this.handlePublish(event.detail);
      this.publishDialog?.$destroy();
      modalContainer.remove();
    });

    this.publishDialog.$on('cancel', () => {
      this.publishDialog?.$destroy();
      modalContainer.remove();
    });
  }

  private showPostBrowser() {
    // Create modal container
    const modalContainer = document.body.createDiv();
    
    this.postBrowser = new PostBrowser({
      target: modalContainer,
      props: {
        show: true
      },
      context: new Map([
        ['ghost-sync-plugin', { plugin: this.plugin }]
      ])
    });

    this.postBrowser.$on('select', async (event) => {
      await this.handlePostSelection(event.detail);
      this.postBrowser?.$destroy();
      modalContainer.remove();
    });

    this.postBrowser.$on('cancel', () => {
      this.postBrowser?.$destroy();
      modalContainer.remove();
    });
  }

  private async handlePublish(options: PublishOptions) {
    // Implementation for publishing
    new Notice(`Publishing with action: ${options.action}`);
  }

  private async handlePostSelection(post: GhostPost) {
    try {
      new Notice(`Syncing "${post.title}" from Ghost...`);

      const articleContent = ContentConverter.convertGhostPostToArticle(post);
      const filename = post.slug + '.md';
      const filePath = path.posix.join(this.plugin.settings.articlesDir, filename);

      const existingFile = this.app.vault.getAbstractFileByPath(filePath);
      if (existingFile) {
        await this.app.vault.modify(existingFile as any, articleContent);
        new Notice(`Updated "${post.title}" in ${filePath}`);
      } else {
        await this.app.vault.create(filePath, articleContent);
        new Notice(`Created "${post.title}" in ${filePath}`);
      }
    } catch (error) {
      console.error("Error syncing selected post:", error);
      new Notice(`Error syncing post: ${error.message}`);
    }
  }
}
