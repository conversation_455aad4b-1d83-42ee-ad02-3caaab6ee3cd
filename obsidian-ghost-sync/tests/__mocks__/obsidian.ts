// Mock implementation of Obsidian API for testing

export class ItemView {
  contentEl = {
    empty: jest.fn(),
    addClass: jest.fn(),
    createEl: jest.fn(() => ({
      createEl: jest.fn(),
      setText: jest.fn(),
      createDiv: jest.fn(() => ({
        createDiv: jest.fn(),
        setText: jest.fn()
      }))
    }))
  };
  leaf: any;
  app = {
    workspace: {
      on: jest.fn(),
      getActiveViewOfType: jest.fn(),
      activeEditor: { file: null as any },
      getLeavesOfType: jest.fn(() => [])
    },
    vault: {
      on: jest.fn(),
      read: jest.fn()
    }
  };
  registerEvent = jest.fn();
  
  constructor(leaf: any) {
    this.leaf = leaf;
  }
  
  getViewType() { return 'mock-view'; }
  getDisplayText() { return 'Mock View'; }
  getIcon() { return 'mock'; }
}

export class WorkspaceLeaf {
  on = jest.fn();
}

export class MarkdownView {}

export class TFile {}

export const Notice = jest.fn();

export class Plugin {}

export class PluginSettingTab {}

export const Setting = jest.fn();

export class SuggestModal {}

export class Modal {}

export class App {}

export const requestUrl = jest.fn();
