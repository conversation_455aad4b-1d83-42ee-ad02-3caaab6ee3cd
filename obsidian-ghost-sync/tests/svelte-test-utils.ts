import { render, type RenderOptions } from '@testing-library/svelte';
import type { SvelteComponent } from 'svelte';
import type GhostSyncPlugin from '../src/main';
import type { PluginContext } from '../src/components/types';

// Mock plugin instance for testing
export function createMockPlugin(): Partial<GhostSyncPlugin> {
  return {
    settings: {
      ghostUrl: 'https://test.ghost.io',
      ghostAdminApiKey: 'test-key',
      articlesDir: 'articles',
      verbose: false
    },
    newsletters: [
      {
        id: 'test-newsletter',
        name: 'Test Newsletter',
        slug: 'test-newsletter',
        description: 'Test newsletter description'
      }
    ],
    syncCurrentPostToGhost: jest.fn().mockResolvedValue(undefined),
    activateSyncStatusView: jest.fn(),
    loadNewsletters: jest.fn().mockResolvedValue(undefined)
  };
}

// Custom render function that provides plugin context
export function renderWithContext<T extends SvelteComponent>(
  component: new (...args: any[]) => T,
  options: { props?: Record<string, any>; plugin?: Partial<GhostSyncPlugin> } = {}
) {
  const { plugin = createMockPlugin(), props = {}, ...renderOptions } = options;

  const context = new Map([
    ['ghost-sync-plugin', { plugin }]
  ]);

  return render(component, {
    props,
    context,
    ...renderOptions
  });
}

// Mock Obsidian APIs for testing
export function setupObsidianMocks() {
  // Mock Notice
  global.Notice = jest.fn();

  // Mock TFile
  global.TFile = jest.fn();

  // Mock App
  global.App = jest.fn();

  // Mock Workspace
  global.Workspace = jest.fn();

  // Mock Vault
  global.Vault = jest.fn();
}

// Mock Ghost API responses
export const mockGhostPost = {
  id: 'test-post-id',
  title: 'Test Post',
  slug: 'test-post',
  status: 'published',
  featured: false,
  feature_image: null,
  visibility: 'public',
  created_at: '2023-01-01T00:00:00.000Z',
  updated_at: '2023-01-01T00:00:00.000Z',
  published_at: '2023-01-01T00:00:00.000Z',
  tags: [
    { id: 'tag1', name: 'test-tag', slug: 'test-tag' }
  ],
  primary_tag: { id: 'tag1', name: 'test-tag', slug: 'test-tag' },
  newsletter: null,
  email: null
};

export const mockSyncStatus = {
  title: 'synced' as const,
  slug: 'synced' as const,
  status: 'synced' as const,
  tags: 'synced' as const,
  featured: 'synced' as const,
  feature_image: 'synced' as const,
  visibility: 'synced' as const,
  primary_tag: 'synced' as const,
  created_at: 'synced' as const,
  updated_at: 'synced' as const,
  published_at: 'synced' as const,
  newsletter: 'synced' as const,
  email_sent: 'synced' as const,
  ghostPost: mockGhostPost
};

// Helper to wait for Svelte component updates
export function tick() {
  return new Promise(resolve => setTimeout(resolve, 0));
}
