import { screen } from '@testing-library/svelte';
import StatusBadge from '../../src/components/StatusBadge.svelte';
import { renderWithContext } from '../svelte-test-utils';

describe('StatusBadge', () => {
  it('renders synced status correctly', () => {
    renderWithContext(StatusBadge, { props: { status: 'synced' } });

    const badge = screen.getByText('Synced');
    expect(badge).toBeInTheDocument();
    expect(badge).toHaveClass('ghost-sync-status-badge');
    expect(badge).toHaveClass('ghost-sync-status-badge-success');
  });

  it('renders different status correctly', () => {
    renderWithContext(StatusBadge, { props: { status: 'different' } });

    const badge = screen.getByText('Different');
    expect(badge).toBeInTheDocument();
    expect(badge).toHaveClass('ghost-sync-status-badge');
    expect(badge).toHaveClass('ghost-sync-status-badge-warning');
  });

  it('renders unknown status correctly', () => {
    renderWithContext(StatusBadge, { props: { status: 'unknown' } });

    const badge = screen.getByText('Unknown');
    expect(badge).toBeInTheDocument();
    expect(badge).toHaveClass('ghost-sync-status-badge');
    expect(badge).toHaveClass('ghost-sync-status-badge-unknown');
  });

  it('renders compact version correctly', () => {
    renderWithContext(StatusBadge, { props: { status: 'synced', compact: true } });

    const badge = screen.getByText('✓');
    expect(badge).toBeInTheDocument();
    expect(badge).toHaveClass('ghost-sync-badge');
    expect(badge).toHaveClass('ghost-sync-badge-synced');
  });

  it('renders compact different status correctly', () => {
    renderWithContext(StatusBadge, { props: { status: 'different', compact: true } });

    const badge = screen.getByText('!');
    expect(badge).toBeInTheDocument();
    expect(badge).toHaveClass('ghost-sync-badge');
    expect(badge).toHaveClass('ghost-sync-badge-different');
  });

  it('renders compact unknown status correctly', () => {
    renderWithContext(StatusBadge, { props: { status: 'unknown', compact: true } });

    const badge = screen.getByText('?');
    expect(badge).toBeInTheDocument();
    expect(badge).toHaveClass('ghost-sync-badge');
    expect(badge).toHaveClass('ghost-sync-badge-unknown');
  });
});
