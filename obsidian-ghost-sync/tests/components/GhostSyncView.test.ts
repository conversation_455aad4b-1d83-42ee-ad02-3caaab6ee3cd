import { render, screen, fireEvent } from '@testing-library/svelte';
import { TFile } from 'obsidian';
import GhostSyncView from '../../src/components/GhostSyncView.svelte';
import { renderWithContext, mockSyncStatus } from '../svelte-test-utils';

// Mock path module
jest.mock('path', () => ({
  normalize: jest.fn((path) => path)
}));

describe('GhostSyncView', () => {
  const mockFile = {
    path: 'articles/test-post.md',
    name: 'test-post.md'
  } as TFile;

  it('renders header correctly', () => {
    renderWithContext(GhostSyncView, {
      props: {
        currentFile: mockFile,
        syncStatus: mockSyncStatus
      }
    });
    
    expect(screen.getByText('Ghost')).toBeInTheDocument();
  });

  it('shows no file selected message when no file', () => {
    renderWithContext(GhostSyncView, {
      props: {
        currentFile: null,
        syncStatus: mockSyncStatus
      }
    });
    
    expect(screen.getByText('No file selected')).toBeInTheDocument();
  });

  it('shows directory warning when file not in articles dir', () => {
    const fileOutsideArticles = {
      path: 'other/test-post.md',
      name: 'test-post.md'
    } as TFile;

    renderWithContext(GhostSyncView, {
      props: {
        currentFile: fileOutsideArticles,
        syncStatus: mockSyncStatus
      }
    });
    
    expect(screen.getByText(/File must be in articles directory/)).toBeInTheDocument();
  });

  it('renders property displays when file is valid', () => {
    renderWithContext(GhostSyncView, {
      props: {
        currentFile: mockFile,
        syncStatus: mockSyncStatus
      }
    });
    
    expect(screen.getByText('Title:')).toBeInTheDocument();
    expect(screen.getByText('Slug:')).toBeInTheDocument();
    expect(screen.getByText('Status:')).toBeInTheDocument();
    expect(screen.getByText('Tags:')).toBeInTheDocument();
  });

  it('renders feature image when present', () => {
    const syncStatusWithImage = {
      ...mockSyncStatus,
      ghostPost: {
        ...mockSyncStatus.ghostPost!,
        feature_image: 'https://example.com/image.jpg'
      }
    };

    renderWithContext(GhostSyncView, {
      props: {
        currentFile: mockFile,
        syncStatus: syncStatusWithImage
      }
    });
    
    const image = screen.getByAltText('Feature image');
    expect(image).toBeInTheDocument();
    expect(image).toHaveAttribute('src', 'https://example.com/image.jpg');
  });

  it('renders control buttons', () => {
    renderWithContext(GhostSyncView, {
      props: {
        currentFile: mockFile,
        syncStatus: mockSyncStatus
      }
    });
    
    expect(screen.getByText('Sync Now')).toBeInTheDocument();
    expect(screen.getByText('Publish')).toBeInTheDocument();
    expect(screen.getByText('Browse Posts')).toBeInTheDocument();
    expect(screen.getByText('Refresh')).toBeInTheDocument();
  });

  it('emits refresh event when refresh button clicked', async () => {
    const { component } = renderWithContext(GhostSyncView, {
      props: {
        currentFile: mockFile,
        syncStatus: mockSyncStatus
      }
    });

    const refreshHandler = jest.fn();
    component.$on('refresh', refreshHandler);

    const refreshButton = screen.getByText('Refresh');
    await fireEvent.click(refreshButton);

    expect(refreshHandler).toHaveBeenCalled();
  });

  it('emits publish event when publish button clicked', async () => {
    const { component } = renderWithContext(GhostSyncView, {
      props: {
        currentFile: mockFile,
        syncStatus: mockSyncStatus
      }
    });

    const publishHandler = jest.fn();
    component.$on('publish', publishHandler);

    const publishButton = screen.getByText('Publish');
    await fireEvent.click(publishButton);

    expect(publishHandler).toHaveBeenCalled();
  });

  it('emits browsePosts event when browse posts button clicked', async () => {
    const { component } = renderWithContext(GhostSyncView, {
      props: {
        currentFile: mockFile,
        syncStatus: mockSyncStatus
      }
    });

    const browseHandler = jest.fn();
    component.$on('browsePosts', browseHandler);

    const browseButton = screen.getByText('Browse Posts');
    await fireEvent.click(browseButton);

    expect(browseHandler).toHaveBeenCalled();
  });

  it('does not show publish button when no ghost post', () => {
    const syncStatusWithoutPost = {
      ...mockSyncStatus,
      ghostPost: undefined
    };

    renderWithContext(GhostSyncView, {
      props: {
        currentFile: mockFile,
        syncStatus: syncStatusWithoutPost
      }
    });
    
    // Should have Sync Now but not Publish
    expect(screen.getByText('Sync Now')).toBeInTheDocument();
    expect(screen.queryByText('Publish')).not.toBeInTheDocument();
  });
});
