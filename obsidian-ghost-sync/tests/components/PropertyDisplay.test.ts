import { render, screen } from '@testing-library/svelte';
import PropertyDisplay from '../../src/components/PropertyDisplay.svelte';

describe('PropertyDisplay', () => {
  it('renders property with string value', () => {
    render(PropertyDisplay, {
      label: 'Title',
      status: 'synced',
      value: 'Test Post Title'
    });
    
    expect(screen.getByText('Title:')).toBeInTheDocument();
    expect(screen.getByText('"Test Post Title"')).toBeInTheDocument();
    expect(screen.getByText('✓')).toBeInTheDocument();
  });

  it('renders property with boolean value', () => {
    render(PropertyDisplay, {
      label: 'Featured',
      status: 'different',
      value: true
    });
    
    expect(screen.getByText('Featured:')).toBeInTheDocument();
    expect(screen.getByText('"Yes"')).toBeInTheDocument();
    expect(screen.getByText('!')).toBeInTheDocument();
  });

  it('renders property with false boolean value', () => {
    render(PropertyDisplay, {
      label: 'Featured',
      status: 'synced',
      value: false
    });
    
    expect(screen.getByText('Featured:')).toBeInTheDocument();
    expect(screen.getByText('"No"')).toBeInTheDocument();
  });

  it('renders property with null value', () => {
    render(PropertyDisplay, {
      label: 'Feature Image',
      status: 'unknown',
      value: null
    });
    
    expect(screen.getByText('Feature Image:')).toBeInTheDocument();
    expect(screen.getByText('"None"')).toBeInTheDocument();
    expect(screen.getByText('?')).toBeInTheDocument();
  });

  it('renders property with object value', () => {
    render(PropertyDisplay, {
      label: 'Primary Tag',
      status: 'synced',
      value: { name: 'test-tag' }
    });
    
    expect(screen.getByText('Primary Tag:')).toBeInTheDocument();
    expect(screen.getByText('"test-tag"')).toBeInTheDocument();
  });

  it('renders tags list correctly', () => {
    render(PropertyDisplay, {
      label: 'Tags',
      status: 'synced',
      value: [
        { name: 'tag1' },
        { name: 'tag2' },
        { name: 'tag3' }
      ],
      isTagsList: true
    });
    
    expect(screen.getByText('Tags:')).toBeInTheDocument();
    expect(screen.getByText('"tag1, tag2, tag3"')).toBeInTheDocument();
  });

  it('renders empty tags list correctly', () => {
    render(PropertyDisplay, {
      label: 'Tags',
      status: 'unknown',
      value: [],
      isTagsList: true
    });
    
    expect(screen.getByText('Tags:')).toBeInTheDocument();
    expect(screen.getByText('"None"')).toBeInTheDocument();
  });

  it('has correct CSS classes', () => {
    const { container } = render(PropertyDisplay, {
      label: 'Test',
      status: 'synced',
      value: 'test'
    });
    
    const item = container.querySelector('.ghost-sync-compact-item');
    expect(item).toBeInTheDocument();
    
    const content = container.querySelector('.ghost-sync-compact-content');
    expect(content).toBeInTheDocument();
    
    const value = container.querySelector('.ghost-sync-value');
    expect(value).toBeInTheDocument();
  });
});
