import { render, screen, fireEvent } from '@testing-library/svelte';
import PublishDialog from '../../src/components/PublishDialog.svelte';
import { renderWithContext, mockGhostPost } from '../svelte-test-utils';

describe('PublishDialog', () => {
  it('renders dialog when show is true', () => {
    renderWithContext(PublishDialog, {
      props: {
        ghostPost: mockGhostPost,
        show: true
      }
    });
    
    expect(screen.getByText('Publish Options')).toBeInTheDocument();
  });

  it('does not render dialog when show is false', () => {
    renderWithContext(PublishDialog, {
      props: {
        ghostPost: mockGhostPost,
        show: false
      }
    });
    
    expect(screen.queryByText('Publish Options')).not.toBeInTheDocument();
  });

  it('shows email already sent message when email exists', () => {
    const postWithEmail = {
      ...mockGhostPost,
      email: { id: 'email-id' }
    };

    renderWithContext(PublishDialog, {
      props: {
        ghostPost: postWithEmail,
        show: true
      }
    });
    
    expect(screen.getByText('Publish Options (Email Already Sent)')).toBeInTheDocument();
  });

  it('renders action options', () => {
    renderWithContext(PublishDialog, {
      props: {
        ghostPost: mockGhostPost,
        show: true
      }
    });
    
    expect(screen.getByText('Publish to website only')).toBeInTheDocument();
    expect(screen.getByText('Send email only')).toBeInTheDocument();
    expect(screen.getByText('Publish & send newsletter')).toBeInTheDocument();
  });

  it('disables email options when email already sent', () => {
    const postWithEmail = {
      ...mockGhostPost,
      email: { id: 'email-id' }
    };

    renderWithContext(PublishDialog, {
      props: {
        ghostPost: postWithEmail,
        show: true
      }
    });
    
    const sendOnlyRadio = screen.getByDisplayValue('send-only');
    const publishSendRadio = screen.getByDisplayValue('publish-send');
    
    expect(sendOnlyRadio).toBeDisabled();
    expect(publishSendRadio).toBeDisabled();
  });

  it('shows newsletter selection when email action selected', async () => {
    renderWithContext(PublishDialog, {
      props: {
        ghostPost: mockGhostPost,
        show: true
      }
    });
    
    const publishSendRadio = screen.getByDisplayValue('publish-send');
    await fireEvent.click(publishSendRadio);
    
    expect(screen.getByText('Newsletter')).toBeInTheDocument();
    expect(screen.getByText('Test Newsletter')).toBeInTheDocument();
  });

  it('shows email segment input when email action selected', async () => {
    renderWithContext(PublishDialog, {
      props: {
        ghostPost: mockGhostPost,
        show: true
      }
    });
    
    const publishSendRadio = screen.getByDisplayValue('publish-send');
    await fireEvent.click(publishSendRadio);
    
    expect(screen.getByText('Email Segment (optional)')).toBeInTheDocument();
    expect(screen.getByPlaceholderText('e.g., status:free, status:-free')).toBeInTheDocument();
  });

  it('shows test mode checkbox when email action selected', async () => {
    renderWithContext(PublishDialog, {
      props: {
        ghostPost: mockGhostPost,
        show: true
      }
    });
    
    const publishSendRadio = screen.getByDisplayValue('publish-send');
    await fireEvent.click(publishSendRadio);
    
    expect(screen.getByText('Test mode (send to \'tester\' members only)')).toBeInTheDocument();
  });

  it('emits confirm event with correct options', async () => {
    const { component } = renderWithContext(PublishDialog, {
      props: {
        ghostPost: mockGhostPost,
        show: true
      }
    });

    const confirmHandler = jest.fn();
    component.$on('confirm', confirmHandler);

    // Select publish-send action
    const publishSendRadio = screen.getByDisplayValue('publish-send');
    await fireEvent.click(publishSendRadio);

    // Select newsletter
    const newsletterSelect = screen.getByDisplayValue('');
    await fireEvent.change(newsletterSelect, { target: { value: 'test-newsletter' } });

    // Click confirm
    const confirmButton = screen.getByText('Confirm');
    await fireEvent.click(confirmButton);

    expect(confirmHandler).toHaveBeenCalledWith(
      expect.objectContaining({
        detail: expect.objectContaining({
          action: 'publish-send',
          newsletter: expect.objectContaining({
            slug: 'test-newsletter'
          })
        })
      })
    );
  });

  it('emits cancel event when cancel button clicked', async () => {
    const { component } = renderWithContext(PublishDialog, {
      props: {
        ghostPost: mockGhostPost,
        show: true
      }
    });

    const cancelHandler = jest.fn();
    component.$on('cancel', cancelHandler);

    const cancelButton = screen.getByText('Cancel');
    await fireEvent.click(cancelButton);

    expect(cancelHandler).toHaveBeenCalled();
  });

  it('disables confirm button when newsletter required but not selected', async () => {
    renderWithContext(PublishDialog, {
      props: {
        ghostPost: mockGhostPost,
        show: true
      }
    });

    const publishSendRadio = screen.getByDisplayValue('publish-send');
    await fireEvent.click(publishSendRadio);

    const confirmButton = screen.getByText('Confirm');
    expect(confirmButton).toBeDisabled();
  });
});
