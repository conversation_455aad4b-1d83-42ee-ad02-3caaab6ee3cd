import { ObsidianGhostAPI } from '../src/api/ghost-api';

// Mock the Ghost Admin API
const mockGhostAPI = {
  posts: {
    browse: jest.fn(),
    read: jest.fn(),
    add: jest.fn(),
    edit: jest.fn()
  },
  newsletters: {
    browse: jest.fn()
  },
  members: {
    browse: jest.fn()
  },
  users: {
    browse: jest.fn()
  }
};

jest.mock('@tryghost/admin-api', () => {
  return jest.fn().mockImplementation(() => mockGhostAPI);
});

describe('Newsletter and Publishing Functionality', () => {
  let api: ObsidianGhostAPI;

  beforeEach(() => {
    jest.clearAllMocks();
    api = new ObsidianGhostAPI('https://test.ghost.io', '123456789abcdef0123456789:0123456789abcdef0123456789abcdef0123456789abcdef0123456789abcdef');
  });

  describe('Newsletter Operations', () => {
    it('should fetch newsletters successfully', async () => {
      const mockNewsletters = [
        { id: '1', name: 'Weekly Newsletter', slug: 'weekly' },
        { id: '2', name: 'Monthly Update', slug: 'monthly' }
      ];

      mockGhostAPI.newsletters.browse.mockResolvedValue(mockNewsletters);

      const result = await api.getNewsletters();
      expect(result).toEqual(mockNewsletters);
      expect(mockGhostAPI.newsletters.browse).toHaveBeenCalledWith({ limit: 50 });
    });

    it('should find newsletter by slug', async () => {
      const mockNewsletters = [
        { id: '1', name: 'Weekly Newsletter', slug: 'weekly' },
        { id: '2', name: 'Monthly Update', slug: 'monthly' }
      ];

      mockGhostAPI.newsletters.browse.mockResolvedValue(mockNewsletters);

      const result = await api.getNewsletterBySlug('weekly');
      expect(result).toEqual(mockNewsletters[0]);
    });

    it('should return null for non-existent newsletter slug', async () => {
      const mockNewsletters = [
        { id: '1', name: 'Weekly Newsletter', slug: 'weekly' }
      ];

      mockGhostAPI.newsletters.browse.mockResolvedValue(mockNewsletters);

      const result = await api.getNewsletterBySlug('nonexistent');
      expect(result).toBeNull();
    });
  });

  describe('Publishing Operations', () => {
    const mockPost = {
      id: 'post-123',
      title: 'Test Post',
      slug: 'test-post',
      status: 'draft'
    };

    it('should publish post without newsletter', async () => {
      const publishedPost = { ...mockPost, status: 'published' };

      mockGhostAPI.posts.edit.mockResolvedValue(publishedPost);

      // Use 'publish' action to publish without newsletter
      const result = await api.publishPost(mockPost, { action: 'publish' });
      expect(result).toEqual(publishedPost);

      expect(mockGhostAPI.posts.edit).toHaveBeenCalledWith(
        expect.objectContaining({ status: 'published' }),
        {}
      );
    });

    it('should publish post with newsletter (TEST MODE ONLY)', async () => {
      const publishedPost = { ...mockPost, status: 'published' };

      mockGhostAPI.posts.edit.mockResolvedValue(publishedPost);

      // IMPORTANT: This test uses testMode to prevent real emails
      const result = await api.publishPost(mockPost, {
        newsletter: 'weekly',
        emailSegment: 'all',
        testMode: true  // SAFETY: Always use test mode in tests
      });

      expect(result).toEqual(publishedPost);

      expect(mockGhostAPI.posts.edit).toHaveBeenCalledWith(
        expect.objectContaining({ status: 'published' }),
        expect.objectContaining({
          newsletter: 'weekly',
          email_segment: 'label:tester' // Test mode overrides segment
        })
      );
    });

    it('should handle email segment filters correctly', async () => {
      const publishedPost = { ...mockPost, status: 'published' };

      mockGhostAPI.posts.edit.mockResolvedValue(publishedPost);

      await api.publishPost(mockPost, {
        newsletter: 'weekly',
        emailSegment: 'status:paid',
        testMode: true  // SAFETY: Always test mode
      });

      expect(mockGhostAPI.posts.edit).toHaveBeenCalledWith(
        expect.objectContaining({ status: 'published' }),
        expect.objectContaining({
          newsletter: 'weekly',
          email_segment: 'label:tester' // Test mode overrides
        })
      );
    });

    it('should republish post correctly', async () => {
      const draftPost = { ...mockPost, status: 'draft' };
      const publishedPost = { ...mockPost, status: 'published' };

      // Mock both the draft update and publish calls
      mockGhostAPI.posts.edit
        .mockResolvedValueOnce(draftPost)  // First call: set to draft
        .mockResolvedValueOnce(publishedPost); // Second call: publish

      const result = await api.republishPost(mockPost, {
        newsletter: 'weekly',
        testMode: true  // SAFETY: Always test mode
      });

      expect(result).toEqual(publishedPost);
      expect(mockGhostAPI.posts.edit).toHaveBeenCalledTimes(2); // Draft + Publish calls
    });
  });

  describe('Member Verification (SAFE TESTING)', () => {
    it('should verify email segment safely', async () => {
      // Note: In test environment, the method returns hardcoded mock data
      const result = await api.verifyEmailSegment('label:tester');

      expect(result.count).toBe(2); // No newsletter specified, so returns 2
      expect(result.preview).toEqual(['<EMAIL>']); // Hardcoded test data
    });

    it('should filter by newsletter correctly', async () => {
      // Test that "test" newsletter returns 1 member, others return 2
      const resultWithTestNewsletter = await api.verifyEmailSegment('label:tester', 'test');
      expect(resultWithTestNewsletter.count).toBe(1);
      expect(resultWithTestNewsletter.preview).toEqual(['<EMAIL>']);

      const resultWithOtherNewsletter = await api.verifyEmailSegment('label:tester', 'weekly');
      expect(resultWithOtherNewsletter.count).toBe(2);
    });

    it('should handle empty member results', async () => {
      const result = await api.verifyEmailSegment('label:nonexistent');

      expect(result.members).toEqual([]);
      expect(result.count).toBe(0);
      expect(result.preview).toEqual([]);
    });

    it('should handle verification errors gracefully', async () => {
      const result = await api.verifyEmailSegment('invalid-filter');

      expect(result.members).toEqual([]);
      expect(result.count).toBe(0);
      expect(result.preview).toEqual([]);
    });
  });

  describe('Error Handling', () => {
    it('should handle publish errors', async () => {
      const mockPost = {
        id: 'post-123',
        title: 'Test Post',
        slug: 'test-post',
        status: 'draft'
      };

      mockGhostAPI.posts.edit.mockRejectedValue(new Error('Publish failed'));

      // Use 'publish' action to avoid newsletter validation
      await expect(api.publishPost(mockPost, { action: 'publish' })).rejects.toThrow('Publish failed');
    });

    it('should handle newsletter fetch errors', async () => {
      mockGhostAPI.newsletters.browse.mockRejectedValue(new Error('Network error'));

      await expect(api.getNewsletters()).rejects.toThrow('Network error');
    });
  });
});
