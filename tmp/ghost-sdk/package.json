{"private": true, "repository": {"type": "git", "url": "git+https://github.com/TryGhost/SDK.git"}, "author": "Ghost Foundation", "license": "MIT", "workspaces": ["packages/*"], "eslintIgnore": ["**/node_modules/**"], "scripts": {"dev": "echo \"Implement me!\"", "presetup": "yarn", "setup": "lerna bootstrap", "test": "lerna run test", "lint": "lerna run lint", "preship": "yarn test", "ship": "lerna publish --git-remote ${GHOST_UPSTREAM:-origin}"}, "devDependencies": {"eslint": "8.57.0", "eslint-plugin-ghost": "3.4.3", "eslint-plugin-react": "7.37.5", "lerna": "6.6.2", "mocha": "11.2.2", "should": "13.2.3", "sinon": "21.0.0"}}