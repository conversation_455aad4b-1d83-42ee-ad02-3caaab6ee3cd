{"name": "@tryghost/custom-fonts", "version": "1.0.2", "repository": {"type": "git", "url": "git+https://github.com/TryGhost/SDK.git", "directory": "packages/custom-fonts"}, "author": "Ghost Foundation", "publishConfig": {"access": "public"}, "main": "./build/cjs/index.js", "module": "./build/esm/index.js", "types": "./build/types/index.d.ts", "exports": {".": {"import": "./build/esm/index.js", "require": "./build/cjs/index.js", "types": "./build/types/index.d.ts"}}, "scripts": {"dev": "tsc --watch --preserveWatchOutput --sourceMap", "build": "yarn build:cjs && yarn build:esm && yarn build:types", "build:types": "tsc -p tsconfig.json --emitDeclarationOnly --declaration --declarationDir ./build/types", "build:cjs": "tsc -p tsconfig.json --outDir ./build/cjs --module CommonJS", "build:esm": "tsc -p tsconfig.esm.json --outDir ./build/esm --module ES2020", "build:ts": "yarn build", "prepare": "yarn build", "test:unit": "NODE_ENV=testing c8 --src src --all --check-coverage --100 --reporter text --reporter co<PERSON><PERSON> mocha -r ts-node/register './test/**/*.test.ts'", "test": "yarn test:ts && yarn test:unit", "test:ts": "tsc --noEmit", "lint:code": "eslint src/ --ext .ts --cache", "lint": "yarn lint:code && yarn lint:test", "lint:test": "eslint -c test/.eslintrc.js test/ --ext .ts --cache"}, "files": ["build"], "devDependencies": {"@types/mocha": "10.0.10", "c8": "10.1.3", "mocha": "10.8.2", "sinon": "21.0.0", "ts-node": "10.9.2", "typescript": "5.9.2"}}