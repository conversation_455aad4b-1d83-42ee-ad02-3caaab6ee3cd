{"name": "@tryghost/timezone-data", "version": "0.4.12", "repository": {"type": "git", "url": "git+https://github.com/TryGhost/SDK.git", "directory": "packages/timezone-data"}, "author": "Ghost Foundation", "license": "MIT", "main": "cjs/timezone-data.js", "umd:main": "umd/timezone-data.min.js", "unpkg": "umd/timezone-data.min.js", "module": "es/timezone-data.js", "source": "src/index.ts", "types": "types/index.d.ts", "files": ["LICENSE", "README.md", "cjs/", "src/", "umd/", "es/", "types/"], "scripts": {"dev": "echo \"Implement me!\"", "pretest": "yarn build", "test": "NODE_ENV=testing c8 --all --reporter text --reporter co<PERSON><PERSON> mocha './test/**/*.test.js'", "build": "rollup -c && tsc --declaration --emitDeclarationOnly --declarationDir ./types", "lint": "eslint . --ext .js --cache", "prepare": "NODE_ENV=production yarn build", "posttest": "yarn lint"}, "publishConfig": {"access": "public"}, "devDependencies": {"@rollup/plugin-typescript": "12.1.4", "c8": "10.1.3", "mocha": "11.2.2", "rollup": "2.79.2", "rollup-plugin-commonjs": "10.1.0", "rollup-plugin-node-resolve": "5.2.0", "rollup-plugin-terser": "7.0.2", "should": "13.2.3", "sinon": "21.0.0", "typescript": "5.9.2"}}