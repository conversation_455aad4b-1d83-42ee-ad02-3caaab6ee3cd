{"name": "@tryghost/referrer-parser", "version": "0.1.8", "description": "Simple library for parsing referrer URLs", "type": "module", "main": "./dist/index.cjs", "module": "./dist/index.js", "types": "./dist/index.d.ts", "exports": {".": {"types": "./dist/index.d.ts", "import": "./dist/index.js", "require": "./dist/index.cjs"}}, "files": ["dist", "lib/referrers.json"], "scripts": {"dev": "vite build --watch", "build": "vite build", "preview": "vite preview", "prepublishOnly": "npm run build", "test": "vitest run", "test:watch": "vitest", "test:coverage": "vitest run --coverage", "type-check": "tsc --noEmit", "lint": "eslint *.ts lib/ test/ --ext .ts", "lint:fix": "eslint *.ts lib/ test/ --ext .ts --fix"}, "keywords": ["ghost", "referrer", "attribution", "source", "medium"], "author": "Ghost Foundation", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/TryGhost/SDK.git", "directory": "packages/referrer-parser"}, "bugs": {"url": "https://github.com/TryGhost/SDK/issues"}, "homepage": "https://ghost.org", "devDependencies": {"@types/node": "^22.0.0", "@types/should": "^13.0.0", "@types/sinon": "^17.0.3", "@typescript-eslint/eslint-plugin": "^8.0.0", "@typescript-eslint/parser": "^8.0.0", "@vitest/coverage-v8": "^3.0.0", "eslint": "^8.52.0", "should": "13.2.3", "sinon": "21.0.0", "typescript": "5.9.2", "vite": "^7.0.0", "vite-plugin-dts": "^4.0.0", "vitest": "^3.0.0"}, "engines": {"node": ">=16.0.0"}, "publishConfig": {"access": "public"}}