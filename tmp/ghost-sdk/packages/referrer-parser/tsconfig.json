{"compilerOptions": {"target": "ESNext", "module": "ESNext", "moduleResolution": "<PERSON><PERSON><PERSON>", "lib": ["ESNext"], "strict": true, "esModuleInterop": true, "skipLibCheck": true, "forceConsistentCasingInFileNames": true, "useDefineForClassFields": true, "resolveJsonModule": true, "isolatedModules": true, "declaration": true, "emitDeclarationOnly": true, "outDir": "./dist", "types": ["vitest/globals", "node"]}, "include": ["index.ts", "lib/**/*.ts", "test/**/*.ts", "vite.config.ts", "vitest.config.ts"], "exclude": ["node_modules", "dist"]}