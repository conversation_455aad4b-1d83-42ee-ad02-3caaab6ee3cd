{"name": "@tryghost/helpers", "version": "1.1.97", "repository": {"type": "git", "url": "git+https://github.com/TryGhost/SDK.git", "directory": "packages/helpers"}, "author": "Ghost Foundation", "license": "MIT", "main": "cjs/helpers.js", "umd:main": "umd/helpers.min.js", "unpkg": "umd/helpers.min.js", "module": "es/helpers.js", "source": "lib/index.js", "scripts": {"dev": "echo \"Implement me!\"", "build": "rollup -c", "prepare": "yarn build", "pretest": "yarn build", "test": "NODE_ENV=testing c8 --all --reporter text --reporter co<PERSON><PERSON> mocha './test/**/*.test.js'", "lint": "eslint . --ext .js --cache", "posttest": "yarn lint", "coverage": "NODE_ENV=testing istanbul cover --dir test/coverage _mocha './test/**/*.test.js'"}, "files": ["LICENSE", "README.md", "cjs/", "lib/", "umd/", "es/"], "publishConfig": {"access": "public"}, "devDependencies": {"@babel/core": "7.27.7", "@babel/polyfill": "7.12.1", "c8": "10.1.3", "istanbul": "0.4.5", "mocha": "11.2.2", "rollup": "2.79.2", "rollup-plugin-babel": "4.4.0", "rollup-plugin-commonjs": "10.1.0", "rollup-plugin-node-resolve": "5.2.0", "rollup-plugin-terser": "7.0.2", "should": "13.2.3", "sinon": "21.0.0"}, "dependencies": {"lodash-es": "^4.17.11"}}