{"name": "@tryghost/content-api", "version": "1.12.0", "repository": {"type": "git", "url": "git+https://github.com/TryGhost/SDK.git", "directory": "packages/content-api"}, "author": "Ghost Foundation", "license": "MIT", "main": "cjs/content-api.js", "umd:main": "umd/content-api.min.js", "unpkg": "umd/content-api.min.js", "module": "es/content-api.js", "source": "lib/content-api.js", "files": ["LICENSE", "README.md", "cjs/", "lib/", "umd/", "es/"], "scripts": {"dev": "echo \"Implement me!\"", "pretest": "yarn build", "test": "NODE_ENV=testing c8 --all --reporter text --reporter co<PERSON><PERSON> mocha './test/**/*.test.js'", "build": "rollup -c", "lint": "eslint . --ext .js --cache", "prepare": "NODE_ENV=production yarn build", "posttest": "yarn lint"}, "publishConfig": {"access": "public"}, "devDependencies": {"@babel/core": "7.27.7", "@babel/polyfill": "7.12.1", "@babel/preset-env": "7.27.2", "@rollup/plugin-json": "6.1.0", "c8": "10.1.3", "core-js": "3.45.0", "eslint-plugin-ghost": "3.4.3", "mocha": "11.2.2", "rollup": "2.79.2", "rollup-plugin-babel": "4.4.0", "rollup-plugin-commonjs": "10.1.0", "rollup-plugin-node-resolve": "5.2.0", "rollup-plugin-polyfill-node": "0.12.0", "rollup-plugin-replace": "2.2.0", "rollup-plugin-terser": "7.0.2", "should": "13.2.3", "sinon": "21.0.0"}, "dependencies": {"axios": "^1.0.0"}}