const assert = require('assert/strict');
const {unparse} = require('../index');

describe('unparse', function () {
    it('serializes json to CSV and adds standard members fields with no explicit columns parameter', async function () {
        const json = [{
            email: '<EMAIL>',
            name: '<PERSON>',
            note: 'Early supporter'
        }];

        const result = unparse(json);

        assert.ok(result);

        const expected = `id,email,name,note,subscribed_to_emails,complimentary_plan,stripe_customer_id,created_at,deleted_at,labels,tiers\r\n,<EMAIL>,<PERSON>,Early supporter,,,,,,,`;
        assert.equal(result, expected);
    });

    it('maps the subscribed property to subscribed_to_emails', function () {
        const json = [{
            email: '<EMAIL>',
            subscribed_to_emails: false
        }];

        const columns = [
            'email', 'subscribed'
        ];

        const result = unparse(json, columns);

        const expected = `email,subscribed_to_emails\r\<EMAIL>,false`;

        assert.equal(result, expected);
    });

    it('adds an error column to serialized CSV when present in columns and as a property', function () {
        const json = [{
            email: '<EMAIL>',
            error: 'things went south here!'
        }];
        const columns = [
            'email', 'error'
        ];

        const result = unparse(json, columns);
        const expected = `email,error\r\<EMAIL>,things went south here!`;
        assert.equal(result, expected);
    });

    it('adds an error column automatically even if not present in columns', function () {
        const json = [{
            email: '<EMAIL>',
            error: 'things went south here!'
        }];
        const columns = [
            'email'
        ];

        const result = unparse(json, columns);
        const expected = `email,error\r\<EMAIL>,things went south here!`;
        assert.equal(result, expected);
    });

    it('handles labels as strings and as objects', function () {
        const json = [{
            email: '<EMAIL>',
            labels: 'member-email-label'
        }, {
            email: '<EMAIL>',
            labels: [{
                name: 'second member label'
            }]
        }, {
            email: '<EMAIL>',
            labels: ['banana, avocado']
        }];
        const columns = [
            'email', 'labels'
        ];

        const result = unparse(json, columns);
        const expected = `email,labels\r
<EMAIL>,member-email-label\r
<EMAIL>,second member label\r
<EMAIL>,"banana, avocado"`;
        assert.equal(result, expected);
    });

    it('handles the tiers property serialization', function () {
        const json = [{
            email: '<EMAIL>',
            tiers: [{
                name: 'Bronze Level'
            }]
        }];

        const columns = [
            'email', 'tiers'
        ];

        const result = unparse(json, columns);
        const expected = `email,tiers\r\<EMAIL>,Bronze Level`;
        assert.equal(result, expected);
    });

    it('escapes fields starting with CSV injection characters', async function () {
        const json = [{
            email: '<EMAIL>',
            name: '=1+2',
            note: 'Early supporter'
        }];

        const result = unparse(json);
        assert.ok(result);

        const expected = `id,email,name,note,subscribed_to_emails,complimentary_plan,stripe_customer_id,created_at,deleted_at,labels,tiers\r\n,<EMAIL>,"'=1+2",Early supporter,,,,,,,`;
        assert.equal(result, expected);
    });

    it('escapes fields with CSV injection characters and quotes', async function () {
        const json = [{
            email: '<EMAIL>',
            name: `=1+2'" `,
            note: 'Early supporter'
        }];

        const result = unparse(json);
        assert.ok(result);

        const expected = `id,email,name,note,subscribed_to_emails,complimentary_plan,stripe_customer_id,created_at,deleted_at,labels,tiers\r\n,<EMAIL>,"'=1+2'"" ",Early supporter,,,,,,,`;
        assert.equal(result, expected);
    });
});
