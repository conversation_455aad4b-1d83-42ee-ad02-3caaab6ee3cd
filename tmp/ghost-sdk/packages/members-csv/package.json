{"name": "@tryghost/members-csv", "version": "2.0.3", "repository": {"type": "git", "url": "git+https://github.com/TryGhost/SDK.git", "directory": "packages/members-csv"}, "private": false, "author": "Ghost Foundation", "license": "MIT", "main": "index.js", "publishConfig": {"access": "public"}, "scripts": {"dev": "echo \"Implement me!\"", "test:unit": "NODE_ENV=testing c8 --all --reporter text --reporter co<PERSON><PERSON> -- mocha --reporter dot './test/**/*.test.js'", "test": "yarn test:unit", "lint:code": "eslint *.js lib/ --ext .js --cache", "lint": "yarn lint:code && yarn lint:test", "lint:test": "eslint -c test/.eslintrc.js test/ --ext .js --cache"}, "files": ["index.js", "lib"], "devDependencies": {"c8": "8.0.1", "mocha": "10.8.2", "should": "13.2.3", "sinon": "15.2.0"}, "dependencies": {"fs-extra": "11.3.0", "lodash": "4.17.21", "papaparse": "5.3.2", "pump": "3.0.2"}}