{"name": "@tryghost/config-url-helpers", "version": "1.0.17", "repository": {"type": "git", "url": "git+https://github.com/TryGhost/SDK.git", "directory": "packages/config-url-helpers"}, "author": "Ghost Foundation", "license": "MIT", "main": "index.js", "scripts": {"dev": "echo \"Implement me!\"", "test": "NODE_ENV=testing c8 --all --reporter text --reporter co<PERSON><PERSON> --check-coverage mocha './test/**/*.test.js'", "coverage": "c8 report -r html", "lint": "eslint . --ext .js --cache", "posttest": "yarn lint"}, "files": ["index.js", "lib"], "publishConfig": {"access": "public"}, "devDependencies": {"c8": "10.1.3", "mocha": "11.2.2", "should": "13.2.3", "sinon": "21.0.0"}}