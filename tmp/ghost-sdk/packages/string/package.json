{"name": "@tryghost/string", "version": "0.2.17", "repository": {"type": "git", "url": "git+https://github.com/TryGhost/SDK.git", "directory": "packages/string"}, "author": "Ghost Foundation", "license": "MIT", "main": "lib", "scripts": {"dev": "echo \"Implement me!\"", "test": "NODE_ENV=testing c8 --all --reporter text --reporter co<PERSON><PERSON> mocha './test/**/*.test.js'", "lint": "eslint . --ext .js --cache", "posttest": "yarn lint"}, "files": ["index.js", "lib"], "publishConfig": {"access": "public"}, "devDependencies": {"c8": "10.1.3", "jsdom": "26.1.0", "mocha": "11.2.2", "should": "13.2.3", "sinon": "21.0.0"}, "dependencies": {"unidecode": "^0.1.8"}}