{"name": "@tryghost/admin-api", "version": "1.14.0", "repository": {"type": "git", "url": "git+https://github.com/TryGhost/SDK.git", "directory": "packages/admin-api"}, "author": "Ghost Foundation", "license": "MIT", "main": "index.js", "files": ["LICENSE", "README.md", "cjs/", "lib/", "index.js"], "scripts": {"dev": "echo \"Implement me!\"", "test": "NODE_ENV=testing c8 --all --reporter text --reporter co<PERSON><PERSON> mocha './test/**/*.test.js'", "lint": "eslint . --ext .js --cache", "posttest": "yarn lint"}, "publishConfig": {"access": "public"}, "devDependencies": {"c8": "10.1.3", "mocha": "11.2.2", "should": "13.2.3", "sinon": "21.0.0"}, "dependencies": {"axios": "^1.0.0", "form-data": "^4.0.0", "jsonwebtoken": "^9.0.0"}}