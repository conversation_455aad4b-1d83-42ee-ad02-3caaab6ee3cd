{"name": "@tryghost/html-to-plaintext", "version": "1.0.4", "repository": {"type": "git", "url": "git+https://github.com/TryGhost/SDK.git", "directory": "packages/html-to-plaintext"}, "author": "Ghost Foundation", "private": false, "main": "index.js", "files": ["index.js", "lib"], "publishConfig": {"access": "public"}, "scripts": {"dev": "echo \"Implement me!\"", "test:unit": "NODE_ENV=testing c8 --all --check-coverage --reporter text --reporter co<PERSON><PERSON> -- mocha --reporter dot './test/**/*.test.js'", "test": "yarn test:unit", "lint:code": "eslint *.js lib/ --ext .js --cache", "lint": "yarn lint:code && yarn lint:test", "lint:test": "eslint -c test/.eslintrc.js test/ --ext .js --cache"}, "devDependencies": {"c8": "10.1.3", "mocha": "11.2.2"}, "dependencies": {"html-to-text": "8.2.1", "lodash": "4.17.21"}}