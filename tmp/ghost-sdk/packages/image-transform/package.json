{"name": "@tryghost/image-transform", "version": "1.4.6", "repository": {"type": "git", "url": "git+https://github.com/TryGhost/SDK.git", "directory": "packages/image-transform"}, "author": "Ghost Foundation", "license": "MIT", "main": "index.js", "scripts": {"dev": "echo \"Implement me!\"", "test": "NODE_ENV=testing c8 --all --reporter text --reporter co<PERSON><PERSON> mocha './test/**/*.test.js'", "lint": "eslint . --ext .js --cache", "posttest": "yarn lint"}, "files": ["index.js", "lib"], "publishConfig": {"access": "public"}, "devDependencies": {"c8": "10.1.3", "mocha": "11.2.2", "should": "13.2.3", "sinon": "21.0.0"}, "dependencies": {"@tryghost/errors": "^1.2.26", "fs-extra": "^11.0.0"}, "optionalDependencies": {"sharp": "^0.34.2"}}