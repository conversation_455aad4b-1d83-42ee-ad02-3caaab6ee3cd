{"name": "@tryghost/helpers-gatsby", "version": "2.0.28", "repository": {"type": "git", "url": "git+https://github.com/TryGhost/SDK.git", "directory": "packages/helpers-gatsby"}, "author": "Ghost Foundation", "license": "MIT", "main": "./lib/index.js", "scripts": {"dev": "echo \"Implement me!\"", "test": "NODE_ENV=testing c8 --all --reporter text --reporter co<PERSON><PERSON> mocha './test/**/*.test.js'", "lint": "eslint . --ext .js --cache", "posttest": "yarn lint", "clean": "<PERSON><PERSON><PERSON> lib", "build": "yarn clean && babel src --out-dir lib --presets=@babel/preset-env,@babel/preset-react", "prepack": "yarn build"}, "files": ["/lib"], "eslintIgnore": ["lib/**"], "publishConfig": {"access": "public"}, "devDependencies": {"@babel/cli": "7.27.2", "@babel/core": "7.27.7", "@babel/preset-env": "7.27.2", "@babel/preset-react": "7.27.1", "c8": "10.1.3", "mocha": "11.2.2", "react": "18.3.1", "rimraf": "6.0.1", "should": "13.2.3", "sinon": "21.0.0"}, "dependencies": {"@gatsbyjs/reach-router": "1.3.9", "@tryghost/helpers": "^1.1.97", "gatsby-link": "4.17.0", "prop-types": "^15.6.2"}, "peerDependencies": {"react": "^18.1.0"}}