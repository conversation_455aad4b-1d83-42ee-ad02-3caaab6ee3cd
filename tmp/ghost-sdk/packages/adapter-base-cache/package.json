{"name": "@tryghost/adapter-base-cache", "version": "0.1.17", "repository": {"type": "git", "url": "git+https://github.com/TryGhost/SDK.git", "directory": "packages/adapter-base-cache"}, "author": "Ghost Foundation", "license": "MIT", "main": "index.js", "scripts": {"test:unit": "NODE_ENV=testing c8 --all --check-coverage --reporter text --reporter co<PERSON><PERSON> mocha './test/**/*.test.js'", "test": "yarn test:unit", "lint:code": "eslint *.js lib/ --ext .js --cache", "lint": "yarn lint:code && yarn lint:test", "lint:test": "eslint -c test/.eslintrc.js test/ --ext .js --cache"}, "files": ["index.js", "lib"], "publishConfig": {"access": "public"}, "devDependencies": {"c8": "10.1.3", "mocha": "11.2.2", "sinon": "21.0.0"}}