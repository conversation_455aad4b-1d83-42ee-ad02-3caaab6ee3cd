{"version": 3, "file": "color-utils.js", "sources": ["../../../node_modules/color-name/index.js", "../../../node_modules/simple-swizzle/node_modules/is-arrayish/index.js", "../../../node_modules/simple-swizzle/index.js", "../../../node_modules/color-string/index.js", "../../../node_modules/color-convert/node_modules/color-name/index.js", "../../../node_modules/color-convert/conversions.js", "../../../node_modules/color-convert/route.js", "../../../node_modules/color-convert/index.js", "../../../node_modules/color/index.js", "../src/color-utils.ts"], "sourcesContent": ["'use strict'\r\n\r\nmodule.exports = {\r\n\t\"aliceblue\": [240, 248, 255],\r\n\t\"antiquewhite\": [250, 235, 215],\r\n\t\"aqua\": [0, 255, 255],\r\n\t\"aquamarine\": [127, 255, 212],\r\n\t\"azure\": [240, 255, 255],\r\n\t\"beige\": [245, 245, 220],\r\n\t\"bisque\": [255, 228, 196],\r\n\t\"black\": [0, 0, 0],\r\n\t\"blanchedalmond\": [255, 235, 205],\r\n\t\"blue\": [0, 0, 255],\r\n\t\"blueviolet\": [138, 43, 226],\r\n\t\"brown\": [165, 42, 42],\r\n\t\"burlywood\": [222, 184, 135],\r\n\t\"cadetblue\": [95, 158, 160],\r\n\t\"chartreuse\": [127, 255, 0],\r\n\t\"chocolate\": [210, 105, 30],\r\n\t\"coral\": [255, 127, 80],\r\n\t\"cornflowerblue\": [100, 149, 237],\r\n\t\"cornsilk\": [255, 248, 220],\r\n\t\"crimson\": [220, 20, 60],\r\n\t\"cyan\": [0, 255, 255],\r\n\t\"darkblue\": [0, 0, 139],\r\n\t\"darkcyan\": [0, 139, 139],\r\n\t\"darkgoldenrod\": [184, 134, 11],\r\n\t\"darkgray\": [169, 169, 169],\r\n\t\"darkgreen\": [0, 100, 0],\r\n\t\"darkgrey\": [169, 169, 169],\r\n\t\"darkkhaki\": [189, 183, 107],\r\n\t\"darkmagenta\": [139, 0, 139],\r\n\t\"darkolivegreen\": [85, 107, 47],\r\n\t\"darkorange\": [255, 140, 0],\r\n\t\"darkorchid\": [153, 50, 204],\r\n\t\"darkred\": [139, 0, 0],\r\n\t\"darksalmon\": [233, 150, 122],\r\n\t\"darkseagreen\": [143, 188, 143],\r\n\t\"darkslateblue\": [72, 61, 139],\r\n\t\"darkslategray\": [47, 79, 79],\r\n\t\"darkslategrey\": [47, 79, 79],\r\n\t\"darkturquoise\": [0, 206, 209],\r\n\t\"darkviolet\": [148, 0, 211],\r\n\t\"deeppink\": [255, 20, 147],\r\n\t\"deepskyblue\": [0, 191, 255],\r\n\t\"dimgray\": [105, 105, 105],\r\n\t\"dimgrey\": [105, 105, 105],\r\n\t\"dodgerblue\": [30, 144, 255],\r\n\t\"firebrick\": [178, 34, 34],\r\n\t\"floralwhite\": [255, 250, 240],\r\n\t\"forestgreen\": [34, 139, 34],\r\n\t\"fuchsia\": [255, 0, 255],\r\n\t\"gainsboro\": [220, 220, 220],\r\n\t\"ghostwhite\": [248, 248, 255],\r\n\t\"gold\": [255, 215, 0],\r\n\t\"goldenrod\": [218, 165, 32],\r\n\t\"gray\": [128, 128, 128],\r\n\t\"green\": [0, 128, 0],\r\n\t\"greenyellow\": [173, 255, 47],\r\n\t\"grey\": [128, 128, 128],\r\n\t\"honeydew\": [240, 255, 240],\r\n\t\"hotpink\": [255, 105, 180],\r\n\t\"indianred\": [205, 92, 92],\r\n\t\"indigo\": [75, 0, 130],\r\n\t\"ivory\": [255, 255, 240],\r\n\t\"khaki\": [240, 230, 140],\r\n\t\"lavender\": [230, 230, 250],\r\n\t\"lavenderblush\": [255, 240, 245],\r\n\t\"lawngreen\": [124, 252, 0],\r\n\t\"lemonchiffon\": [255, 250, 205],\r\n\t\"lightblue\": [173, 216, 230],\r\n\t\"lightcoral\": [240, 128, 128],\r\n\t\"lightcyan\": [224, 255, 255],\r\n\t\"lightgoldenrodyellow\": [250, 250, 210],\r\n\t\"lightgray\": [211, 211, 211],\r\n\t\"lightgreen\": [144, 238, 144],\r\n\t\"lightgrey\": [211, 211, 211],\r\n\t\"lightpink\": [255, 182, 193],\r\n\t\"lightsalmon\": [255, 160, 122],\r\n\t\"lightseagreen\": [32, 178, 170],\r\n\t\"lightskyblue\": [135, 206, 250],\r\n\t\"lightslategray\": [119, 136, 153],\r\n\t\"lightslategrey\": [119, 136, 153],\r\n\t\"lightsteelblue\": [176, 196, 222],\r\n\t\"lightyellow\": [255, 255, 224],\r\n\t\"lime\": [0, 255, 0],\r\n\t\"limegreen\": [50, 205, 50],\r\n\t\"linen\": [250, 240, 230],\r\n\t\"magenta\": [255, 0, 255],\r\n\t\"maroon\": [128, 0, 0],\r\n\t\"mediumaquamarine\": [102, 205, 170],\r\n\t\"mediumblue\": [0, 0, 205],\r\n\t\"mediumorchid\": [186, 85, 211],\r\n\t\"mediumpurple\": [147, 112, 219],\r\n\t\"mediumseagreen\": [60, 179, 113],\r\n\t\"mediumslateblue\": [123, 104, 238],\r\n\t\"mediumspringgreen\": [0, 250, 154],\r\n\t\"mediumturquoise\": [72, 209, 204],\r\n\t\"mediumvioletred\": [199, 21, 133],\r\n\t\"midnightblue\": [25, 25, 112],\r\n\t\"mintcream\": [245, 255, 250],\r\n\t\"mistyrose\": [255, 228, 225],\r\n\t\"moccasin\": [255, 228, 181],\r\n\t\"navajowhite\": [255, 222, 173],\r\n\t\"navy\": [0, 0, 128],\r\n\t\"oldlace\": [253, 245, 230],\r\n\t\"olive\": [128, 128, 0],\r\n\t\"olivedrab\": [107, 142, 35],\r\n\t\"orange\": [255, 165, 0],\r\n\t\"orangered\": [255, 69, 0],\r\n\t\"orchid\": [218, 112, 214],\r\n\t\"palegoldenrod\": [238, 232, 170],\r\n\t\"palegreen\": [152, 251, 152],\r\n\t\"paleturquoise\": [175, 238, 238],\r\n\t\"palevioletred\": [219, 112, 147],\r\n\t\"papayawhip\": [255, 239, 213],\r\n\t\"peachpuff\": [255, 218, 185],\r\n\t\"peru\": [205, 133, 63],\r\n\t\"pink\": [255, 192, 203],\r\n\t\"plum\": [221, 160, 221],\r\n\t\"powderblue\": [176, 224, 230],\r\n\t\"purple\": [128, 0, 128],\r\n\t\"rebeccapurple\": [102, 51, 153],\r\n\t\"red\": [255, 0, 0],\r\n\t\"rosybrown\": [188, 143, 143],\r\n\t\"royalblue\": [65, 105, 225],\r\n\t\"saddlebrown\": [139, 69, 19],\r\n\t\"salmon\": [250, 128, 114],\r\n\t\"sandybrown\": [244, 164, 96],\r\n\t\"seagreen\": [46, 139, 87],\r\n\t\"seashell\": [255, 245, 238],\r\n\t\"sienna\": [160, 82, 45],\r\n\t\"silver\": [192, 192, 192],\r\n\t\"skyblue\": [135, 206, 235],\r\n\t\"slateblue\": [106, 90, 205],\r\n\t\"slategray\": [112, 128, 144],\r\n\t\"slategrey\": [112, 128, 144],\r\n\t\"snow\": [255, 250, 250],\r\n\t\"springgreen\": [0, 255, 127],\r\n\t\"steelblue\": [70, 130, 180],\r\n\t\"tan\": [210, 180, 140],\r\n\t\"teal\": [0, 128, 128],\r\n\t\"thistle\": [216, 191, 216],\r\n\t\"tomato\": [255, 99, 71],\r\n\t\"turquoise\": [64, 224, 208],\r\n\t\"violet\": [238, 130, 238],\r\n\t\"wheat\": [245, 222, 179],\r\n\t\"white\": [255, 255, 255],\r\n\t\"whitesmoke\": [245, 245, 245],\r\n\t\"yellow\": [255, 255, 0],\r\n\t\"yellowgreen\": [154, 205, 50]\r\n};\r\n", "module.exports = function isArrayish(obj) {\n\tif (!obj || typeof obj === 'string') {\n\t\treturn false;\n\t}\n\n\treturn obj instanceof Array || Array.isArray(obj) ||\n\t\t(obj.length >= 0 && (obj.splice instanceof Function ||\n\t\t\t(Object.getOwnPropertyDescriptor(obj, (obj.length - 1)) && obj.constructor.name !== 'String')));\n};\n", "'use strict';\n\nvar isArrayish = require('is-arrayish');\n\nvar concat = Array.prototype.concat;\nvar slice = Array.prototype.slice;\n\nvar swizzle = module.exports = function swizzle(args) {\n\tvar results = [];\n\n\tfor (var i = 0, len = args.length; i < len; i++) {\n\t\tvar arg = args[i];\n\n\t\tif (isArrayish(arg)) {\n\t\t\t// http://jsperf.com/javascript-array-concat-vs-push/98\n\t\t\tresults = concat.call(results, slice.call(arg));\n\t\t} else {\n\t\t\tresults.push(arg);\n\t\t}\n\t}\n\n\treturn results;\n};\n\nswizzle.wrap = function (fn) {\n\treturn function () {\n\t\treturn fn(swizzle(arguments));\n\t};\n};\n", "/* MIT license */\nvar colorNames = require('color-name');\nvar swizzle = require('simple-swizzle');\nvar hasOwnProperty = Object.hasOwnProperty;\n\nvar reverseNames = Object.create(null);\n\n// create a list of reverse color names\nfor (var name in colorNames) {\n\tif (hasOwnProperty.call(colorNames, name)) {\n\t\treverseNames[colorNames[name]] = name;\n\t}\n}\n\nvar cs = module.exports = {\n\tto: {},\n\tget: {}\n};\n\ncs.get = function (string) {\n\tvar prefix = string.substring(0, 3).toLowerCase();\n\tvar val;\n\tvar model;\n\tswitch (prefix) {\n\t\tcase 'hsl':\n\t\t\tval = cs.get.hsl(string);\n\t\t\tmodel = 'hsl';\n\t\t\tbreak;\n\t\tcase 'hwb':\n\t\t\tval = cs.get.hwb(string);\n\t\t\tmodel = 'hwb';\n\t\t\tbreak;\n\t\tdefault:\n\t\t\tval = cs.get.rgb(string);\n\t\t\tmodel = 'rgb';\n\t\t\tbreak;\n\t}\n\n\tif (!val) {\n\t\treturn null;\n\t}\n\n\treturn {model: model, value: val};\n};\n\ncs.get.rgb = function (string) {\n\tif (!string) {\n\t\treturn null;\n\t}\n\n\tvar abbr = /^#([a-f0-9]{3,4})$/i;\n\tvar hex = /^#([a-f0-9]{6})([a-f0-9]{2})?$/i;\n\tvar rgba = /^rgba?\\(\\s*([+-]?\\d+)(?=[\\s,])\\s*(?:,\\s*)?([+-]?\\d+)(?=[\\s,])\\s*(?:,\\s*)?([+-]?\\d+)\\s*(?:[,|\\/]\\s*([+-]?[\\d\\.]+)(%?)\\s*)?\\)$/;\n\tvar per = /^rgba?\\(\\s*([+-]?[\\d\\.]+)\\%\\s*,?\\s*([+-]?[\\d\\.]+)\\%\\s*,?\\s*([+-]?[\\d\\.]+)\\%\\s*(?:[,|\\/]\\s*([+-]?[\\d\\.]+)(%?)\\s*)?\\)$/;\n\tvar keyword = /^(\\w+)$/;\n\n\tvar rgb = [0, 0, 0, 1];\n\tvar match;\n\tvar i;\n\tvar hexAlpha;\n\n\tif (match = string.match(hex)) {\n\t\thexAlpha = match[2];\n\t\tmatch = match[1];\n\n\t\tfor (i = 0; i < 3; i++) {\n\t\t\t// https://jsperf.com/slice-vs-substr-vs-substring-methods-long-string/19\n\t\t\tvar i2 = i * 2;\n\t\t\trgb[i] = parseInt(match.slice(i2, i2 + 2), 16);\n\t\t}\n\n\t\tif (hexAlpha) {\n\t\t\trgb[3] = parseInt(hexAlpha, 16) / 255;\n\t\t}\n\t} else if (match = string.match(abbr)) {\n\t\tmatch = match[1];\n\t\thexAlpha = match[3];\n\n\t\tfor (i = 0; i < 3; i++) {\n\t\t\trgb[i] = parseInt(match[i] + match[i], 16);\n\t\t}\n\n\t\tif (hexAlpha) {\n\t\t\trgb[3] = parseInt(hexAlpha + hexAlpha, 16) / 255;\n\t\t}\n\t} else if (match = string.match(rgba)) {\n\t\tfor (i = 0; i < 3; i++) {\n\t\t\trgb[i] = parseInt(match[i + 1], 0);\n\t\t}\n\n\t\tif (match[4]) {\n\t\t\tif (match[5]) {\n\t\t\t\trgb[3] = parseFloat(match[4]) * 0.01;\n\t\t\t} else {\n\t\t\t\trgb[3] = parseFloat(match[4]);\n\t\t\t}\n\t\t}\n\t} else if (match = string.match(per)) {\n\t\tfor (i = 0; i < 3; i++) {\n\t\t\trgb[i] = Math.round(parseFloat(match[i + 1]) * 2.55);\n\t\t}\n\n\t\tif (match[4]) {\n\t\t\tif (match[5]) {\n\t\t\t\trgb[3] = parseFloat(match[4]) * 0.01;\n\t\t\t} else {\n\t\t\t\trgb[3] = parseFloat(match[4]);\n\t\t\t}\n\t\t}\n\t} else if (match = string.match(keyword)) {\n\t\tif (match[1] === 'transparent') {\n\t\t\treturn [0, 0, 0, 0];\n\t\t}\n\n\t\tif (!hasOwnProperty.call(colorNames, match[1])) {\n\t\t\treturn null;\n\t\t}\n\n\t\trgb = colorNames[match[1]];\n\t\trgb[3] = 1;\n\n\t\treturn rgb;\n\t} else {\n\t\treturn null;\n\t}\n\n\tfor (i = 0; i < 3; i++) {\n\t\trgb[i] = clamp(rgb[i], 0, 255);\n\t}\n\trgb[3] = clamp(rgb[3], 0, 1);\n\n\treturn rgb;\n};\n\ncs.get.hsl = function (string) {\n\tif (!string) {\n\t\treturn null;\n\t}\n\n\tvar hsl = /^hsla?\\(\\s*([+-]?(?:\\d{0,3}\\.)?\\d+)(?:deg)?\\s*,?\\s*([+-]?[\\d\\.]+)%\\s*,?\\s*([+-]?[\\d\\.]+)%\\s*(?:[,|\\/]\\s*([+-]?(?=\\.\\d|\\d)(?:0|[1-9]\\d*)?(?:\\.\\d*)?(?:[eE][+-]?\\d+)?)\\s*)?\\)$/;\n\tvar match = string.match(hsl);\n\n\tif (match) {\n\t\tvar alpha = parseFloat(match[4]);\n\t\tvar h = ((parseFloat(match[1]) % 360) + 360) % 360;\n\t\tvar s = clamp(parseFloat(match[2]), 0, 100);\n\t\tvar l = clamp(parseFloat(match[3]), 0, 100);\n\t\tvar a = clamp(isNaN(alpha) ? 1 : alpha, 0, 1);\n\n\t\treturn [h, s, l, a];\n\t}\n\n\treturn null;\n};\n\ncs.get.hwb = function (string) {\n\tif (!string) {\n\t\treturn null;\n\t}\n\n\tvar hwb = /^hwb\\(\\s*([+-]?\\d{0,3}(?:\\.\\d+)?)(?:deg)?\\s*,\\s*([+-]?[\\d\\.]+)%\\s*,\\s*([+-]?[\\d\\.]+)%\\s*(?:,\\s*([+-]?(?=\\.\\d|\\d)(?:0|[1-9]\\d*)?(?:\\.\\d*)?(?:[eE][+-]?\\d+)?)\\s*)?\\)$/;\n\tvar match = string.match(hwb);\n\n\tif (match) {\n\t\tvar alpha = parseFloat(match[4]);\n\t\tvar h = ((parseFloat(match[1]) % 360) + 360) % 360;\n\t\tvar w = clamp(parseFloat(match[2]), 0, 100);\n\t\tvar b = clamp(parseFloat(match[3]), 0, 100);\n\t\tvar a = clamp(isNaN(alpha) ? 1 : alpha, 0, 1);\n\t\treturn [h, w, b, a];\n\t}\n\n\treturn null;\n};\n\ncs.to.hex = function () {\n\tvar rgba = swizzle(arguments);\n\n\treturn (\n\t\t'#' +\n\t\thexDouble(rgba[0]) +\n\t\thexDouble(rgba[1]) +\n\t\thexDouble(rgba[2]) +\n\t\t(rgba[3] < 1\n\t\t\t? (hexDouble(Math.round(rgba[3] * 255)))\n\t\t\t: '')\n\t);\n};\n\ncs.to.rgb = function () {\n\tvar rgba = swizzle(arguments);\n\n\treturn rgba.length < 4 || rgba[3] === 1\n\t\t? 'rgb(' + Math.round(rgba[0]) + ', ' + Math.round(rgba[1]) + ', ' + Math.round(rgba[2]) + ')'\n\t\t: 'rgba(' + Math.round(rgba[0]) + ', ' + Math.round(rgba[1]) + ', ' + Math.round(rgba[2]) + ', ' + rgba[3] + ')';\n};\n\ncs.to.rgb.percent = function () {\n\tvar rgba = swizzle(arguments);\n\n\tvar r = Math.round(rgba[0] / 255 * 100);\n\tvar g = Math.round(rgba[1] / 255 * 100);\n\tvar b = Math.round(rgba[2] / 255 * 100);\n\n\treturn rgba.length < 4 || rgba[3] === 1\n\t\t? 'rgb(' + r + '%, ' + g + '%, ' + b + '%)'\n\t\t: 'rgba(' + r + '%, ' + g + '%, ' + b + '%, ' + rgba[3] + ')';\n};\n\ncs.to.hsl = function () {\n\tvar hsla = swizzle(arguments);\n\treturn hsla.length < 4 || hsla[3] === 1\n\t\t? 'hsl(' + hsla[0] + ', ' + hsla[1] + '%, ' + hsla[2] + '%)'\n\t\t: 'hsla(' + hsla[0] + ', ' + hsla[1] + '%, ' + hsla[2] + '%, ' + hsla[3] + ')';\n};\n\n// hwb is a bit different than rgb(a) & hsl(a) since there is no alpha specific syntax\n// (hwb have alpha optional & 1 is default value)\ncs.to.hwb = function () {\n\tvar hwba = swizzle(arguments);\n\n\tvar a = '';\n\tif (hwba.length >= 4 && hwba[3] !== 1) {\n\t\ta = ', ' + hwba[3];\n\t}\n\n\treturn 'hwb(' + hwba[0] + ', ' + hwba[1] + '%, ' + hwba[2] + '%' + a + ')';\n};\n\ncs.to.keyword = function (rgb) {\n\treturn reverseNames[rgb.slice(0, 3)];\n};\n\n// helpers\nfunction clamp(num, min, max) {\n\treturn Math.min(Math.max(min, num), max);\n}\n\nfunction hexDouble(num) {\n\tvar str = Math.round(num).toString(16).toUpperCase();\n\treturn (str.length < 2) ? '0' + str : str;\n}\n", "'use strict'\r\n\r\nmodule.exports = {\r\n\t\"aliceblue\": [240, 248, 255],\r\n\t\"antiquewhite\": [250, 235, 215],\r\n\t\"aqua\": [0, 255, 255],\r\n\t\"aquamarine\": [127, 255, 212],\r\n\t\"azure\": [240, 255, 255],\r\n\t\"beige\": [245, 245, 220],\r\n\t\"bisque\": [255, 228, 196],\r\n\t\"black\": [0, 0, 0],\r\n\t\"blanchedalmond\": [255, 235, 205],\r\n\t\"blue\": [0, 0, 255],\r\n\t\"blueviolet\": [138, 43, 226],\r\n\t\"brown\": [165, 42, 42],\r\n\t\"burlywood\": [222, 184, 135],\r\n\t\"cadetblue\": [95, 158, 160],\r\n\t\"chartreuse\": [127, 255, 0],\r\n\t\"chocolate\": [210, 105, 30],\r\n\t\"coral\": [255, 127, 80],\r\n\t\"cornflowerblue\": [100, 149, 237],\r\n\t\"cornsilk\": [255, 248, 220],\r\n\t\"crimson\": [220, 20, 60],\r\n\t\"cyan\": [0, 255, 255],\r\n\t\"darkblue\": [0, 0, 139],\r\n\t\"darkcyan\": [0, 139, 139],\r\n\t\"darkgoldenrod\": [184, 134, 11],\r\n\t\"darkgray\": [169, 169, 169],\r\n\t\"darkgreen\": [0, 100, 0],\r\n\t\"darkgrey\": [169, 169, 169],\r\n\t\"darkkhaki\": [189, 183, 107],\r\n\t\"darkmagenta\": [139, 0, 139],\r\n\t\"darkolivegreen\": [85, 107, 47],\r\n\t\"darkorange\": [255, 140, 0],\r\n\t\"darkorchid\": [153, 50, 204],\r\n\t\"darkred\": [139, 0, 0],\r\n\t\"darksalmon\": [233, 150, 122],\r\n\t\"darkseagreen\": [143, 188, 143],\r\n\t\"darkslateblue\": [72, 61, 139],\r\n\t\"darkslategray\": [47, 79, 79],\r\n\t\"darkslategrey\": [47, 79, 79],\r\n\t\"darkturquoise\": [0, 206, 209],\r\n\t\"darkviolet\": [148, 0, 211],\r\n\t\"deeppink\": [255, 20, 147],\r\n\t\"deepskyblue\": [0, 191, 255],\r\n\t\"dimgray\": [105, 105, 105],\r\n\t\"dimgrey\": [105, 105, 105],\r\n\t\"dodgerblue\": [30, 144, 255],\r\n\t\"firebrick\": [178, 34, 34],\r\n\t\"floralwhite\": [255, 250, 240],\r\n\t\"forestgreen\": [34, 139, 34],\r\n\t\"fuchsia\": [255, 0, 255],\r\n\t\"gainsboro\": [220, 220, 220],\r\n\t\"ghostwhite\": [248, 248, 255],\r\n\t\"gold\": [255, 215, 0],\r\n\t\"goldenrod\": [218, 165, 32],\r\n\t\"gray\": [128, 128, 128],\r\n\t\"green\": [0, 128, 0],\r\n\t\"greenyellow\": [173, 255, 47],\r\n\t\"grey\": [128, 128, 128],\r\n\t\"honeydew\": [240, 255, 240],\r\n\t\"hotpink\": [255, 105, 180],\r\n\t\"indianred\": [205, 92, 92],\r\n\t\"indigo\": [75, 0, 130],\r\n\t\"ivory\": [255, 255, 240],\r\n\t\"khaki\": [240, 230, 140],\r\n\t\"lavender\": [230, 230, 250],\r\n\t\"lavenderblush\": [255, 240, 245],\r\n\t\"lawngreen\": [124, 252, 0],\r\n\t\"lemonchiffon\": [255, 250, 205],\r\n\t\"lightblue\": [173, 216, 230],\r\n\t\"lightcoral\": [240, 128, 128],\r\n\t\"lightcyan\": [224, 255, 255],\r\n\t\"lightgoldenrodyellow\": [250, 250, 210],\r\n\t\"lightgray\": [211, 211, 211],\r\n\t\"lightgreen\": [144, 238, 144],\r\n\t\"lightgrey\": [211, 211, 211],\r\n\t\"lightpink\": [255, 182, 193],\r\n\t\"lightsalmon\": [255, 160, 122],\r\n\t\"lightseagreen\": [32, 178, 170],\r\n\t\"lightskyblue\": [135, 206, 250],\r\n\t\"lightslategray\": [119, 136, 153],\r\n\t\"lightslategrey\": [119, 136, 153],\r\n\t\"lightsteelblue\": [176, 196, 222],\r\n\t\"lightyellow\": [255, 255, 224],\r\n\t\"lime\": [0, 255, 0],\r\n\t\"limegreen\": [50, 205, 50],\r\n\t\"linen\": [250, 240, 230],\r\n\t\"magenta\": [255, 0, 255],\r\n\t\"maroon\": [128, 0, 0],\r\n\t\"mediumaquamarine\": [102, 205, 170],\r\n\t\"mediumblue\": [0, 0, 205],\r\n\t\"mediumorchid\": [186, 85, 211],\r\n\t\"mediumpurple\": [147, 112, 219],\r\n\t\"mediumseagreen\": [60, 179, 113],\r\n\t\"mediumslateblue\": [123, 104, 238],\r\n\t\"mediumspringgreen\": [0, 250, 154],\r\n\t\"mediumturquoise\": [72, 209, 204],\r\n\t\"mediumvioletred\": [199, 21, 133],\r\n\t\"midnightblue\": [25, 25, 112],\r\n\t\"mintcream\": [245, 255, 250],\r\n\t\"mistyrose\": [255, 228, 225],\r\n\t\"moccasin\": [255, 228, 181],\r\n\t\"navajowhite\": [255, 222, 173],\r\n\t\"navy\": [0, 0, 128],\r\n\t\"oldlace\": [253, 245, 230],\r\n\t\"olive\": [128, 128, 0],\r\n\t\"olivedrab\": [107, 142, 35],\r\n\t\"orange\": [255, 165, 0],\r\n\t\"orangered\": [255, 69, 0],\r\n\t\"orchid\": [218, 112, 214],\r\n\t\"palegoldenrod\": [238, 232, 170],\r\n\t\"palegreen\": [152, 251, 152],\r\n\t\"paleturquoise\": [175, 238, 238],\r\n\t\"palevioletred\": [219, 112, 147],\r\n\t\"papayawhip\": [255, 239, 213],\r\n\t\"peachpuff\": [255, 218, 185],\r\n\t\"peru\": [205, 133, 63],\r\n\t\"pink\": [255, 192, 203],\r\n\t\"plum\": [221, 160, 221],\r\n\t\"powderblue\": [176, 224, 230],\r\n\t\"purple\": [128, 0, 128],\r\n\t\"rebeccapurple\": [102, 51, 153],\r\n\t\"red\": [255, 0, 0],\r\n\t\"rosybrown\": [188, 143, 143],\r\n\t\"royalblue\": [65, 105, 225],\r\n\t\"saddlebrown\": [139, 69, 19],\r\n\t\"salmon\": [250, 128, 114],\r\n\t\"sandybrown\": [244, 164, 96],\r\n\t\"seagreen\": [46, 139, 87],\r\n\t\"seashell\": [255, 245, 238],\r\n\t\"sienna\": [160, 82, 45],\r\n\t\"silver\": [192, 192, 192],\r\n\t\"skyblue\": [135, 206, 235],\r\n\t\"slateblue\": [106, 90, 205],\r\n\t\"slategray\": [112, 128, 144],\r\n\t\"slategrey\": [112, 128, 144],\r\n\t\"snow\": [255, 250, 250],\r\n\t\"springgreen\": [0, 255, 127],\r\n\t\"steelblue\": [70, 130, 180],\r\n\t\"tan\": [210, 180, 140],\r\n\t\"teal\": [0, 128, 128],\r\n\t\"thistle\": [216, 191, 216],\r\n\t\"tomato\": [255, 99, 71],\r\n\t\"turquoise\": [64, 224, 208],\r\n\t\"violet\": [238, 130, 238],\r\n\t\"wheat\": [245, 222, 179],\r\n\t\"white\": [255, 255, 255],\r\n\t\"whitesmoke\": [245, 245, 245],\r\n\t\"yellow\": [255, 255, 0],\r\n\t\"yellowgreen\": [154, 205, 50]\r\n};\r\n", "/* MIT license */\nvar cssKeywords = require('color-name');\n\n// NOTE: conversions should only return primitive values (i.e. arrays, or\n//       values that give correct `typeof` results).\n//       do not use box values types (i.e. Number(), String(), etc.)\n\nvar reverseKeywords = {};\nfor (var key in cssKeywords) {\n\tif (cssKeywords.hasOwnProperty(key)) {\n\t\treverseKeywords[cssKeywords[key]] = key;\n\t}\n}\n\nvar convert = module.exports = {\n\trgb: {channels: 3, labels: 'rgb'},\n\thsl: {channels: 3, labels: 'hsl'},\n\thsv: {channels: 3, labels: 'hsv'},\n\thwb: {channels: 3, labels: 'hwb'},\n\tcmyk: {channels: 4, labels: 'cmyk'},\n\txyz: {channels: 3, labels: 'xyz'},\n\tlab: {channels: 3, labels: 'lab'},\n\tlch: {channels: 3, labels: 'lch'},\n\thex: {channels: 1, labels: ['hex']},\n\tkeyword: {channels: 1, labels: ['keyword']},\n\tansi16: {channels: 1, labels: ['ansi16']},\n\tansi256: {channels: 1, labels: ['ansi256']},\n\thcg: {channels: 3, labels: ['h', 'c', 'g']},\n\tapple: {channels: 3, labels: ['r16', 'g16', 'b16']},\n\tgray: {channels: 1, labels: ['gray']}\n};\n\n// hide .channels and .labels properties\nfor (var model in convert) {\n\tif (convert.hasOwnProperty(model)) {\n\t\tif (!('channels' in convert[model])) {\n\t\t\tthrow new Error('missing channels property: ' + model);\n\t\t}\n\n\t\tif (!('labels' in convert[model])) {\n\t\t\tthrow new Error('missing channel labels property: ' + model);\n\t\t}\n\n\t\tif (convert[model].labels.length !== convert[model].channels) {\n\t\t\tthrow new Error('channel and label counts mismatch: ' + model);\n\t\t}\n\n\t\tvar channels = convert[model].channels;\n\t\tvar labels = convert[model].labels;\n\t\tdelete convert[model].channels;\n\t\tdelete convert[model].labels;\n\t\tObject.defineProperty(convert[model], 'channels', {value: channels});\n\t\tObject.defineProperty(convert[model], 'labels', {value: labels});\n\t}\n}\n\nconvert.rgb.hsl = function (rgb) {\n\tvar r = rgb[0] / 255;\n\tvar g = rgb[1] / 255;\n\tvar b = rgb[2] / 255;\n\tvar min = Math.min(r, g, b);\n\tvar max = Math.max(r, g, b);\n\tvar delta = max - min;\n\tvar h;\n\tvar s;\n\tvar l;\n\n\tif (max === min) {\n\t\th = 0;\n\t} else if (r === max) {\n\t\th = (g - b) / delta;\n\t} else if (g === max) {\n\t\th = 2 + (b - r) / delta;\n\t} else if (b === max) {\n\t\th = 4 + (r - g) / delta;\n\t}\n\n\th = Math.min(h * 60, 360);\n\n\tif (h < 0) {\n\t\th += 360;\n\t}\n\n\tl = (min + max) / 2;\n\n\tif (max === min) {\n\t\ts = 0;\n\t} else if (l <= 0.5) {\n\t\ts = delta / (max + min);\n\t} else {\n\t\ts = delta / (2 - max - min);\n\t}\n\n\treturn [h, s * 100, l * 100];\n};\n\nconvert.rgb.hsv = function (rgb) {\n\tvar rdif;\n\tvar gdif;\n\tvar bdif;\n\tvar h;\n\tvar s;\n\n\tvar r = rgb[0] / 255;\n\tvar g = rgb[1] / 255;\n\tvar b = rgb[2] / 255;\n\tvar v = Math.max(r, g, b);\n\tvar diff = v - Math.min(r, g, b);\n\tvar diffc = function (c) {\n\t\treturn (v - c) / 6 / diff + 1 / 2;\n\t};\n\n\tif (diff === 0) {\n\t\th = s = 0;\n\t} else {\n\t\ts = diff / v;\n\t\trdif = diffc(r);\n\t\tgdif = diffc(g);\n\t\tbdif = diffc(b);\n\n\t\tif (r === v) {\n\t\t\th = bdif - gdif;\n\t\t} else if (g === v) {\n\t\t\th = (1 / 3) + rdif - bdif;\n\t\t} else if (b === v) {\n\t\t\th = (2 / 3) + gdif - rdif;\n\t\t}\n\t\tif (h < 0) {\n\t\t\th += 1;\n\t\t} else if (h > 1) {\n\t\t\th -= 1;\n\t\t}\n\t}\n\n\treturn [\n\t\th * 360,\n\t\ts * 100,\n\t\tv * 100\n\t];\n};\n\nconvert.rgb.hwb = function (rgb) {\n\tvar r = rgb[0];\n\tvar g = rgb[1];\n\tvar b = rgb[2];\n\tvar h = convert.rgb.hsl(rgb)[0];\n\tvar w = 1 / 255 * Math.min(r, Math.min(g, b));\n\n\tb = 1 - 1 / 255 * Math.max(r, Math.max(g, b));\n\n\treturn [h, w * 100, b * 100];\n};\n\nconvert.rgb.cmyk = function (rgb) {\n\tvar r = rgb[0] / 255;\n\tvar g = rgb[1] / 255;\n\tvar b = rgb[2] / 255;\n\tvar c;\n\tvar m;\n\tvar y;\n\tvar k;\n\n\tk = Math.min(1 - r, 1 - g, 1 - b);\n\tc = (1 - r - k) / (1 - k) || 0;\n\tm = (1 - g - k) / (1 - k) || 0;\n\ty = (1 - b - k) / (1 - k) || 0;\n\n\treturn [c * 100, m * 100, y * 100, k * 100];\n};\n\n/**\n * See https://en.m.wikipedia.org/wiki/Euclidean_distance#Squared_Euclidean_distance\n * */\nfunction comparativeDistance(x, y) {\n\treturn (\n\t\tMath.pow(x[0] - y[0], 2) +\n\t\tMath.pow(x[1] - y[1], 2) +\n\t\tMath.pow(x[2] - y[2], 2)\n\t);\n}\n\nconvert.rgb.keyword = function (rgb) {\n\tvar reversed = reverseKeywords[rgb];\n\tif (reversed) {\n\t\treturn reversed;\n\t}\n\n\tvar currentClosestDistance = Infinity;\n\tvar currentClosestKeyword;\n\n\tfor (var keyword in cssKeywords) {\n\t\tif (cssKeywords.hasOwnProperty(keyword)) {\n\t\t\tvar value = cssKeywords[keyword];\n\n\t\t\t// Compute comparative distance\n\t\t\tvar distance = comparativeDistance(rgb, value);\n\n\t\t\t// Check if its less, if so set as closest\n\t\t\tif (distance < currentClosestDistance) {\n\t\t\t\tcurrentClosestDistance = distance;\n\t\t\t\tcurrentClosestKeyword = keyword;\n\t\t\t}\n\t\t}\n\t}\n\n\treturn currentClosestKeyword;\n};\n\nconvert.keyword.rgb = function (keyword) {\n\treturn cssKeywords[keyword];\n};\n\nconvert.rgb.xyz = function (rgb) {\n\tvar r = rgb[0] / 255;\n\tvar g = rgb[1] / 255;\n\tvar b = rgb[2] / 255;\n\n\t// assume sRGB\n\tr = r > 0.04045 ? Math.pow(((r + 0.055) / 1.055), 2.4) : (r / 12.92);\n\tg = g > 0.04045 ? Math.pow(((g + 0.055) / 1.055), 2.4) : (g / 12.92);\n\tb = b > 0.04045 ? Math.pow(((b + 0.055) / 1.055), 2.4) : (b / 12.92);\n\n\tvar x = (r * 0.4124) + (g * 0.3576) + (b * 0.1805);\n\tvar y = (r * 0.2126) + (g * 0.7152) + (b * 0.0722);\n\tvar z = (r * 0.0193) + (g * 0.1192) + (b * 0.9505);\n\n\treturn [x * 100, y * 100, z * 100];\n};\n\nconvert.rgb.lab = function (rgb) {\n\tvar xyz = convert.rgb.xyz(rgb);\n\tvar x = xyz[0];\n\tvar y = xyz[1];\n\tvar z = xyz[2];\n\tvar l;\n\tvar a;\n\tvar b;\n\n\tx /= 95.047;\n\ty /= 100;\n\tz /= 108.883;\n\n\tx = x > 0.008856 ? Math.pow(x, 1 / 3) : (7.787 * x) + (16 / 116);\n\ty = y > 0.008856 ? Math.pow(y, 1 / 3) : (7.787 * y) + (16 / 116);\n\tz = z > 0.008856 ? Math.pow(z, 1 / 3) : (7.787 * z) + (16 / 116);\n\n\tl = (116 * y) - 16;\n\ta = 500 * (x - y);\n\tb = 200 * (y - z);\n\n\treturn [l, a, b];\n};\n\nconvert.hsl.rgb = function (hsl) {\n\tvar h = hsl[0] / 360;\n\tvar s = hsl[1] / 100;\n\tvar l = hsl[2] / 100;\n\tvar t1;\n\tvar t2;\n\tvar t3;\n\tvar rgb;\n\tvar val;\n\n\tif (s === 0) {\n\t\tval = l * 255;\n\t\treturn [val, val, val];\n\t}\n\n\tif (l < 0.5) {\n\t\tt2 = l * (1 + s);\n\t} else {\n\t\tt2 = l + s - l * s;\n\t}\n\n\tt1 = 2 * l - t2;\n\n\trgb = [0, 0, 0];\n\tfor (var i = 0; i < 3; i++) {\n\t\tt3 = h + 1 / 3 * -(i - 1);\n\t\tif (t3 < 0) {\n\t\t\tt3++;\n\t\t}\n\t\tif (t3 > 1) {\n\t\t\tt3--;\n\t\t}\n\n\t\tif (6 * t3 < 1) {\n\t\t\tval = t1 + (t2 - t1) * 6 * t3;\n\t\t} else if (2 * t3 < 1) {\n\t\t\tval = t2;\n\t\t} else if (3 * t3 < 2) {\n\t\t\tval = t1 + (t2 - t1) * (2 / 3 - t3) * 6;\n\t\t} else {\n\t\t\tval = t1;\n\t\t}\n\n\t\trgb[i] = val * 255;\n\t}\n\n\treturn rgb;\n};\n\nconvert.hsl.hsv = function (hsl) {\n\tvar h = hsl[0];\n\tvar s = hsl[1] / 100;\n\tvar l = hsl[2] / 100;\n\tvar smin = s;\n\tvar lmin = Math.max(l, 0.01);\n\tvar sv;\n\tvar v;\n\n\tl *= 2;\n\ts *= (l <= 1) ? l : 2 - l;\n\tsmin *= lmin <= 1 ? lmin : 2 - lmin;\n\tv = (l + s) / 2;\n\tsv = l === 0 ? (2 * smin) / (lmin + smin) : (2 * s) / (l + s);\n\n\treturn [h, sv * 100, v * 100];\n};\n\nconvert.hsv.rgb = function (hsv) {\n\tvar h = hsv[0] / 60;\n\tvar s = hsv[1] / 100;\n\tvar v = hsv[2] / 100;\n\tvar hi = Math.floor(h) % 6;\n\n\tvar f = h - Math.floor(h);\n\tvar p = 255 * v * (1 - s);\n\tvar q = 255 * v * (1 - (s * f));\n\tvar t = 255 * v * (1 - (s * (1 - f)));\n\tv *= 255;\n\n\tswitch (hi) {\n\t\tcase 0:\n\t\t\treturn [v, t, p];\n\t\tcase 1:\n\t\t\treturn [q, v, p];\n\t\tcase 2:\n\t\t\treturn [p, v, t];\n\t\tcase 3:\n\t\t\treturn [p, q, v];\n\t\tcase 4:\n\t\t\treturn [t, p, v];\n\t\tcase 5:\n\t\t\treturn [v, p, q];\n\t}\n};\n\nconvert.hsv.hsl = function (hsv) {\n\tvar h = hsv[0];\n\tvar s = hsv[1] / 100;\n\tvar v = hsv[2] / 100;\n\tvar vmin = Math.max(v, 0.01);\n\tvar lmin;\n\tvar sl;\n\tvar l;\n\n\tl = (2 - s) * v;\n\tlmin = (2 - s) * vmin;\n\tsl = s * vmin;\n\tsl /= (lmin <= 1) ? lmin : 2 - lmin;\n\tsl = sl || 0;\n\tl /= 2;\n\n\treturn [h, sl * 100, l * 100];\n};\n\n// http://dev.w3.org/csswg/css-color/#hwb-to-rgb\nconvert.hwb.rgb = function (hwb) {\n\tvar h = hwb[0] / 360;\n\tvar wh = hwb[1] / 100;\n\tvar bl = hwb[2] / 100;\n\tvar ratio = wh + bl;\n\tvar i;\n\tvar v;\n\tvar f;\n\tvar n;\n\n\t// wh + bl cant be > 1\n\tif (ratio > 1) {\n\t\twh /= ratio;\n\t\tbl /= ratio;\n\t}\n\n\ti = Math.floor(6 * h);\n\tv = 1 - bl;\n\tf = 6 * h - i;\n\n\tif ((i & 0x01) !== 0) {\n\t\tf = 1 - f;\n\t}\n\n\tn = wh + f * (v - wh); // linear interpolation\n\n\tvar r;\n\tvar g;\n\tvar b;\n\tswitch (i) {\n\t\tdefault:\n\t\tcase 6:\n\t\tcase 0: r = v; g = n; b = wh; break;\n\t\tcase 1: r = n; g = v; b = wh; break;\n\t\tcase 2: r = wh; g = v; b = n; break;\n\t\tcase 3: r = wh; g = n; b = v; break;\n\t\tcase 4: r = n; g = wh; b = v; break;\n\t\tcase 5: r = v; g = wh; b = n; break;\n\t}\n\n\treturn [r * 255, g * 255, b * 255];\n};\n\nconvert.cmyk.rgb = function (cmyk) {\n\tvar c = cmyk[0] / 100;\n\tvar m = cmyk[1] / 100;\n\tvar y = cmyk[2] / 100;\n\tvar k = cmyk[3] / 100;\n\tvar r;\n\tvar g;\n\tvar b;\n\n\tr = 1 - Math.min(1, c * (1 - k) + k);\n\tg = 1 - Math.min(1, m * (1 - k) + k);\n\tb = 1 - Math.min(1, y * (1 - k) + k);\n\n\treturn [r * 255, g * 255, b * 255];\n};\n\nconvert.xyz.rgb = function (xyz) {\n\tvar x = xyz[0] / 100;\n\tvar y = xyz[1] / 100;\n\tvar z = xyz[2] / 100;\n\tvar r;\n\tvar g;\n\tvar b;\n\n\tr = (x * 3.2406) + (y * -1.5372) + (z * -0.4986);\n\tg = (x * -0.9689) + (y * 1.8758) + (z * 0.0415);\n\tb = (x * 0.0557) + (y * -0.2040) + (z * 1.0570);\n\n\t// assume sRGB\n\tr = r > 0.0031308\n\t\t? ((1.055 * Math.pow(r, 1.0 / 2.4)) - 0.055)\n\t\t: r * 12.92;\n\n\tg = g > 0.0031308\n\t\t? ((1.055 * Math.pow(g, 1.0 / 2.4)) - 0.055)\n\t\t: g * 12.92;\n\n\tb = b > 0.0031308\n\t\t? ((1.055 * Math.pow(b, 1.0 / 2.4)) - 0.055)\n\t\t: b * 12.92;\n\n\tr = Math.min(Math.max(0, r), 1);\n\tg = Math.min(Math.max(0, g), 1);\n\tb = Math.min(Math.max(0, b), 1);\n\n\treturn [r * 255, g * 255, b * 255];\n};\n\nconvert.xyz.lab = function (xyz) {\n\tvar x = xyz[0];\n\tvar y = xyz[1];\n\tvar z = xyz[2];\n\tvar l;\n\tvar a;\n\tvar b;\n\n\tx /= 95.047;\n\ty /= 100;\n\tz /= 108.883;\n\n\tx = x > 0.008856 ? Math.pow(x, 1 / 3) : (7.787 * x) + (16 / 116);\n\ty = y > 0.008856 ? Math.pow(y, 1 / 3) : (7.787 * y) + (16 / 116);\n\tz = z > 0.008856 ? Math.pow(z, 1 / 3) : (7.787 * z) + (16 / 116);\n\n\tl = (116 * y) - 16;\n\ta = 500 * (x - y);\n\tb = 200 * (y - z);\n\n\treturn [l, a, b];\n};\n\nconvert.lab.xyz = function (lab) {\n\tvar l = lab[0];\n\tvar a = lab[1];\n\tvar b = lab[2];\n\tvar x;\n\tvar y;\n\tvar z;\n\n\ty = (l + 16) / 116;\n\tx = a / 500 + y;\n\tz = y - b / 200;\n\n\tvar y2 = Math.pow(y, 3);\n\tvar x2 = Math.pow(x, 3);\n\tvar z2 = Math.pow(z, 3);\n\ty = y2 > 0.008856 ? y2 : (y - 16 / 116) / 7.787;\n\tx = x2 > 0.008856 ? x2 : (x - 16 / 116) / 7.787;\n\tz = z2 > 0.008856 ? z2 : (z - 16 / 116) / 7.787;\n\n\tx *= 95.047;\n\ty *= 100;\n\tz *= 108.883;\n\n\treturn [x, y, z];\n};\n\nconvert.lab.lch = function (lab) {\n\tvar l = lab[0];\n\tvar a = lab[1];\n\tvar b = lab[2];\n\tvar hr;\n\tvar h;\n\tvar c;\n\n\thr = Math.atan2(b, a);\n\th = hr * 360 / 2 / Math.PI;\n\n\tif (h < 0) {\n\t\th += 360;\n\t}\n\n\tc = Math.sqrt(a * a + b * b);\n\n\treturn [l, c, h];\n};\n\nconvert.lch.lab = function (lch) {\n\tvar l = lch[0];\n\tvar c = lch[1];\n\tvar h = lch[2];\n\tvar a;\n\tvar b;\n\tvar hr;\n\n\thr = h / 360 * 2 * Math.PI;\n\ta = c * Math.cos(hr);\n\tb = c * Math.sin(hr);\n\n\treturn [l, a, b];\n};\n\nconvert.rgb.ansi16 = function (args) {\n\tvar r = args[0];\n\tvar g = args[1];\n\tvar b = args[2];\n\tvar value = 1 in arguments ? arguments[1] : convert.rgb.hsv(args)[2]; // hsv -> ansi16 optimization\n\n\tvalue = Math.round(value / 50);\n\n\tif (value === 0) {\n\t\treturn 30;\n\t}\n\n\tvar ansi = 30\n\t\t+ ((Math.round(b / 255) << 2)\n\t\t| (Math.round(g / 255) << 1)\n\t\t| Math.round(r / 255));\n\n\tif (value === 2) {\n\t\tansi += 60;\n\t}\n\n\treturn ansi;\n};\n\nconvert.hsv.ansi16 = function (args) {\n\t// optimization here; we already know the value and don't need to get\n\t// it converted for us.\n\treturn convert.rgb.ansi16(convert.hsv.rgb(args), args[2]);\n};\n\nconvert.rgb.ansi256 = function (args) {\n\tvar r = args[0];\n\tvar g = args[1];\n\tvar b = args[2];\n\n\t// we use the extended greyscale palette here, with the exception of\n\t// black and white. normal palette only has 4 greyscale shades.\n\tif (r === g && g === b) {\n\t\tif (r < 8) {\n\t\t\treturn 16;\n\t\t}\n\n\t\tif (r > 248) {\n\t\t\treturn 231;\n\t\t}\n\n\t\treturn Math.round(((r - 8) / 247) * 24) + 232;\n\t}\n\n\tvar ansi = 16\n\t\t+ (36 * Math.round(r / 255 * 5))\n\t\t+ (6 * Math.round(g / 255 * 5))\n\t\t+ Math.round(b / 255 * 5);\n\n\treturn ansi;\n};\n\nconvert.ansi16.rgb = function (args) {\n\tvar color = args % 10;\n\n\t// handle greyscale\n\tif (color === 0 || color === 7) {\n\t\tif (args > 50) {\n\t\t\tcolor += 3.5;\n\t\t}\n\n\t\tcolor = color / 10.5 * 255;\n\n\t\treturn [color, color, color];\n\t}\n\n\tvar mult = (~~(args > 50) + 1) * 0.5;\n\tvar r = ((color & 1) * mult) * 255;\n\tvar g = (((color >> 1) & 1) * mult) * 255;\n\tvar b = (((color >> 2) & 1) * mult) * 255;\n\n\treturn [r, g, b];\n};\n\nconvert.ansi256.rgb = function (args) {\n\t// handle greyscale\n\tif (args >= 232) {\n\t\tvar c = (args - 232) * 10 + 8;\n\t\treturn [c, c, c];\n\t}\n\n\targs -= 16;\n\n\tvar rem;\n\tvar r = Math.floor(args / 36) / 5 * 255;\n\tvar g = Math.floor((rem = args % 36) / 6) / 5 * 255;\n\tvar b = (rem % 6) / 5 * 255;\n\n\treturn [r, g, b];\n};\n\nconvert.rgb.hex = function (args) {\n\tvar integer = ((Math.round(args[0]) & 0xFF) << 16)\n\t\t+ ((Math.round(args[1]) & 0xFF) << 8)\n\t\t+ (Math.round(args[2]) & 0xFF);\n\n\tvar string = integer.toString(16).toUpperCase();\n\treturn '000000'.substring(string.length) + string;\n};\n\nconvert.hex.rgb = function (args) {\n\tvar match = args.toString(16).match(/[a-f0-9]{6}|[a-f0-9]{3}/i);\n\tif (!match) {\n\t\treturn [0, 0, 0];\n\t}\n\n\tvar colorString = match[0];\n\n\tif (match[0].length === 3) {\n\t\tcolorString = colorString.split('').map(function (char) {\n\t\t\treturn char + char;\n\t\t}).join('');\n\t}\n\n\tvar integer = parseInt(colorString, 16);\n\tvar r = (integer >> 16) & 0xFF;\n\tvar g = (integer >> 8) & 0xFF;\n\tvar b = integer & 0xFF;\n\n\treturn [r, g, b];\n};\n\nconvert.rgb.hcg = function (rgb) {\n\tvar r = rgb[0] / 255;\n\tvar g = rgb[1] / 255;\n\tvar b = rgb[2] / 255;\n\tvar max = Math.max(Math.max(r, g), b);\n\tvar min = Math.min(Math.min(r, g), b);\n\tvar chroma = (max - min);\n\tvar grayscale;\n\tvar hue;\n\n\tif (chroma < 1) {\n\t\tgrayscale = min / (1 - chroma);\n\t} else {\n\t\tgrayscale = 0;\n\t}\n\n\tif (chroma <= 0) {\n\t\thue = 0;\n\t} else\n\tif (max === r) {\n\t\thue = ((g - b) / chroma) % 6;\n\t} else\n\tif (max === g) {\n\t\thue = 2 + (b - r) / chroma;\n\t} else {\n\t\thue = 4 + (r - g) / chroma + 4;\n\t}\n\n\thue /= 6;\n\thue %= 1;\n\n\treturn [hue * 360, chroma * 100, grayscale * 100];\n};\n\nconvert.hsl.hcg = function (hsl) {\n\tvar s = hsl[1] / 100;\n\tvar l = hsl[2] / 100;\n\tvar c = 1;\n\tvar f = 0;\n\n\tif (l < 0.5) {\n\t\tc = 2.0 * s * l;\n\t} else {\n\t\tc = 2.0 * s * (1.0 - l);\n\t}\n\n\tif (c < 1.0) {\n\t\tf = (l - 0.5 * c) / (1.0 - c);\n\t}\n\n\treturn [hsl[0], c * 100, f * 100];\n};\n\nconvert.hsv.hcg = function (hsv) {\n\tvar s = hsv[1] / 100;\n\tvar v = hsv[2] / 100;\n\n\tvar c = s * v;\n\tvar f = 0;\n\n\tif (c < 1.0) {\n\t\tf = (v - c) / (1 - c);\n\t}\n\n\treturn [hsv[0], c * 100, f * 100];\n};\n\nconvert.hcg.rgb = function (hcg) {\n\tvar h = hcg[0] / 360;\n\tvar c = hcg[1] / 100;\n\tvar g = hcg[2] / 100;\n\n\tif (c === 0.0) {\n\t\treturn [g * 255, g * 255, g * 255];\n\t}\n\n\tvar pure = [0, 0, 0];\n\tvar hi = (h % 1) * 6;\n\tvar v = hi % 1;\n\tvar w = 1 - v;\n\tvar mg = 0;\n\n\tswitch (Math.floor(hi)) {\n\t\tcase 0:\n\t\t\tpure[0] = 1; pure[1] = v; pure[2] = 0; break;\n\t\tcase 1:\n\t\t\tpure[0] = w; pure[1] = 1; pure[2] = 0; break;\n\t\tcase 2:\n\t\t\tpure[0] = 0; pure[1] = 1; pure[2] = v; break;\n\t\tcase 3:\n\t\t\tpure[0] = 0; pure[1] = w; pure[2] = 1; break;\n\t\tcase 4:\n\t\t\tpure[0] = v; pure[1] = 0; pure[2] = 1; break;\n\t\tdefault:\n\t\t\tpure[0] = 1; pure[1] = 0; pure[2] = w;\n\t}\n\n\tmg = (1.0 - c) * g;\n\n\treturn [\n\t\t(c * pure[0] + mg) * 255,\n\t\t(c * pure[1] + mg) * 255,\n\t\t(c * pure[2] + mg) * 255\n\t];\n};\n\nconvert.hcg.hsv = function (hcg) {\n\tvar c = hcg[1] / 100;\n\tvar g = hcg[2] / 100;\n\n\tvar v = c + g * (1.0 - c);\n\tvar f = 0;\n\n\tif (v > 0.0) {\n\t\tf = c / v;\n\t}\n\n\treturn [hcg[0], f * 100, v * 100];\n};\n\nconvert.hcg.hsl = function (hcg) {\n\tvar c = hcg[1] / 100;\n\tvar g = hcg[2] / 100;\n\n\tvar l = g * (1.0 - c) + 0.5 * c;\n\tvar s = 0;\n\n\tif (l > 0.0 && l < 0.5) {\n\t\ts = c / (2 * l);\n\t} else\n\tif (l >= 0.5 && l < 1.0) {\n\t\ts = c / (2 * (1 - l));\n\t}\n\n\treturn [hcg[0], s * 100, l * 100];\n};\n\nconvert.hcg.hwb = function (hcg) {\n\tvar c = hcg[1] / 100;\n\tvar g = hcg[2] / 100;\n\tvar v = c + g * (1.0 - c);\n\treturn [hcg[0], (v - c) * 100, (1 - v) * 100];\n};\n\nconvert.hwb.hcg = function (hwb) {\n\tvar w = hwb[1] / 100;\n\tvar b = hwb[2] / 100;\n\tvar v = 1 - b;\n\tvar c = v - w;\n\tvar g = 0;\n\n\tif (c < 1) {\n\t\tg = (v - c) / (1 - c);\n\t}\n\n\treturn [hwb[0], c * 100, g * 100];\n};\n\nconvert.apple.rgb = function (apple) {\n\treturn [(apple[0] / 65535) * 255, (apple[1] / 65535) * 255, (apple[2] / 65535) * 255];\n};\n\nconvert.rgb.apple = function (rgb) {\n\treturn [(rgb[0] / 255) * 65535, (rgb[1] / 255) * 65535, (rgb[2] / 255) * 65535];\n};\n\nconvert.gray.rgb = function (args) {\n\treturn [args[0] / 100 * 255, args[0] / 100 * 255, args[0] / 100 * 255];\n};\n\nconvert.gray.hsl = convert.gray.hsv = function (args) {\n\treturn [0, 0, args[0]];\n};\n\nconvert.gray.hwb = function (gray) {\n\treturn [0, 100, gray[0]];\n};\n\nconvert.gray.cmyk = function (gray) {\n\treturn [0, 0, 0, gray[0]];\n};\n\nconvert.gray.lab = function (gray) {\n\treturn [gray[0], 0, 0];\n};\n\nconvert.gray.hex = function (gray) {\n\tvar val = Math.round(gray[0] / 100 * 255) & 0xFF;\n\tvar integer = (val << 16) + (val << 8) + val;\n\n\tvar string = integer.toString(16).toUpperCase();\n\treturn '000000'.substring(string.length) + string;\n};\n\nconvert.rgb.gray = function (rgb) {\n\tvar val = (rgb[0] + rgb[1] + rgb[2]) / 3;\n\treturn [val / 255 * 100];\n};\n", "var conversions = require('./conversions');\n\n/*\n\tthis function routes a model to all other models.\n\n\tall functions that are routed have a property `.conversion` attached\n\tto the returned synthetic function. This property is an array\n\tof strings, each with the steps in between the 'from' and 'to'\n\tcolor models (inclusive).\n\n\tconversions that are not possible simply are not included.\n*/\n\nfunction buildGraph() {\n\tvar graph = {};\n\t// https://jsperf.com/object-keys-vs-for-in-with-closure/3\n\tvar models = Object.keys(conversions);\n\n\tfor (var len = models.length, i = 0; i < len; i++) {\n\t\tgraph[models[i]] = {\n\t\t\t// http://jsperf.com/1-vs-infinity\n\t\t\t// micro-opt, but this is simple.\n\t\t\tdistance: -1,\n\t\t\tparent: null\n\t\t};\n\t}\n\n\treturn graph;\n}\n\n// https://en.wikipedia.org/wiki/Breadth-first_search\nfunction deriveBFS(fromModel) {\n\tvar graph = buildGraph();\n\tvar queue = [fromModel]; // unshift -> queue -> pop\n\n\tgraph[fromModel].distance = 0;\n\n\twhile (queue.length) {\n\t\tvar current = queue.pop();\n\t\tvar adjacents = Object.keys(conversions[current]);\n\n\t\tfor (var len = adjacents.length, i = 0; i < len; i++) {\n\t\t\tvar adjacent = adjacents[i];\n\t\t\tvar node = graph[adjacent];\n\n\t\t\tif (node.distance === -1) {\n\t\t\t\tnode.distance = graph[current].distance + 1;\n\t\t\t\tnode.parent = current;\n\t\t\t\tqueue.unshift(adjacent);\n\t\t\t}\n\t\t}\n\t}\n\n\treturn graph;\n}\n\nfunction link(from, to) {\n\treturn function (args) {\n\t\treturn to(from(args));\n\t};\n}\n\nfunction wrapConversion(toModel, graph) {\n\tvar path = [graph[toModel].parent, toModel];\n\tvar fn = conversions[graph[toModel].parent][toModel];\n\n\tvar cur = graph[toModel].parent;\n\twhile (graph[cur].parent) {\n\t\tpath.unshift(graph[cur].parent);\n\t\tfn = link(conversions[graph[cur].parent][cur], fn);\n\t\tcur = graph[cur].parent;\n\t}\n\n\tfn.conversion = path;\n\treturn fn;\n}\n\nmodule.exports = function (fromModel) {\n\tvar graph = deriveBFS(fromModel);\n\tvar conversion = {};\n\n\tvar models = Object.keys(graph);\n\tfor (var len = models.length, i = 0; i < len; i++) {\n\t\tvar toModel = models[i];\n\t\tvar node = graph[toModel];\n\n\t\tif (node.parent === null) {\n\t\t\t// no possible conversion, or this node is the source model.\n\t\t\tcontinue;\n\t\t}\n\n\t\tconversion[toModel] = wrapConversion(toModel, graph);\n\t}\n\n\treturn conversion;\n};\n\n", "var conversions = require('./conversions');\nvar route = require('./route');\n\nvar convert = {};\n\nvar models = Object.keys(conversions);\n\nfunction wrapRaw(fn) {\n\tvar wrappedFn = function (args) {\n\t\tif (args === undefined || args === null) {\n\t\t\treturn args;\n\t\t}\n\n\t\tif (arguments.length > 1) {\n\t\t\targs = Array.prototype.slice.call(arguments);\n\t\t}\n\n\t\treturn fn(args);\n\t};\n\n\t// preserve .conversion property if there is one\n\tif ('conversion' in fn) {\n\t\twrappedFn.conversion = fn.conversion;\n\t}\n\n\treturn wrappedFn;\n}\n\nfunction wrapRounded(fn) {\n\tvar wrappedFn = function (args) {\n\t\tif (args === undefined || args === null) {\n\t\t\treturn args;\n\t\t}\n\n\t\tif (arguments.length > 1) {\n\t\t\targs = Array.prototype.slice.call(arguments);\n\t\t}\n\n\t\tvar result = fn(args);\n\n\t\t// we're assuming the result is an array here.\n\t\t// see notice in conversions.js; don't use box types\n\t\t// in conversion functions.\n\t\tif (typeof result === 'object') {\n\t\t\tfor (var len = result.length, i = 0; i < len; i++) {\n\t\t\t\tresult[i] = Math.round(result[i]);\n\t\t\t}\n\t\t}\n\n\t\treturn result;\n\t};\n\n\t// preserve .conversion property if there is one\n\tif ('conversion' in fn) {\n\t\twrappedFn.conversion = fn.conversion;\n\t}\n\n\treturn wrappedFn;\n}\n\nmodels.forEach(function (fromModel) {\n\tconvert[fromModel] = {};\n\n\tObject.defineProperty(convert[fromModel], 'channels', {value: conversions[fromModel].channels});\n\tObject.defineProperty(convert[fromModel], 'labels', {value: conversions[fromModel].labels});\n\n\tvar routes = route(fromModel);\n\tvar routeModels = Object.keys(routes);\n\n\trouteModels.forEach(function (toModel) {\n\t\tvar fn = routes[toModel];\n\n\t\tconvert[fromModel][toModel] = wrapRounded(fn);\n\t\tconvert[fromModel][toModel].raw = wrapRaw(fn);\n\t});\n});\n\nmodule.exports = convert;\n", "'use strict';\n\nvar colorString = require('color-string');\nvar convert = require('color-convert');\n\nvar _slice = [].slice;\n\nvar skippedModels = [\n\t// to be honest, I don't really feel like keyword belongs in color convert, but eh.\n\t'keyword',\n\n\t// gray conflicts with some method names, and has its own method defined.\n\t'gray',\n\n\t// shouldn't really be in color-convert either...\n\t'hex'\n];\n\nvar hashedModelKeys = {};\nObject.keys(convert).forEach(function (model) {\n\thashedModelKeys[_slice.call(convert[model].labels).sort().join('')] = model;\n});\n\nvar limiters = {};\n\nfunction Color(obj, model) {\n\tif (!(this instanceof Color)) {\n\t\treturn new Color(obj, model);\n\t}\n\n\tif (model && model in skippedModels) {\n\t\tmodel = null;\n\t}\n\n\tif (model && !(model in convert)) {\n\t\tthrow new Error('Unknown model: ' + model);\n\t}\n\n\tvar i;\n\tvar channels;\n\n\tif (obj == null) { // eslint-disable-line no-eq-null,eqeqeq\n\t\tthis.model = 'rgb';\n\t\tthis.color = [0, 0, 0];\n\t\tthis.valpha = 1;\n\t} else if (obj instanceof Color) {\n\t\tthis.model = obj.model;\n\t\tthis.color = obj.color.slice();\n\t\tthis.valpha = obj.valpha;\n\t} else if (typeof obj === 'string') {\n\t\tvar result = colorString.get(obj);\n\t\tif (result === null) {\n\t\t\tthrow new Error('Unable to parse color from string: ' + obj);\n\t\t}\n\n\t\tthis.model = result.model;\n\t\tchannels = convert[this.model].channels;\n\t\tthis.color = result.value.slice(0, channels);\n\t\tthis.valpha = typeof result.value[channels] === 'number' ? result.value[channels] : 1;\n\t} else if (obj.length) {\n\t\tthis.model = model || 'rgb';\n\t\tchannels = convert[this.model].channels;\n\t\tvar newArr = _slice.call(obj, 0, channels);\n\t\tthis.color = zeroArray(newArr, channels);\n\t\tthis.valpha = typeof obj[channels] === 'number' ? obj[channels] : 1;\n\t} else if (typeof obj === 'number') {\n\t\t// this is always RGB - can be converted later on.\n\t\tobj &= 0xFFFFFF;\n\t\tthis.model = 'rgb';\n\t\tthis.color = [\n\t\t\t(obj >> 16) & 0xFF,\n\t\t\t(obj >> 8) & 0xFF,\n\t\t\tobj & 0xFF\n\t\t];\n\t\tthis.valpha = 1;\n\t} else {\n\t\tthis.valpha = 1;\n\n\t\tvar keys = Object.keys(obj);\n\t\tif ('alpha' in obj) {\n\t\t\tkeys.splice(keys.indexOf('alpha'), 1);\n\t\t\tthis.valpha = typeof obj.alpha === 'number' ? obj.alpha : 0;\n\t\t}\n\n\t\tvar hashedKeys = keys.sort().join('');\n\t\tif (!(hashedKeys in hashedModelKeys)) {\n\t\t\tthrow new Error('Unable to parse color from object: ' + JSON.stringify(obj));\n\t\t}\n\n\t\tthis.model = hashedModelKeys[hashedKeys];\n\n\t\tvar labels = convert[this.model].labels;\n\t\tvar color = [];\n\t\tfor (i = 0; i < labels.length; i++) {\n\t\t\tcolor.push(obj[labels[i]]);\n\t\t}\n\n\t\tthis.color = zeroArray(color);\n\t}\n\n\t// perform limitations (clamping, etc.)\n\tif (limiters[this.model]) {\n\t\tchannels = convert[this.model].channels;\n\t\tfor (i = 0; i < channels; i++) {\n\t\t\tvar limit = limiters[this.model][i];\n\t\t\tif (limit) {\n\t\t\t\tthis.color[i] = limit(this.color[i]);\n\t\t\t}\n\t\t}\n\t}\n\n\tthis.valpha = Math.max(0, Math.min(1, this.valpha));\n\n\tif (Object.freeze) {\n\t\tObject.freeze(this);\n\t}\n}\n\nColor.prototype = {\n\ttoString: function () {\n\t\treturn this.string();\n\t},\n\n\ttoJSON: function () {\n\t\treturn this[this.model]();\n\t},\n\n\tstring: function (places) {\n\t\tvar self = this.model in colorString.to ? this : this.rgb();\n\t\tself = self.round(typeof places === 'number' ? places : 1);\n\t\tvar args = self.valpha === 1 ? self.color : self.color.concat(this.valpha);\n\t\treturn colorString.to[self.model](args);\n\t},\n\n\tpercentString: function (places) {\n\t\tvar self = this.rgb().round(typeof places === 'number' ? places : 1);\n\t\tvar args = self.valpha === 1 ? self.color : self.color.concat(this.valpha);\n\t\treturn colorString.to.rgb.percent(args);\n\t},\n\n\tarray: function () {\n\t\treturn this.valpha === 1 ? this.color.slice() : this.color.concat(this.valpha);\n\t},\n\n\tobject: function () {\n\t\tvar result = {};\n\t\tvar channels = convert[this.model].channels;\n\t\tvar labels = convert[this.model].labels;\n\n\t\tfor (var i = 0; i < channels; i++) {\n\t\t\tresult[labels[i]] = this.color[i];\n\t\t}\n\n\t\tif (this.valpha !== 1) {\n\t\t\tresult.alpha = this.valpha;\n\t\t}\n\n\t\treturn result;\n\t},\n\n\tunitArray: function () {\n\t\tvar rgb = this.rgb().color;\n\t\trgb[0] /= 255;\n\t\trgb[1] /= 255;\n\t\trgb[2] /= 255;\n\n\t\tif (this.valpha !== 1) {\n\t\t\trgb.push(this.valpha);\n\t\t}\n\n\t\treturn rgb;\n\t},\n\n\tunitObject: function () {\n\t\tvar rgb = this.rgb().object();\n\t\trgb.r /= 255;\n\t\trgb.g /= 255;\n\t\trgb.b /= 255;\n\n\t\tif (this.valpha !== 1) {\n\t\t\trgb.alpha = this.valpha;\n\t\t}\n\n\t\treturn rgb;\n\t},\n\n\tround: function (places) {\n\t\tplaces = Math.max(places || 0, 0);\n\t\treturn new Color(this.color.map(roundToPlace(places)).concat(this.valpha), this.model);\n\t},\n\n\talpha: function (val) {\n\t\tif (arguments.length) {\n\t\t\treturn new Color(this.color.concat(Math.max(0, Math.min(1, val))), this.model);\n\t\t}\n\n\t\treturn this.valpha;\n\t},\n\n\t// rgb\n\tred: getset('rgb', 0, maxfn(255)),\n\tgreen: getset('rgb', 1, maxfn(255)),\n\tblue: getset('rgb', 2, maxfn(255)),\n\n\thue: getset(['hsl', 'hsv', 'hsl', 'hwb', 'hcg'], 0, function (val) { return ((val % 360) + 360) % 360; }), // eslint-disable-line brace-style\n\n\tsaturationl: getset('hsl', 1, maxfn(100)),\n\tlightness: getset('hsl', 2, maxfn(100)),\n\n\tsaturationv: getset('hsv', 1, maxfn(100)),\n\tvalue: getset('hsv', 2, maxfn(100)),\n\n\tchroma: getset('hcg', 1, maxfn(100)),\n\tgray: getset('hcg', 2, maxfn(100)),\n\n\twhite: getset('hwb', 1, maxfn(100)),\n\twblack: getset('hwb', 2, maxfn(100)),\n\n\tcyan: getset('cmyk', 0, maxfn(100)),\n\tmagenta: getset('cmyk', 1, maxfn(100)),\n\tyellow: getset('cmyk', 2, maxfn(100)),\n\tblack: getset('cmyk', 3, maxfn(100)),\n\n\tx: getset('xyz', 0, maxfn(100)),\n\ty: getset('xyz', 1, maxfn(100)),\n\tz: getset('xyz', 2, maxfn(100)),\n\n\tl: getset('lab', 0, maxfn(100)),\n\ta: getset('lab', 1),\n\tb: getset('lab', 2),\n\n\tkeyword: function (val) {\n\t\tif (arguments.length) {\n\t\t\treturn new Color(val);\n\t\t}\n\n\t\treturn convert[this.model].keyword(this.color);\n\t},\n\n\thex: function (val) {\n\t\tif (arguments.length) {\n\t\t\treturn new Color(val);\n\t\t}\n\n\t\treturn colorString.to.hex(this.rgb().round().color);\n\t},\n\n\trgbNumber: function () {\n\t\tvar rgb = this.rgb().color;\n\t\treturn ((rgb[0] & 0xFF) << 16) | ((rgb[1] & 0xFF) << 8) | (rgb[2] & 0xFF);\n\t},\n\n\tluminosity: function () {\n\t\t// http://www.w3.org/TR/WCAG20/#relativeluminancedef\n\t\tvar rgb = this.rgb().color;\n\n\t\tvar lum = [];\n\t\tfor (var i = 0; i < rgb.length; i++) {\n\t\t\tvar chan = rgb[i] / 255;\n\t\t\tlum[i] = (chan <= 0.03928) ? chan / 12.92 : Math.pow(((chan + 0.055) / 1.055), 2.4);\n\t\t}\n\n\t\treturn 0.2126 * lum[0] + 0.7152 * lum[1] + 0.0722 * lum[2];\n\t},\n\n\tcontrast: function (color2) {\n\t\t// http://www.w3.org/TR/WCAG20/#contrast-ratiodef\n\t\tvar lum1 = this.luminosity();\n\t\tvar lum2 = color2.luminosity();\n\n\t\tif (lum1 > lum2) {\n\t\t\treturn (lum1 + 0.05) / (lum2 + 0.05);\n\t\t}\n\n\t\treturn (lum2 + 0.05) / (lum1 + 0.05);\n\t},\n\n\tlevel: function (color2) {\n\t\tvar contrastRatio = this.contrast(color2);\n\t\tif (contrastRatio >= 7.1) {\n\t\t\treturn 'AAA';\n\t\t}\n\n\t\treturn (contrastRatio >= 4.5) ? 'AA' : '';\n\t},\n\n\tisDark: function () {\n\t\t// YIQ equation from http://24ways.org/2010/calculating-color-contrast\n\t\tvar rgb = this.rgb().color;\n\t\tvar yiq = (rgb[0] * 299 + rgb[1] * 587 + rgb[2] * 114) / 1000;\n\t\treturn yiq < 128;\n\t},\n\n\tisLight: function () {\n\t\treturn !this.isDark();\n\t},\n\n\tnegate: function () {\n\t\tvar rgb = this.rgb();\n\t\tfor (var i = 0; i < 3; i++) {\n\t\t\trgb.color[i] = 255 - rgb.color[i];\n\t\t}\n\t\treturn rgb;\n\t},\n\n\tlighten: function (ratio) {\n\t\tvar hsl = this.hsl();\n\t\thsl.color[2] += hsl.color[2] * ratio;\n\t\treturn hsl;\n\t},\n\n\tdarken: function (ratio) {\n\t\tvar hsl = this.hsl();\n\t\thsl.color[2] -= hsl.color[2] * ratio;\n\t\treturn hsl;\n\t},\n\n\tsaturate: function (ratio) {\n\t\tvar hsl = this.hsl();\n\t\thsl.color[1] += hsl.color[1] * ratio;\n\t\treturn hsl;\n\t},\n\n\tdesaturate: function (ratio) {\n\t\tvar hsl = this.hsl();\n\t\thsl.color[1] -= hsl.color[1] * ratio;\n\t\treturn hsl;\n\t},\n\n\twhiten: function (ratio) {\n\t\tvar hwb = this.hwb();\n\t\thwb.color[1] += hwb.color[1] * ratio;\n\t\treturn hwb;\n\t},\n\n\tblacken: function (ratio) {\n\t\tvar hwb = this.hwb();\n\t\thwb.color[2] += hwb.color[2] * ratio;\n\t\treturn hwb;\n\t},\n\n\tgrayscale: function () {\n\t\t// http://en.wikipedia.org/wiki/Grayscale#Converting_color_to_grayscale\n\t\tvar rgb = this.rgb().color;\n\t\tvar val = rgb[0] * 0.3 + rgb[1] * 0.59 + rgb[2] * 0.11;\n\t\treturn Color.rgb(val, val, val);\n\t},\n\n\tfade: function (ratio) {\n\t\treturn this.alpha(this.valpha - (this.valpha * ratio));\n\t},\n\n\topaquer: function (ratio) {\n\t\treturn this.alpha(this.valpha + (this.valpha * ratio));\n\t},\n\n\trotate: function (degrees) {\n\t\tvar hsl = this.hsl();\n\t\tvar hue = hsl.color[0];\n\t\thue = (hue + degrees) % 360;\n\t\thue = hue < 0 ? 360 + hue : hue;\n\t\thsl.color[0] = hue;\n\t\treturn hsl;\n\t},\n\n\tmix: function (mixinColor, weight) {\n\t\t// ported from sass implementation in C\n\t\t// https://github.com/sass/libsass/blob/0e6b4a2850092356aa3ece07c6b249f0221caced/functions.cpp#L209\n\t\tif (!mixinColor || !mixinColor.rgb) {\n\t\t\tthrow new Error('Argument to \"mix\" was not a Color instance, but rather an instance of ' + typeof mixinColor);\n\t\t}\n\t\tvar color1 = mixinColor.rgb();\n\t\tvar color2 = this.rgb();\n\t\tvar p = weight === undefined ? 0.5 : weight;\n\n\t\tvar w = 2 * p - 1;\n\t\tvar a = color1.alpha() - color2.alpha();\n\n\t\tvar w1 = (((w * a === -1) ? w : (w + a) / (1 + w * a)) + 1) / 2.0;\n\t\tvar w2 = 1 - w1;\n\n\t\treturn Color.rgb(\n\t\t\t\tw1 * color1.red() + w2 * color2.red(),\n\t\t\t\tw1 * color1.green() + w2 * color2.green(),\n\t\t\t\tw1 * color1.blue() + w2 * color2.blue(),\n\t\t\t\tcolor1.alpha() * p + color2.alpha() * (1 - p));\n\t}\n};\n\n// model conversion methods and static constructors\nObject.keys(convert).forEach(function (model) {\n\tif (skippedModels.indexOf(model) !== -1) {\n\t\treturn;\n\t}\n\n\tvar channels = convert[model].channels;\n\n\t// conversion methods\n\tColor.prototype[model] = function () {\n\t\tif (this.model === model) {\n\t\t\treturn new Color(this);\n\t\t}\n\n\t\tif (arguments.length) {\n\t\t\treturn new Color(arguments, model);\n\t\t}\n\n\t\tvar newAlpha = typeof arguments[channels] === 'number' ? channels : this.valpha;\n\t\treturn new Color(assertArray(convert[this.model][model].raw(this.color)).concat(newAlpha), model);\n\t};\n\n\t// 'static' construction methods\n\tColor[model] = function (color) {\n\t\tif (typeof color === 'number') {\n\t\t\tcolor = zeroArray(_slice.call(arguments), channels);\n\t\t}\n\t\treturn new Color(color, model);\n\t};\n});\n\nfunction roundTo(num, places) {\n\treturn Number(num.toFixed(places));\n}\n\nfunction roundToPlace(places) {\n\treturn function (num) {\n\t\treturn roundTo(num, places);\n\t};\n}\n\nfunction getset(model, channel, modifier) {\n\tmodel = Array.isArray(model) ? model : [model];\n\n\tmodel.forEach(function (m) {\n\t\t(limiters[m] || (limiters[m] = []))[channel] = modifier;\n\t});\n\n\tmodel = model[0];\n\n\treturn function (val) {\n\t\tvar result;\n\n\t\tif (arguments.length) {\n\t\t\tif (modifier) {\n\t\t\t\tval = modifier(val);\n\t\t\t}\n\n\t\t\tresult = this[model]();\n\t\t\tresult.color[channel] = val;\n\t\t\treturn result;\n\t\t}\n\n\t\tresult = this[model]().color[channel];\n\t\tif (modifier) {\n\t\t\tresult = modifier(result);\n\t\t}\n\n\t\treturn result;\n\t};\n}\n\nfunction maxfn(max) {\n\treturn function (v) {\n\t\treturn Math.max(0, Math.min(max, v));\n\t};\n}\n\nfunction assertArray(val) {\n\treturn Array.isArray(val) ? val : [val];\n}\n\nfunction zeroArray(arr, length) {\n\tfor (var i = 0; i < length; i++) {\n\t\tif (typeof arr[i] !== 'number') {\n\t\t\tarr[i] = 0;\n\t\t}\n\t}\n\n\treturn arr;\n}\n\nmodule.exports = Color;\n", "import Color from 'color';\n\nexport {Color};\n\nexport function lightenToContrastThreshold(foreground: string | Color, background: string | Color, contrastThreshold: number): Color {\n    const foregroundColor = Color(foreground);\n    const backgroundColor = Color(background);\n\n    const {h,s} = foregroundColor.hsl().object();\n\n    let newColor = foregroundColor;\n\n    while (newColor.contrast(backgroundColor) < contrastThreshold) {\n        if (newColor.lightness() >= 100) {\n            break;\n        }\n\n        newColor = Color({h, s, l: newColor.lightness() + 5});\n    }\n\n    return newColor;\n}\n\nexport function darkenToContrastThreshold(foreground: string | Color, background: string | Color, contrastThreshold: number): Color {\n    const foregroundColor = Color(foreground);\n    const backgroundColor = Color(background);\n\n    const {h,s} = foregroundColor.hsl().object();\n\n    let newColor = foregroundColor;\n\n    while (newColor.contrast(backgroundColor) < contrastThreshold) {\n        if (newColor.lightness() <= 0) {\n            break;\n        }\n\n        newColor = Color({h, s, l: newColor.lightness() - 5});\n    }\n\n    return newColor;\n}\n\nexport function textColorForBackgroundColor(background: string | Color): Color {\n    const backgroundColor = Color(background);\n\n    const white = Color({r: 255, g: 255, b: 255});\n    const black = Color({r: 0, g: 0, b: 0});\n\n    // shared with Portal https://github.com/TryGhost/Portal/blob/317876f20d22431df15e655ea6cc197fe636615e/src/utils/contrast-color.js#L26-L29\n    const yiq = (\n        backgroundColor.red() * 0.299 +\n        backgroundColor.green() * 0.587 +\n        backgroundColor.b() * 0.114\n    );\n\n    return (yiq >= 186) ? black : white;\n}\n"], "names": ["colorName", "colorNames", "swizzle", "cssKeywords", "convert", "Color"], "mappings": ";;;;AAEA,IAAAA,WAAc,GAAG;AACjB,CAAC,WAAW,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC;AAC7B,CAAC,cAAc,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC;AAChC,CAAC,MAAM,EAAE,CAAC,CAAC,EAAE,GAAG,EAAE,GAAG,CAAC;AACtB,CAAC,YAAY,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC;AAC9B,CAAC,OAAO,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC;AACzB,CAAC,OAAO,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC;AACzB,CAAC,QAAQ,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC;AAC1B,CAAC,OAAO,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;AACnB,CAAC,gBAAgB,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC;AAClC,CAAC,MAAM,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,GAAG,CAAC;AACpB,CAAC,YAAY,EAAE,CAAC,GAAG,EAAE,EAAE,EAAE,GAAG,CAAC;AAC7B,CAAC,OAAO,EAAE,CAAC,GAAG,EAAE,EAAE,EAAE,EAAE,CAAC;AACvB,CAAC,WAAW,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC;AAC7B,CAAC,WAAW,EAAE,CAAC,EAAE,EAAE,GAAG,EAAE,GAAG,CAAC;AAC5B,CAAC,YAAY,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,CAAC,CAAC;AAC5B,CAAC,WAAW,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,EAAE,CAAC;AAC5B,CAAC,OAAO,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,EAAE,CAAC;AACxB,CAAC,gBAAgB,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC;AAClC,CAAC,UAAU,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC;AAC5B,CAAC,SAAS,EAAE,CAAC,GAAG,EAAE,EAAE,EAAE,EAAE,CAAC;AACzB,CAAC,MAAM,EAAE,CAAC,CAAC,EAAE,GAAG,EAAE,GAAG,CAAC;AACtB,CAAC,UAAU,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,GAAG,CAAC;AACxB,CAAC,UAAU,EAAE,CAAC,CAAC,EAAE,GAAG,EAAE,GAAG,CAAC;AAC1B,CAAC,eAAe,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,EAAE,CAAC;AAChC,CAAC,UAAU,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC;AAC5B,CAAC,WAAW,EAAE,CAAC,CAAC,EAAE,GAAG,EAAE,CAAC,CAAC;AACzB,CAAC,UAAU,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC;AAC5B,CAAC,WAAW,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC;AAC7B,CAAC,aAAa,EAAE,CAAC,GAAG,EAAE,CAAC,EAAE,GAAG,CAAC;AAC7B,CAAC,gBAAgB,EAAE,CAAC,EAAE,EAAE,GAAG,EAAE,EAAE,CAAC;AAChC,CAAC,YAAY,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,CAAC,CAAC;AAC5B,CAAC,YAAY,EAAE,CAAC,GAAG,EAAE,EAAE,EAAE,GAAG,CAAC;AAC7B,CAAC,SAAS,EAAE,CAAC,GAAG,EAAE,CAAC,EAAE,CAAC,CAAC;AACvB,CAAC,YAAY,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC;AAC9B,CAAC,cAAc,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC;AAChC,CAAC,eAAe,EAAE,CAAC,EAAE,EAAE,EAAE,EAAE,GAAG,CAAC;AAC/B,CAAC,eAAe,EAAE,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC;AAC9B,CAAC,eAAe,EAAE,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC;AAC9B,CAAC,eAAe,EAAE,CAAC,CAAC,EAAE,GAAG,EAAE,GAAG,CAAC;AAC/B,CAAC,YAAY,EAAE,CAAC,GAAG,EAAE,CAAC,EAAE,GAAG,CAAC;AAC5B,CAAC,UAAU,EAAE,CAAC,GAAG,EAAE,EAAE,EAAE,GAAG,CAAC;AAC3B,CAAC,aAAa,EAAE,CAAC,CAAC,EAAE,GAAG,EAAE,GAAG,CAAC;AAC7B,CAAC,SAAS,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC;AAC3B,CAAC,SAAS,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC;AAC3B,CAAC,YAAY,EAAE,CAAC,EAAE,EAAE,GAAG,EAAE,GAAG,CAAC;AAC7B,CAAC,WAAW,EAAE,CAAC,GAAG,EAAE,EAAE,EAAE,EAAE,CAAC;AAC3B,CAAC,aAAa,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC;AAC/B,CAAC,aAAa,EAAE,CAAC,EAAE,EAAE,GAAG,EAAE,EAAE,CAAC;AAC7B,CAAC,SAAS,EAAE,CAAC,GAAG,EAAE,CAAC,EAAE,GAAG,CAAC;AACzB,CAAC,WAAW,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC;AAC7B,CAAC,YAAY,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC;AAC9B,CAAC,MAAM,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,CAAC,CAAC;AACtB,CAAC,WAAW,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,EAAE,CAAC;AAC5B,CAAC,MAAM,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC;AACxB,CAAC,OAAO,EAAE,CAAC,CAAC,EAAE,GAAG,EAAE,CAAC,CAAC;AACrB,CAAC,aAAa,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,EAAE,CAAC;AAC9B,CAAC,MAAM,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC;AACxB,CAAC,UAAU,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC;AAC5B,CAAC,SAAS,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC;AAC3B,CAAC,WAAW,EAAE,CAAC,GAAG,EAAE,EAAE,EAAE,EAAE,CAAC;AAC3B,CAAC,QAAQ,EAAE,CAAC,EAAE,EAAE,CAAC,EAAE,GAAG,CAAC;AACvB,CAAC,OAAO,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC;AACzB,CAAC,OAAO,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC;AACzB,CAAC,UAAU,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC;AAC5B,CAAC,eAAe,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC;AACjC,CAAC,WAAW,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,CAAC,CAAC;AAC3B,CAAC,cAAc,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC;AAChC,CAAC,WAAW,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC;AAC7B,CAAC,YAAY,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC;AAC9B,CAAC,WAAW,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC;AAC7B,CAAC,sBAAsB,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC;AACxC,CAAC,WAAW,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC;AAC7B,CAAC,YAAY,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC;AAC9B,CAAC,WAAW,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC;AAC7B,CAAC,WAAW,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC;AAC7B,CAAC,aAAa,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC;AAC/B,CAAC,eAAe,EAAE,CAAC,EAAE,EAAE,GAAG,EAAE,GAAG,CAAC;AAChC,CAAC,cAAc,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC;AAChC,CAAC,gBAAgB,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC;AAClC,CAAC,gBAAgB,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC;AAClC,CAAC,gBAAgB,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC;AAClC,CAAC,aAAa,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC;AAC/B,CAAC,MAAM,EAAE,CAAC,CAAC,EAAE,GAAG,EAAE,CAAC,CAAC;AACpB,CAAC,WAAW,EAAE,CAAC,EAAE,EAAE,GAAG,EAAE,EAAE,CAAC;AAC3B,CAAC,OAAO,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC;AACzB,CAAC,SAAS,EAAE,CAAC,GAAG,EAAE,CAAC,EAAE,GAAG,CAAC;AACzB,CAAC,QAAQ,EAAE,CAAC,GAAG,EAAE,CAAC,EAAE,CAAC,CAAC;AACtB,CAAC,kBAAkB,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC;AACpC,CAAC,YAAY,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,GAAG,CAAC;AAC1B,CAAC,cAAc,EAAE,CAAC,GAAG,EAAE,EAAE,EAAE,GAAG,CAAC;AAC/B,CAAC,cAAc,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC;AAChC,CAAC,gBAAgB,EAAE,CAAC,EAAE,EAAE,GAAG,EAAE,GAAG,CAAC;AACjC,CAAC,iBAAiB,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC;AACnC,CAAC,mBAAmB,EAAE,CAAC,CAAC,EAAE,GAAG,EAAE,GAAG,CAAC;AACnC,CAAC,iBAAiB,EAAE,CAAC,EAAE,EAAE,GAAG,EAAE,GAAG,CAAC;AAClC,CAAC,iBAAiB,EAAE,CAAC,GAAG,EAAE,EAAE,EAAE,GAAG,CAAC;AAClC,CAAC,cAAc,EAAE,CAAC,EAAE,EAAE,EAAE,EAAE,GAAG,CAAC;AAC9B,CAAC,WAAW,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC;AAC7B,CAAC,WAAW,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC;AAC7B,CAAC,UAAU,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC;AAC5B,CAAC,aAAa,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC;AAC/B,CAAC,MAAM,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,GAAG,CAAC;AACpB,CAAC,SAAS,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC;AAC3B,CAAC,OAAO,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,CAAC,CAAC;AACvB,CAAC,WAAW,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,EAAE,CAAC;AAC5B,CAAC,QAAQ,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,CAAC,CAAC;AACxB,CAAC,WAAW,EAAE,CAAC,GAAG,EAAE,EAAE,EAAE,CAAC,CAAC;AAC1B,CAAC,QAAQ,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC;AAC1B,CAAC,eAAe,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC;AACjC,CAAC,WAAW,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC;AAC7B,CAAC,eAAe,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC;AACjC,CAAC,eAAe,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC;AACjC,CAAC,YAAY,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC;AAC9B,CAAC,WAAW,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC;AAC7B,CAAC,MAAM,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,EAAE,CAAC;AACvB,CAAC,MAAM,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC;AACxB,CAAC,MAAM,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC;AACxB,CAAC,YAAY,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC;AAC9B,CAAC,QAAQ,EAAE,CAAC,GAAG,EAAE,CAAC,EAAE,GAAG,CAAC;AACxB,CAAC,eAAe,EAAE,CAAC,GAAG,EAAE,EAAE,EAAE,GAAG,CAAC;AAChC,CAAC,KAAK,EAAE,CAAC,GAAG,EAAE,CAAC,EAAE,CAAC,CAAC;AACnB,CAAC,WAAW,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC;AAC7B,CAAC,WAAW,EAAE,CAAC,EAAE,EAAE,GAAG,EAAE,GAAG,CAAC;AAC5B,CAAC,aAAa,EAAE,CAAC,GAAG,EAAE,EAAE,EAAE,EAAE,CAAC;AAC7B,CAAC,QAAQ,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC;AAC1B,CAAC,YAAY,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,EAAE,CAAC;AAC7B,CAAC,UAAU,EAAE,CAAC,EAAE,EAAE,GAAG,EAAE,EAAE,CAAC;AAC1B,CAAC,UAAU,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC;AAC5B,CAAC,QAAQ,EAAE,CAAC,GAAG,EAAE,EAAE,EAAE,EAAE,CAAC;AACxB,CAAC,QAAQ,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC;AAC1B,CAAC,SAAS,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC;AAC3B,CAAC,WAAW,EAAE,CAAC,GAAG,EAAE,EAAE,EAAE,GAAG,CAAC;AAC5B,CAAC,WAAW,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC;AAC7B,CAAC,WAAW,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC;AAC7B,CAAC,MAAM,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC;AACxB,CAAC,aAAa,EAAE,CAAC,CAAC,EAAE,GAAG,EAAE,GAAG,CAAC;AAC7B,CAAC,WAAW,EAAE,CAAC,EAAE,EAAE,GAAG,EAAE,GAAG,CAAC;AAC5B,CAAC,KAAK,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC;AACvB,CAAC,MAAM,EAAE,CAAC,CAAC,EAAE,GAAG,EAAE,GAAG,CAAC;AACtB,CAAC,SAAS,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC;AAC3B,CAAC,QAAQ,EAAE,CAAC,GAAG,EAAE,EAAE,EAAE,EAAE,CAAC;AACxB,CAAC,WAAW,EAAE,CAAC,EAAE,EAAE,GAAG,EAAE,GAAG,CAAC;AAC5B,CAAC,QAAQ,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC;AAC1B,CAAC,OAAO,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC;AACzB,CAAC,OAAO,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC;AACzB,CAAC,YAAY,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC;AAC9B,CAAC,QAAQ,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,CAAC,CAAC;AACxB,CAAC,aAAa,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,EAAE,CAAC;AAC9B,CAAC;;ACvJD,IAAA,UAAc,GAAG,SAAS,UAAU,CAAC,GAAG,EAAE;AAC1C,CAAC,IAAI,CAAC,GAAG,IAAI,OAAO,GAAG,KAAK,QAAQ,EAAE;AACtC,EAAE,OAAO,KAAK,CAAC;AACf,EAAE;AACF;AACA,CAAC,OAAO,GAAG,YAAY,KAAK,IAAI,KAAK,CAAC,OAAO,CAAC,GAAG,CAAC;AAClD,GAAG,GAAG,CAAC,MAAM,IAAI,CAAC,KAAK,GAAG,CAAC,MAAM,YAAY,QAAQ;AACrD,IAAI,MAAM,CAAC,wBAAwB,CAAC,GAAG,GAAG,GAAG,CAAC,MAAM,GAAG,CAAC,EAAE,IAAI,GAAG,CAAC,WAAW,CAAC,IAAI,KAAK,QAAQ,CAAC,CAAC,CAAC,CAAC;AACnG,CAAC;;;ACPD;AACwC;AACxC;AACA,IAAI,MAAM,GAAG,KAAK,CAAC,SAAS,CAAC,MAAM,CAAC;AACpC,IAAI,KAAK,GAAG,KAAK,CAAC,SAAS,CAAC,KAAK,CAAC;AAClC;AACA,IAAI,OAAO,GAAG,MAAc,CAAA,OAAA,GAAG,SAAS,OAAO,CAAC,IAAI,EAAE;AACtD,CAAC,IAAI,OAAO,GAAG,EAAE,CAAC;AAClB;AACA,CAAC,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,GAAG,GAAG,IAAI,CAAC,MAAM,EAAE,CAAC,GAAG,GAAG,EAAE,CAAC,EAAE,EAAE;AAClD,EAAE,IAAI,GAAG,GAAG,IAAI,CAAC,CAAC,CAAC,CAAC;AACpB;AACA,EAAE,IAAI,UAAU,CAAC,GAAG,CAAC,EAAE;AACvB;AACA,GAAG,OAAO,GAAG,MAAM,CAAC,IAAI,CAAC,OAAO,EAAE,KAAK,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC;AACnD,GAAG,MAAM;AACT,GAAG,OAAO,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;AACrB,GAAG;AACH,EAAE;AACF;AACA,CAAC,OAAO,OAAO,CAAC;AAChB,CAAC,CAAC;AACF;AACA,OAAO,CAAC,IAAI,GAAG,UAAU,EAAE,EAAE;AAC7B,CAAC,OAAO,YAAY;AACpB,EAAE,OAAO,EAAE,CAAC,OAAO,CAAC,SAAS,CAAC,CAAC,CAAC;AAChC,EAAE,CAAC;AACH,CAAC,CAAA;;;;AC5BD;AACuC;AACC;AACxC,IAAI,cAAc,GAAG,MAAM,CAAC,cAAc,CAAC;AAC3C;AACA,IAAI,YAAY,GAAG,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;AACvC;AACA;AACA,KAAK,IAAI,IAAI,IAAIC,WAAU,EAAE;AAC7B,CAAC,IAAI,cAAc,CAAC,IAAI,CAACA,WAAU,EAAE,IAAI,CAAC,EAAE;AAC5C,EAAE,YAAY,CAACA,WAAU,CAAC,IAAI,CAAC,CAAC,GAAG,IAAI,CAAC;AACxC,EAAE;AACF,CAAC;AACD;AACA,IAAI,EAAE,GAAG,MAAA,CAAA,OAAc,GAAG;AAC1B,CAAC,EAAE,EAAE,EAAE;AACP,CAAC,GAAG,EAAE,EAAE;AACR,CAAC,CAAC;AACF;AACA,EAAE,CAAC,GAAG,GAAG,UAAU,MAAM,EAAE;AAC3B,CAAC,IAAI,MAAM,GAAG,MAAM,CAAC,SAAS,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,WAAW,EAAE,CAAC;AACnD,CAAC,IAAI,GAAG,CAAC;AACT,CAAC,IAAI,KAAK,CAAC;AACX,CAAC,QAAQ,MAAM;AACf,EAAE,KAAK,KAAK;AACZ,GAAG,GAAG,GAAG,EAAE,CAAC,GAAG,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC;AAC5B,GAAG,KAAK,GAAG,KAAK,CAAC;AACjB,GAAG,MAAM;AACT,EAAE,KAAK,KAAK;AACZ,GAAG,GAAG,GAAG,EAAE,CAAC,GAAG,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC;AAC5B,GAAG,KAAK,GAAG,KAAK,CAAC;AACjB,GAAG,MAAM;AACT,EAAE;AACF,GAAG,GAAG,GAAG,EAAE,CAAC,GAAG,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC;AAC5B,GAAG,KAAK,GAAG,KAAK,CAAC;AACjB,GAAG,MAAM;AACT,EAAE;AACF;AACA,CAAC,IAAI,CAAC,GAAG,EAAE;AACX,EAAE,OAAO,IAAI,CAAC;AACd,EAAE;AACF;AACA,CAAC,OAAO,CAAC,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,GAAG,CAAC,CAAC;AACnC,CAAC,CAAC;AACF;AACA,EAAE,CAAC,GAAG,CAAC,GAAG,GAAG,UAAU,MAAM,EAAE;AAC/B,CAAC,IAAI,CAAC,MAAM,EAAE;AACd,EAAE,OAAO,IAAI,CAAC;AACd,EAAE;AACF;AACA,CAAC,IAAI,IAAI,GAAG,qBAAqB,CAAC;AAClC,CAAC,IAAI,GAAG,GAAG,iCAAiC,CAAC;AAC7C,CAAC,IAAI,IAAI,GAAG,8HAA8H,CAAC;AAC3I,CAAC,IAAI,GAAG,GAAG,sHAAsH,CAAC;AAClI,CAAC,IAAI,OAAO,GAAG,SAAS,CAAC;AACzB;AACA,CAAC,IAAI,GAAG,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;AACxB,CAAC,IAAI,KAAK,CAAC;AACX,CAAC,IAAI,CAAC,CAAC;AACP,CAAC,IAAI,QAAQ,CAAC;AACd;AACA,CAAC,IAAI,KAAK,GAAG,MAAM,CAAC,KAAK,CAAC,GAAG,CAAC,EAAE;AAChC,EAAE,QAAQ,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC;AACtB,EAAE,KAAK,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC;AACnB;AACA,EAAE,KAAK,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,EAAE,EAAE;AAC1B;AACA,GAAG,IAAI,EAAE,GAAG,CAAC,GAAG,CAAC,CAAC;AAClB,GAAG,GAAG,CAAC,CAAC,CAAC,GAAG,QAAQ,CAAC,KAAK,CAAC,KAAK,CAAC,EAAE,EAAE,EAAE,GAAG,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC;AAClD,GAAG;AACH;AACA,EAAE,IAAI,QAAQ,EAAE;AAChB,GAAG,GAAG,CAAC,CAAC,CAAC,GAAG,QAAQ,CAAC,QAAQ,EAAE,EAAE,CAAC,GAAG,GAAG,CAAC;AACzC,GAAG;AACH,EAAE,MAAM,IAAI,KAAK,GAAG,MAAM,CAAC,KAAK,CAAC,IAAI,CAAC,EAAE;AACxC,EAAE,KAAK,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC;AACnB,EAAE,QAAQ,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC;AACtB;AACA,EAAE,KAAK,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,EAAE,EAAE;AAC1B,GAAG,GAAG,CAAC,CAAC,CAAC,GAAG,QAAQ,CAAC,KAAK,CAAC,CAAC,CAAC,GAAG,KAAK,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC;AAC9C,GAAG;AACH;AACA,EAAE,IAAI,QAAQ,EAAE;AAChB,GAAG,GAAG,CAAC,CAAC,CAAC,GAAG,QAAQ,CAAC,QAAQ,GAAG,QAAQ,EAAE,EAAE,CAAC,GAAG,GAAG,CAAC;AACpD,GAAG;AACH,EAAE,MAAM,IAAI,KAAK,GAAG,MAAM,CAAC,KAAK,CAAC,IAAI,CAAC,EAAE;AACxC,EAAE,KAAK,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,EAAE,EAAE;AAC1B,GAAG,GAAG,CAAC,CAAC,CAAC,GAAG,QAAQ,CAAC,KAAK,CAAC,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;AACtC,GAAG;AACH;AACA,EAAE,IAAI,KAAK,CAAC,CAAC,CAAC,EAAE;AAChB,GAAG,IAAI,KAAK,CAAC,CAAC,CAAC,EAAE;AACjB,IAAI,GAAG,CAAC,CAAC,CAAC,GAAG,UAAU,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC;AACzC,IAAI,MAAM;AACV,IAAI,GAAG,CAAC,CAAC,CAAC,GAAG,UAAU,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC;AAClC,IAAI;AACJ,GAAG;AACH,EAAE,MAAM,IAAI,KAAK,GAAG,MAAM,CAAC,KAAK,CAAC,GAAG,CAAC,EAAE;AACvC,EAAE,KAAK,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,EAAE,EAAE;AAC1B,GAAG,GAAG,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,KAAK,CAAC,UAAU,CAAC,KAAK,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,CAAC;AACxD,GAAG;AACH;AACA,EAAE,IAAI,KAAK,CAAC,CAAC,CAAC,EAAE;AAChB,GAAG,IAAI,KAAK,CAAC,CAAC,CAAC,EAAE;AACjB,IAAI,GAAG,CAAC,CAAC,CAAC,GAAG,UAAU,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC;AACzC,IAAI,MAAM;AACV,IAAI,GAAG,CAAC,CAAC,CAAC,GAAG,UAAU,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC;AAClC,IAAI;AACJ,GAAG;AACH,EAAE,MAAM,IAAI,KAAK,GAAG,MAAM,CAAC,KAAK,CAAC,OAAO,CAAC,EAAE;AAC3C,EAAE,IAAI,KAAK,CAAC,CAAC,CAAC,KAAK,aAAa,EAAE;AAClC,GAAG,OAAO,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;AACvB,GAAG;AACH;AACA,EAAE,IAAI,CAAC,cAAc,CAAC,IAAI,CAACA,WAAU,EAAE,KAAK,CAAC,CAAC,CAAC,CAAC,EAAE;AAClD,GAAG,OAAO,IAAI,CAAC;AACf,GAAG;AACH;AACA,EAAE,GAAG,GAAGA,WAAU,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC;AAC7B,EAAE,GAAG,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;AACb;AACA,EAAE,OAAO,GAAG,CAAC;AACb,EAAE,MAAM;AACR,EAAE,OAAO,IAAI,CAAC;AACd,EAAE;AACF;AACA,CAAC,KAAK,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,EAAE,EAAE;AACzB,EAAE,GAAG,CAAC,CAAC,CAAC,GAAG,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,GAAG,CAAC,CAAC;AACjC,EAAE;AACF,CAAC,GAAG,CAAC,CAAC,CAAC,GAAG,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;AAC9B;AACA,CAAC,OAAO,GAAG,CAAC;AACZ,CAAC,CAAC;AACF;AACA,EAAE,CAAC,GAAG,CAAC,GAAG,GAAG,UAAU,MAAM,EAAE;AAC/B,CAAC,IAAI,CAAC,MAAM,EAAE;AACd,EAAE,OAAO,IAAI,CAAC;AACd,EAAE;AACF;AACA,CAAC,IAAI,GAAG,GAAG,8KAA8K,CAAC;AAC1L,CAAC,IAAI,KAAK,GAAG,MAAM,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;AAC/B;AACA,CAAC,IAAI,KAAK,EAAE;AACZ,EAAE,IAAI,KAAK,GAAG,UAAU,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC;AACnC,EAAE,IAAI,CAAC,GAAG,CAAC,CAAC,UAAU,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,GAAG,GAAG,IAAI,GAAG,IAAI,GAAG,CAAC;AACrD,EAAE,IAAI,CAAC,GAAG,KAAK,CAAC,UAAU,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,GAAG,CAAC,CAAC;AAC9C,EAAE,IAAI,CAAC,GAAG,KAAK,CAAC,UAAU,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,GAAG,CAAC,CAAC;AAC9C,EAAE,IAAI,CAAC,GAAG,KAAK,CAAC,KAAK,CAAC,KAAK,CAAC,GAAG,CAAC,GAAG,KAAK,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;AAChD;AACA,EAAE,OAAO,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;AACtB,EAAE;AACF;AACA,CAAC,OAAO,IAAI,CAAC;AACb,CAAC,CAAC;AACF;AACA,EAAE,CAAC,GAAG,CAAC,GAAG,GAAG,UAAU,MAAM,EAAE;AAC/B,CAAC,IAAI,CAAC,MAAM,EAAE;AACd,EAAE,OAAO,IAAI,CAAC;AACd,EAAE;AACF;AACA,CAAC,IAAI,GAAG,GAAG,qKAAqK,CAAC;AACjL,CAAC,IAAI,KAAK,GAAG,MAAM,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;AAC/B;AACA,CAAC,IAAI,KAAK,EAAE;AACZ,EAAE,IAAI,KAAK,GAAG,UAAU,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC;AACnC,EAAE,IAAI,CAAC,GAAG,CAAC,CAAC,UAAU,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,GAAG,GAAG,IAAI,GAAG,IAAI,GAAG,CAAC;AACrD,EAAE,IAAI,CAAC,GAAG,KAAK,CAAC,UAAU,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,GAAG,CAAC,CAAC;AAC9C,EAAE,IAAI,CAAC,GAAG,KAAK,CAAC,UAAU,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,GAAG,CAAC,CAAC;AAC9C,EAAE,IAAI,CAAC,GAAG,KAAK,CAAC,KAAK,CAAC,KAAK,CAAC,GAAG,CAAC,GAAG,KAAK,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;AAChD,EAAE,OAAO,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;AACtB,EAAE;AACF;AACA,CAAC,OAAO,IAAI,CAAC;AACb,CAAC,CAAC;AACF;AACA,EAAE,CAAC,EAAE,CAAC,GAAG,GAAG,YAAY;AACxB,CAAC,IAAI,IAAI,GAAGC,aAAO,CAAC,SAAS,CAAC,CAAC;AAC/B;AACA,CAAC;AACD,EAAE,GAAG;AACL,EAAE,SAAS,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;AACpB,EAAE,SAAS,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;AACpB,EAAE,SAAS,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;AACpB,GAAG,IAAI,CAAC,CAAC,CAAC,GAAG,CAAC;AACd,MAAM,SAAS,CAAC,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,CAAC,GAAG,GAAG,CAAC,CAAC;AAC1C,KAAK,EAAE,CAAC;AACR,GAAG;AACH,CAAC,CAAC;AACF;AACA,EAAE,CAAC,EAAE,CAAC,GAAG,GAAG,YAAY;AACxB,CAAC,IAAI,IAAI,GAAGA,aAAO,CAAC,SAAS,CAAC,CAAC;AAC/B;AACA,CAAC,OAAO,IAAI,CAAC,MAAM,GAAG,CAAC,IAAI,IAAI,CAAC,CAAC,CAAC,KAAK,CAAC;AACxC,IAAI,MAAM,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,GAAG,IAAI,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,GAAG,IAAI,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,GAAG,GAAG;AAChG,IAAI,OAAO,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,GAAG,IAAI,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,GAAG,IAAI,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,GAAG,IAAI,GAAG,IAAI,CAAC,CAAC,CAAC,GAAG,GAAG,CAAC;AACnH,CAAC,CAAC;AACF;AACA,EAAE,CAAC,EAAE,CAAC,GAAG,CAAC,OAAO,GAAG,YAAY;AAChC,CAAC,IAAI,IAAI,GAAGA,aAAO,CAAC,SAAS,CAAC,CAAC;AAC/B;AACA,CAAC,IAAI,CAAC,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,CAAC,GAAG,GAAG,GAAG,GAAG,CAAC,CAAC;AACzC,CAAC,IAAI,CAAC,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,CAAC,GAAG,GAAG,GAAG,GAAG,CAAC,CAAC;AACzC,CAAC,IAAI,CAAC,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,CAAC,GAAG,GAAG,GAAG,GAAG,CAAC,CAAC;AACzC;AACA,CAAC,OAAO,IAAI,CAAC,MAAM,GAAG,CAAC,IAAI,IAAI,CAAC,CAAC,CAAC,KAAK,CAAC;AACxC,IAAI,MAAM,GAAG,CAAC,GAAG,KAAK,GAAG,CAAC,GAAG,KAAK,GAAG,CAAC,GAAG,IAAI;AAC7C,IAAI,OAAO,GAAG,CAAC,GAAG,KAAK,GAAG,CAAC,GAAG,KAAK,GAAG,CAAC,GAAG,KAAK,GAAG,IAAI,CAAC,CAAC,CAAC,GAAG,GAAG,CAAC;AAChE,CAAC,CAAC;AACF;AACA,EAAE,CAAC,EAAE,CAAC,GAAG,GAAG,YAAY;AACxB,CAAC,IAAI,IAAI,GAAGA,aAAO,CAAC,SAAS,CAAC,CAAC;AAC/B,CAAC,OAAO,IAAI,CAAC,MAAM,GAAG,CAAC,IAAI,IAAI,CAAC,CAAC,CAAC,KAAK,CAAC;AACxC,IAAI,MAAM,GAAG,IAAI,CAAC,CAAC,CAAC,GAAG,IAAI,GAAG,IAAI,CAAC,CAAC,CAAC,GAAG,KAAK,GAAG,IAAI,CAAC,CAAC,CAAC,GAAG,IAAI;AAC9D,IAAI,OAAO,GAAG,IAAI,CAAC,CAAC,CAAC,GAAG,IAAI,GAAG,IAAI,CAAC,CAAC,CAAC,GAAG,KAAK,GAAG,IAAI,CAAC,CAAC,CAAC,GAAG,KAAK,GAAG,IAAI,CAAC,CAAC,CAAC,GAAG,GAAG,CAAC;AACjF,CAAC,CAAC;AACF;AACA;AACA;AACA,EAAE,CAAC,EAAE,CAAC,GAAG,GAAG,YAAY;AACxB,CAAC,IAAI,IAAI,GAAGA,aAAO,CAAC,SAAS,CAAC,CAAC;AAC/B;AACA,CAAC,IAAI,CAAC,GAAG,EAAE,CAAC;AACZ,CAAC,IAAI,IAAI,CAAC,MAAM,IAAI,CAAC,IAAI,IAAI,CAAC,CAAC,CAAC,KAAK,CAAC,EAAE;AACxC,EAAE,CAAC,GAAG,IAAI,GAAG,IAAI,CAAC,CAAC,CAAC,CAAC;AACrB,EAAE;AACF;AACA,CAAC,OAAO,MAAM,GAAG,IAAI,CAAC,CAAC,CAAC,GAAG,IAAI,GAAG,IAAI,CAAC,CAAC,CAAC,GAAG,KAAK,GAAG,IAAI,CAAC,CAAC,CAAC,GAAG,GAAG,GAAG,CAAC,GAAG,GAAG,CAAC;AAC5E,CAAC,CAAC;AACF;AACA,EAAE,CAAC,EAAE,CAAC,OAAO,GAAG,UAAU,GAAG,EAAE;AAC/B,CAAC,OAAO,YAAY,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;AACtC,CAAC,CAAC;AACF;AACA;AACA,SAAS,KAAK,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE;AAC9B,CAAC,OAAO,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,GAAG,EAAE,GAAG,CAAC,EAAE,GAAG,CAAC,CAAC;AAC1C,CAAC;AACD;AACA,SAAS,SAAS,CAAC,GAAG,EAAE;AACxB,CAAC,IAAI,GAAG,GAAG,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC,WAAW,EAAE,CAAC;AACtD,CAAC,OAAO,CAAC,GAAG,CAAC,MAAM,GAAG,CAAC,IAAI,GAAG,GAAG,GAAG,GAAG,GAAG,CAAC;AAC3C,CAAA;;;;;AC/OA,IAAA,SAAc,GAAG;AACjB,CAAC,WAAW,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC;AAC7B,CAAC,cAAc,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC;AAChC,CAAC,MAAM,EAAE,CAAC,CAAC,EAAE,GAAG,EAAE,GAAG,CAAC;AACtB,CAAC,YAAY,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC;AAC9B,CAAC,OAAO,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC;AACzB,CAAC,OAAO,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC;AACzB,CAAC,QAAQ,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC;AAC1B,CAAC,OAAO,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;AACnB,CAAC,gBAAgB,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC;AAClC,CAAC,MAAM,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,GAAG,CAAC;AACpB,CAAC,YAAY,EAAE,CAAC,GAAG,EAAE,EAAE,EAAE,GAAG,CAAC;AAC7B,CAAC,OAAO,EAAE,CAAC,GAAG,EAAE,EAAE,EAAE,EAAE,CAAC;AACvB,CAAC,WAAW,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC;AAC7B,CAAC,WAAW,EAAE,CAAC,EAAE,EAAE,GAAG,EAAE,GAAG,CAAC;AAC5B,CAAC,YAAY,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,CAAC,CAAC;AAC5B,CAAC,WAAW,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,EAAE,CAAC;AAC5B,CAAC,OAAO,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,EAAE,CAAC;AACxB,CAAC,gBAAgB,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC;AAClC,CAAC,UAAU,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC;AAC5B,CAAC,SAAS,EAAE,CAAC,GAAG,EAAE,EAAE,EAAE,EAAE,CAAC;AACzB,CAAC,MAAM,EAAE,CAAC,CAAC,EAAE,GAAG,EAAE,GAAG,CAAC;AACtB,CAAC,UAAU,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,GAAG,CAAC;AACxB,CAAC,UAAU,EAAE,CAAC,CAAC,EAAE,GAAG,EAAE,GAAG,CAAC;AAC1B,CAAC,eAAe,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,EAAE,CAAC;AAChC,CAAC,UAAU,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC;AAC5B,CAAC,WAAW,EAAE,CAAC,CAAC,EAAE,GAAG,EAAE,CAAC,CAAC;AACzB,CAAC,UAAU,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC;AAC5B,CAAC,WAAW,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC;AAC7B,CAAC,aAAa,EAAE,CAAC,GAAG,EAAE,CAAC,EAAE,GAAG,CAAC;AAC7B,CAAC,gBAAgB,EAAE,CAAC,EAAE,EAAE,GAAG,EAAE,EAAE,CAAC;AAChC,CAAC,YAAY,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,CAAC,CAAC;AAC5B,CAAC,YAAY,EAAE,CAAC,GAAG,EAAE,EAAE,EAAE,GAAG,CAAC;AAC7B,CAAC,SAAS,EAAE,CAAC,GAAG,EAAE,CAAC,EAAE,CAAC,CAAC;AACvB,CAAC,YAAY,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC;AAC9B,CAAC,cAAc,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC;AAChC,CAAC,eAAe,EAAE,CAAC,EAAE,EAAE,EAAE,EAAE,GAAG,CAAC;AAC/B,CAAC,eAAe,EAAE,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC;AAC9B,CAAC,eAAe,EAAE,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC;AAC9B,CAAC,eAAe,EAAE,CAAC,CAAC,EAAE,GAAG,EAAE,GAAG,CAAC;AAC/B,CAAC,YAAY,EAAE,CAAC,GAAG,EAAE,CAAC,EAAE,GAAG,CAAC;AAC5B,CAAC,UAAU,EAAE,CAAC,GAAG,EAAE,EAAE,EAAE,GAAG,CAAC;AAC3B,CAAC,aAAa,EAAE,CAAC,CAAC,EAAE,GAAG,EAAE,GAAG,CAAC;AAC7B,CAAC,SAAS,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC;AAC3B,CAAC,SAAS,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC;AAC3B,CAAC,YAAY,EAAE,CAAC,EAAE,EAAE,GAAG,EAAE,GAAG,CAAC;AAC7B,CAAC,WAAW,EAAE,CAAC,GAAG,EAAE,EAAE,EAAE,EAAE,CAAC;AAC3B,CAAC,aAAa,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC;AAC/B,CAAC,aAAa,EAAE,CAAC,EAAE,EAAE,GAAG,EAAE,EAAE,CAAC;AAC7B,CAAC,SAAS,EAAE,CAAC,GAAG,EAAE,CAAC,EAAE,GAAG,CAAC;AACzB,CAAC,WAAW,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC;AAC7B,CAAC,YAAY,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC;AAC9B,CAAC,MAAM,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,CAAC,CAAC;AACtB,CAAC,WAAW,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,EAAE,CAAC;AAC5B,CAAC,MAAM,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC;AACxB,CAAC,OAAO,EAAE,CAAC,CAAC,EAAE,GAAG,EAAE,CAAC,CAAC;AACrB,CAAC,aAAa,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,EAAE,CAAC;AAC9B,CAAC,MAAM,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC;AACxB,CAAC,UAAU,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC;AAC5B,CAAC,SAAS,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC;AAC3B,CAAC,WAAW,EAAE,CAAC,GAAG,EAAE,EAAE,EAAE,EAAE,CAAC;AAC3B,CAAC,QAAQ,EAAE,CAAC,EAAE,EAAE,CAAC,EAAE,GAAG,CAAC;AACvB,CAAC,OAAO,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC;AACzB,CAAC,OAAO,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC;AACzB,CAAC,UAAU,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC;AAC5B,CAAC,eAAe,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC;AACjC,CAAC,WAAW,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,CAAC,CAAC;AAC3B,CAAC,cAAc,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC;AAChC,CAAC,WAAW,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC;AAC7B,CAAC,YAAY,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC;AAC9B,CAAC,WAAW,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC;AAC7B,CAAC,sBAAsB,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC;AACxC,CAAC,WAAW,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC;AAC7B,CAAC,YAAY,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC;AAC9B,CAAC,WAAW,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC;AAC7B,CAAC,WAAW,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC;AAC7B,CAAC,aAAa,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC;AAC/B,CAAC,eAAe,EAAE,CAAC,EAAE,EAAE,GAAG,EAAE,GAAG,CAAC;AAChC,CAAC,cAAc,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC;AAChC,CAAC,gBAAgB,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC;AAClC,CAAC,gBAAgB,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC;AAClC,CAAC,gBAAgB,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC;AAClC,CAAC,aAAa,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC;AAC/B,CAAC,MAAM,EAAE,CAAC,CAAC,EAAE,GAAG,EAAE,CAAC,CAAC;AACpB,CAAC,WAAW,EAAE,CAAC,EAAE,EAAE,GAAG,EAAE,EAAE,CAAC;AAC3B,CAAC,OAAO,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC;AACzB,CAAC,SAAS,EAAE,CAAC,GAAG,EAAE,CAAC,EAAE,GAAG,CAAC;AACzB,CAAC,QAAQ,EAAE,CAAC,GAAG,EAAE,CAAC,EAAE,CAAC,CAAC;AACtB,CAAC,kBAAkB,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC;AACpC,CAAC,YAAY,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,GAAG,CAAC;AAC1B,CAAC,cAAc,EAAE,CAAC,GAAG,EAAE,EAAE,EAAE,GAAG,CAAC;AAC/B,CAAC,cAAc,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC;AAChC,CAAC,gBAAgB,EAAE,CAAC,EAAE,EAAE,GAAG,EAAE,GAAG,CAAC;AACjC,CAAC,iBAAiB,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC;AACnC,CAAC,mBAAmB,EAAE,CAAC,CAAC,EAAE,GAAG,EAAE,GAAG,CAAC;AACnC,CAAC,iBAAiB,EAAE,CAAC,EAAE,EAAE,GAAG,EAAE,GAAG,CAAC;AAClC,CAAC,iBAAiB,EAAE,CAAC,GAAG,EAAE,EAAE,EAAE,GAAG,CAAC;AAClC,CAAC,cAAc,EAAE,CAAC,EAAE,EAAE,EAAE,EAAE,GAAG,CAAC;AAC9B,CAAC,WAAW,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC;AAC7B,CAAC,WAAW,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC;AAC7B,CAAC,UAAU,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC;AAC5B,CAAC,aAAa,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC;AAC/B,CAAC,MAAM,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,GAAG,CAAC;AACpB,CAAC,SAAS,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC;AAC3B,CAAC,OAAO,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,CAAC,CAAC;AACvB,CAAC,WAAW,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,EAAE,CAAC;AAC5B,CAAC,QAAQ,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,CAAC,CAAC;AACxB,CAAC,WAAW,EAAE,CAAC,GAAG,EAAE,EAAE,EAAE,CAAC,CAAC;AAC1B,CAAC,QAAQ,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC;AAC1B,CAAC,eAAe,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC;AACjC,CAAC,WAAW,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC;AAC7B,CAAC,eAAe,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC;AACjC,CAAC,eAAe,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC;AACjC,CAAC,YAAY,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC;AAC9B,CAAC,WAAW,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC;AAC7B,CAAC,MAAM,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,EAAE,CAAC;AACvB,CAAC,MAAM,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC;AACxB,CAAC,MAAM,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC;AACxB,CAAC,YAAY,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC;AAC9B,CAAC,QAAQ,EAAE,CAAC,GAAG,EAAE,CAAC,EAAE,GAAG,CAAC;AACxB,CAAC,eAAe,EAAE,CAAC,GAAG,EAAE,EAAE,EAAE,GAAG,CAAC;AAChC,CAAC,KAAK,EAAE,CAAC,GAAG,EAAE,CAAC,EAAE,CAAC,CAAC;AACnB,CAAC,WAAW,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC;AAC7B,CAAC,WAAW,EAAE,CAAC,EAAE,EAAE,GAAG,EAAE,GAAG,CAAC;AAC5B,CAAC,aAAa,EAAE,CAAC,GAAG,EAAE,EAAE,EAAE,EAAE,CAAC;AAC7B,CAAC,QAAQ,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC;AAC1B,CAAC,YAAY,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,EAAE,CAAC;AAC7B,CAAC,UAAU,EAAE,CAAC,EAAE,EAAE,GAAG,EAAE,EAAE,CAAC;AAC1B,CAAC,UAAU,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC;AAC5B,CAAC,QAAQ,EAAE,CAAC,GAAG,EAAE,EAAE,EAAE,EAAE,CAAC;AACxB,CAAC,QAAQ,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC;AAC1B,CAAC,SAAS,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC;AAC3B,CAAC,WAAW,EAAE,CAAC,GAAG,EAAE,EAAE,EAAE,GAAG,CAAC;AAC5B,CAAC,WAAW,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC;AAC7B,CAAC,WAAW,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC;AAC7B,CAAC,MAAM,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC;AACxB,CAAC,aAAa,EAAE,CAAC,CAAC,EAAE,GAAG,EAAE,GAAG,CAAC;AAC7B,CAAC,WAAW,EAAE,CAAC,EAAE,EAAE,GAAG,EAAE,GAAG,CAAC;AAC5B,CAAC,KAAK,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC;AACvB,CAAC,MAAM,EAAE,CAAC,CAAC,EAAE,GAAG,EAAE,GAAG,CAAC;AACtB,CAAC,SAAS,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC;AAC3B,CAAC,QAAQ,EAAE,CAAC,GAAG,EAAE,EAAE,EAAE,EAAE,CAAC;AACxB,CAAC,WAAW,EAAE,CAAC,EAAE,EAAE,GAAG,EAAE,GAAG,CAAC;AAC5B,CAAC,QAAQ,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC;AAC1B,CAAC,OAAO,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC;AACzB,CAAC,OAAO,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC;AACzB,CAAC,YAAY,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC;AAC9B,CAAC,QAAQ,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,CAAC,CAAC;AACxB,CAAC,aAAa,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,EAAE,CAAC;AAC9B,CAAC;;;ACvJD;AACwC;AACxC;AACA;AACA;AACA;AACA;AACA,IAAI,eAAe,GAAG,EAAE,CAAC;AACzB,KAAK,IAAI,GAAG,IAAIC,SAAW,EAAE;AAC7B,CAAC,IAAIA,SAAW,CAAC,cAAc,CAAC,GAAG,CAAC,EAAE;AACtC,EAAE,eAAe,CAACA,SAAW,CAAC,GAAG,CAAC,CAAC,GAAG,GAAG,CAAC;AAC1C,EAAE;AACF,CAAC;AACD;AACA,IAAI,OAAO,GAAG,MAAA,CAAA,OAAc,GAAG;AAC/B,CAAC,GAAG,EAAE,CAAC,QAAQ,EAAE,CAAC,EAAE,MAAM,EAAE,KAAK,CAAC;AAClC,CAAC,GAAG,EAAE,CAAC,QAAQ,EAAE,CAAC,EAAE,MAAM,EAAE,KAAK,CAAC;AAClC,CAAC,GAAG,EAAE,CAAC,QAAQ,EAAE,CAAC,EAAE,MAAM,EAAE,KAAK,CAAC;AAClC,CAAC,GAAG,EAAE,CAAC,QAAQ,EAAE,CAAC,EAAE,MAAM,EAAE,KAAK,CAAC;AAClC,CAAC,IAAI,EAAE,CAAC,QAAQ,EAAE,CAAC,EAAE,MAAM,EAAE,MAAM,CAAC;AACpC,CAAC,GAAG,EAAE,CAAC,QAAQ,EAAE,CAAC,EAAE,MAAM,EAAE,KAAK,CAAC;AAClC,CAAC,GAAG,EAAE,CAAC,QAAQ,EAAE,CAAC,EAAE,MAAM,EAAE,KAAK,CAAC;AAClC,CAAC,GAAG,EAAE,CAAC,QAAQ,EAAE,CAAC,EAAE,MAAM,EAAE,KAAK,CAAC;AAClC,CAAC,GAAG,EAAE,CAAC,QAAQ,EAAE,CAAC,EAAE,MAAM,EAAE,CAAC,KAAK,CAAC,CAAC;AACpC,CAAC,OAAO,EAAE,CAAC,QAAQ,EAAE,CAAC,EAAE,MAAM,EAAE,CAAC,SAAS,CAAC,CAAC;AAC5C,CAAC,MAAM,EAAE,CAAC,QAAQ,EAAE,CAAC,EAAE,MAAM,EAAE,CAAC,QAAQ,CAAC,CAAC;AAC1C,CAAC,OAAO,EAAE,CAAC,QAAQ,EAAE,CAAC,EAAE,MAAM,EAAE,CAAC,SAAS,CAAC,CAAC;AAC5C,CAAC,GAAG,EAAE,CAAC,QAAQ,EAAE,CAAC,EAAE,MAAM,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC,CAAC;AAC5C,CAAC,KAAK,EAAE,CAAC,QAAQ,EAAE,CAAC,EAAE,MAAM,EAAE,CAAC,KAAK,EAAE,KAAK,EAAE,KAAK,CAAC,CAAC;AACpD,CAAC,IAAI,EAAE,CAAC,QAAQ,EAAE,CAAC,EAAE,MAAM,EAAE,CAAC,MAAM,CAAC,CAAC;AACtC,CAAC,CAAC;AACF;AACA;AACA,KAAK,IAAI,KAAK,IAAI,OAAO,EAAE;AAC3B,CAAC,IAAI,OAAO,CAAC,cAAc,CAAC,KAAK,CAAC,EAAE;AACpC,EAAE,IAAI,EAAE,UAAU,IAAI,OAAO,CAAC,KAAK,CAAC,CAAC,EAAE;AACvC,GAAG,MAAM,IAAI,KAAK,CAAC,6BAA6B,GAAG,KAAK,CAAC,CAAC;AAC1D,GAAG;AACH;AACA,EAAE,IAAI,EAAE,QAAQ,IAAI,OAAO,CAAC,KAAK,CAAC,CAAC,EAAE;AACrC,GAAG,MAAM,IAAI,KAAK,CAAC,mCAAmC,GAAG,KAAK,CAAC,CAAC;AAChE,GAAG;AACH;AACA,EAAE,IAAI,OAAO,CAAC,KAAK,CAAC,CAAC,MAAM,CAAC,MAAM,KAAK,OAAO,CAAC,KAAK,CAAC,CAAC,QAAQ,EAAE;AAChE,GAAG,MAAM,IAAI,KAAK,CAAC,qCAAqC,GAAG,KAAK,CAAC,CAAC;AAClE,GAAG;AACH;AACA,EAAE,IAAI,QAAQ,GAAG,OAAO,CAAC,KAAK,CAAC,CAAC,QAAQ,CAAC;AACzC,EAAE,IAAI,MAAM,GAAG,OAAO,CAAC,KAAK,CAAC,CAAC,MAAM,CAAC;AACrC,EAAE,OAAO,OAAO,CAAC,KAAK,CAAC,CAAC,QAAQ,CAAC;AACjC,EAAE,OAAO,OAAO,CAAC,KAAK,CAAC,CAAC,MAAM,CAAC;AAC/B,EAAE,MAAM,CAAC,cAAc,CAAC,OAAO,CAAC,KAAK,CAAC,EAAE,UAAU,EAAE,CAAC,KAAK,EAAE,QAAQ,CAAC,CAAC,CAAC;AACvE,EAAE,MAAM,CAAC,cAAc,CAAC,OAAO,CAAC,KAAK,CAAC,EAAE,QAAQ,EAAE,CAAC,KAAK,EAAE,MAAM,CAAC,CAAC,CAAC;AACnE,EAAE;AACF,CAAC;AACD;AACA,OAAO,CAAC,GAAG,CAAC,GAAG,GAAG,UAAU,GAAG,EAAE;AACjC,CAAC,IAAI,CAAC,GAAG,GAAG,CAAC,CAAC,CAAC,GAAG,GAAG,CAAC;AACtB,CAAC,IAAI,CAAC,GAAG,GAAG,CAAC,CAAC,CAAC,GAAG,GAAG,CAAC;AACtB,CAAC,IAAI,CAAC,GAAG,GAAG,CAAC,CAAC,CAAC,GAAG,GAAG,CAAC;AACtB,CAAC,IAAI,GAAG,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;AAC7B,CAAC,IAAI,GAAG,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;AAC7B,CAAC,IAAI,KAAK,GAAG,GAAG,GAAG,GAAG,CAAC;AACvB,CAAC,IAAI,CAAC,CAAC;AACP,CAAC,IAAI,CAAC,CAAC;AACP,CAAC,IAAI,CAAC,CAAC;AACP;AACA,CAAC,IAAI,GAAG,KAAK,GAAG,EAAE;AAClB,EAAE,CAAC,GAAG,CAAC,CAAC;AACR,EAAE,MAAM,IAAI,CAAC,KAAK,GAAG,EAAE;AACvB,EAAE,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,IAAI,KAAK,CAAC;AACtB,EAAE,MAAM,IAAI,CAAC,KAAK,GAAG,EAAE;AACvB,EAAE,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,IAAI,KAAK,CAAC;AAC1B,EAAE,MAAM,IAAI,CAAC,KAAK,GAAG,EAAE;AACvB,EAAE,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,IAAI,KAAK,CAAC;AAC1B,EAAE;AACF;AACA,CAAC,CAAC,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,GAAG,EAAE,EAAE,GAAG,CAAC,CAAC;AAC3B;AACA,CAAC,IAAI,CAAC,GAAG,CAAC,EAAE;AACZ,EAAE,CAAC,IAAI,GAAG,CAAC;AACX,EAAE;AACF;AACA,CAAC,CAAC,GAAG,CAAC,GAAG,GAAG,GAAG,IAAI,CAAC,CAAC;AACrB;AACA,CAAC,IAAI,GAAG,KAAK,GAAG,EAAE;AAClB,EAAE,CAAC,GAAG,CAAC,CAAC;AACR,EAAE,MAAM,IAAI,CAAC,IAAI,GAAG,EAAE;AACtB,EAAE,CAAC,GAAG,KAAK,IAAI,GAAG,GAAG,GAAG,CAAC,CAAC;AAC1B,EAAE,MAAM;AACR,EAAE,CAAC,GAAG,KAAK,IAAI,CAAC,GAAG,GAAG,GAAG,GAAG,CAAC,CAAC;AAC9B,EAAE;AACF;AACA,CAAC,OAAO,CAAC,CAAC,EAAE,CAAC,GAAG,GAAG,EAAE,CAAC,GAAG,GAAG,CAAC,CAAC;AAC9B,CAAC,CAAC;AACF;AACA,OAAO,CAAC,GAAG,CAAC,GAAG,GAAG,UAAU,GAAG,EAAE;AACjC,CAAC,IAAI,IAAI,CAAC;AACV,CAAC,IAAI,IAAI,CAAC;AACV,CAAC,IAAI,IAAI,CAAC;AACV,CAAC,IAAI,CAAC,CAAC;AACP,CAAC,IAAI,CAAC,CAAC;AACP;AACA,CAAC,IAAI,CAAC,GAAG,GAAG,CAAC,CAAC,CAAC,GAAG,GAAG,CAAC;AACtB,CAAC,IAAI,CAAC,GAAG,GAAG,CAAC,CAAC,CAAC,GAAG,GAAG,CAAC;AACtB,CAAC,IAAI,CAAC,GAAG,GAAG,CAAC,CAAC,CAAC,GAAG,GAAG,CAAC;AACtB,CAAC,IAAI,CAAC,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;AAC3B,CAAC,IAAI,IAAI,GAAG,CAAC,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;AAClC,CAAC,IAAI,KAAK,GAAG,UAAU,CAAC,EAAE;AAC1B,EAAE,OAAO,CAAC,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,IAAI,GAAG,CAAC,GAAG,CAAC,CAAC;AACpC,EAAE,CAAC;AACH;AACA,CAAC,IAAI,IAAI,KAAK,CAAC,EAAE;AACjB,EAAE,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;AACZ,EAAE,MAAM;AACR,EAAE,CAAC,GAAG,IAAI,GAAG,CAAC,CAAC;AACf,EAAE,IAAI,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC;AAClB,EAAE,IAAI,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC;AAClB,EAAE,IAAI,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC;AAClB;AACA,EAAE,IAAI,CAAC,KAAK,CAAC,EAAE;AACf,GAAG,CAAC,GAAG,IAAI,GAAG,IAAI,CAAC;AACnB,GAAG,MAAM,IAAI,CAAC,KAAK,CAAC,EAAE;AACtB,GAAG,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,IAAI,IAAI,GAAG,IAAI,CAAC;AAC7B,GAAG,MAAM,IAAI,CAAC,KAAK,CAAC,EAAE;AACtB,GAAG,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,IAAI,IAAI,GAAG,IAAI,CAAC;AAC7B,GAAG;AACH,EAAE,IAAI,CAAC,GAAG,CAAC,EAAE;AACb,GAAG,CAAC,IAAI,CAAC,CAAC;AACV,GAAG,MAAM,IAAI,CAAC,GAAG,CAAC,EAAE;AACpB,GAAG,CAAC,IAAI,CAAC,CAAC;AACV,GAAG;AACH,EAAE;AACF;AACA,CAAC,OAAO;AACR,EAAE,CAAC,GAAG,GAAG;AACT,EAAE,CAAC,GAAG,GAAG;AACT,EAAE,CAAC,GAAG,GAAG;AACT,EAAE,CAAC;AACH,CAAC,CAAC;AACF;AACA,OAAO,CAAC,GAAG,CAAC,GAAG,GAAG,UAAU,GAAG,EAAE;AACjC,CAAC,IAAI,CAAC,GAAG,GAAG,CAAC,CAAC,CAAC,CAAC;AAChB,CAAC,IAAI,CAAC,GAAG,GAAG,CAAC,CAAC,CAAC,CAAC;AAChB,CAAC,IAAI,CAAC,GAAG,GAAG,CAAC,CAAC,CAAC,CAAC;AAChB,CAAC,IAAI,CAAC,GAAG,OAAO,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;AACjC,CAAC,IAAI,CAAC,GAAG,CAAC,GAAG,GAAG,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;AAC/C;AACA,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,GAAG,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;AAC/C;AACA,CAAC,OAAO,CAAC,CAAC,EAAE,CAAC,GAAG,GAAG,EAAE,CAAC,GAAG,GAAG,CAAC,CAAC;AAC9B,CAAC,CAAC;AACF;AACA,OAAO,CAAC,GAAG,CAAC,IAAI,GAAG,UAAU,GAAG,EAAE;AAClC,CAAC,IAAI,CAAC,GAAG,GAAG,CAAC,CAAC,CAAC,GAAG,GAAG,CAAC;AACtB,CAAC,IAAI,CAAC,GAAG,GAAG,CAAC,CAAC,CAAC,GAAG,GAAG,CAAC;AACtB,CAAC,IAAI,CAAC,GAAG,GAAG,CAAC,CAAC,CAAC,GAAG,GAAG,CAAC;AACtB,CAAC,IAAI,CAAC,CAAC;AACP,CAAC,IAAI,CAAC,CAAC;AACP,CAAC,IAAI,CAAC,CAAC;AACP,CAAC,IAAI,CAAC,CAAC;AACP;AACA,CAAC,CAAC,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC,CAAC;AACnC,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,CAAC;AAChC,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,CAAC;AAChC,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,CAAC;AAChC;AACA,CAAC,OAAO,CAAC,CAAC,GAAG,GAAG,EAAE,CAAC,GAAG,GAAG,EAAE,CAAC,GAAG,GAAG,EAAE,CAAC,GAAG,GAAG,CAAC,CAAC;AAC7C,CAAC,CAAC;AACF;AACA;AACA;AACA;AACA,SAAS,mBAAmB,CAAC,CAAC,EAAE,CAAC,EAAE;AACnC,CAAC;AACD,EAAE,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;AAC1B,EAAE,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;AAC1B,EAAE,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;AAC1B,GAAG;AACH,CAAC;AACD;AACA,OAAO,CAAC,GAAG,CAAC,OAAO,GAAG,UAAU,GAAG,EAAE;AACrC,CAAC,IAAI,QAAQ,GAAG,eAAe,CAAC,GAAG,CAAC,CAAC;AACrC,CAAC,IAAI,QAAQ,EAAE;AACf,EAAE,OAAO,QAAQ,CAAC;AAClB,EAAE;AACF;AACA,CAAC,IAAI,sBAAsB,GAAG,QAAQ,CAAC;AACvC,CAAC,IAAI,qBAAqB,CAAC;AAC3B;AACA,CAAC,KAAK,IAAI,OAAO,IAAIA,SAAW,EAAE;AAClC,EAAE,IAAIA,SAAW,CAAC,cAAc,CAAC,OAAO,CAAC,EAAE;AAC3C,GAAG,IAAI,KAAK,GAAGA,SAAW,CAAC,OAAO,CAAC,CAAC;AACpC;AACA;AACA,GAAG,IAAI,QAAQ,GAAG,mBAAmB,CAAC,GAAG,EAAE,KAAK,CAAC,CAAC;AAClD;AACA;AACA,GAAG,IAAI,QAAQ,GAAG,sBAAsB,EAAE;AAC1C,IAAI,sBAAsB,GAAG,QAAQ,CAAC;AACtC,IAAI,qBAAqB,GAAG,OAAO,CAAC;AACpC,IAAI;AACJ,GAAG;AACH,EAAE;AACF;AACA,CAAC,OAAO,qBAAqB,CAAC;AAC9B,CAAC,CAAC;AACF;AACA,OAAO,CAAC,OAAO,CAAC,GAAG,GAAG,UAAU,OAAO,EAAE;AACzC,CAAC,OAAOA,SAAW,CAAC,OAAO,CAAC,CAAC;AAC7B,CAAC,CAAC;AACF;AACA,OAAO,CAAC,GAAG,CAAC,GAAG,GAAG,UAAU,GAAG,EAAE;AACjC,CAAC,IAAI,CAAC,GAAG,GAAG,CAAC,CAAC,CAAC,GAAG,GAAG,CAAC;AACtB,CAAC,IAAI,CAAC,GAAG,GAAG,CAAC,CAAC,CAAC,GAAG,GAAG,CAAC;AACtB,CAAC,IAAI,CAAC,GAAG,GAAG,CAAC,CAAC,CAAC,GAAG,GAAG,CAAC;AACtB;AACA;AACA,CAAC,CAAC,GAAG,CAAC,GAAG,OAAO,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC,CAAC,GAAG,KAAK,IAAI,KAAK,GAAG,GAAG,CAAC,IAAI,CAAC,GAAG,KAAK,CAAC,CAAC;AACtE,CAAC,CAAC,GAAG,CAAC,GAAG,OAAO,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC,CAAC,GAAG,KAAK,IAAI,KAAK,GAAG,GAAG,CAAC,IAAI,CAAC,GAAG,KAAK,CAAC,CAAC;AACtE,CAAC,CAAC,GAAG,CAAC,GAAG,OAAO,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC,CAAC,GAAG,KAAK,IAAI,KAAK,GAAG,GAAG,CAAC,IAAI,CAAC,GAAG,KAAK,CAAC,CAAC;AACtE;AACA,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,GAAG,MAAM,KAAK,CAAC,GAAG,MAAM,CAAC,IAAI,CAAC,GAAG,MAAM,CAAC,CAAC;AACpD,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,GAAG,MAAM,KAAK,CAAC,GAAG,MAAM,CAAC,IAAI,CAAC,GAAG,MAAM,CAAC,CAAC;AACpD,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,GAAG,MAAM,KAAK,CAAC,GAAG,MAAM,CAAC,IAAI,CAAC,GAAG,MAAM,CAAC,CAAC;AACpD;AACA,CAAC,OAAO,CAAC,CAAC,GAAG,GAAG,EAAE,CAAC,GAAG,GAAG,EAAE,CAAC,GAAG,GAAG,CAAC,CAAC;AACpC,CAAC,CAAC;AACF;AACA,OAAO,CAAC,GAAG,CAAC,GAAG,GAAG,UAAU,GAAG,EAAE;AACjC,CAAC,IAAI,GAAG,GAAG,OAAO,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;AAChC,CAAC,IAAI,CAAC,GAAG,GAAG,CAAC,CAAC,CAAC,CAAC;AAChB,CAAC,IAAI,CAAC,GAAG,GAAG,CAAC,CAAC,CAAC,CAAC;AAChB,CAAC,IAAI,CAAC,GAAG,GAAG,CAAC,CAAC,CAAC,CAAC;AAChB,CAAC,IAAI,CAAC,CAAC;AACP,CAAC,IAAI,CAAC,CAAC;AACP,CAAC,IAAI,CAAC,CAAC;AACP;AACA,CAAC,CAAC,IAAI,MAAM,CAAC;AACb,CAAC,CAAC,IAAI,GAAG,CAAC;AACV,CAAC,CAAC,IAAI,OAAO,CAAC;AACd;AACA,CAAC,CAAC,GAAG,CAAC,GAAG,QAAQ,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,KAAK,GAAG,CAAC,KAAK,EAAE,GAAG,GAAG,CAAC,CAAC;AAClE,CAAC,CAAC,GAAG,CAAC,GAAG,QAAQ,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,KAAK,GAAG,CAAC,KAAK,EAAE,GAAG,GAAG,CAAC,CAAC;AAClE,CAAC,CAAC,GAAG,CAAC,GAAG,QAAQ,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,KAAK,GAAG,CAAC,KAAK,EAAE,GAAG,GAAG,CAAC,CAAC;AAClE;AACA,CAAC,CAAC,GAAG,CAAC,GAAG,GAAG,CAAC,IAAI,EAAE,CAAC;AACpB,CAAC,CAAC,GAAG,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC;AACnB,CAAC,CAAC,GAAG,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC;AACnB;AACA,CAAC,OAAO,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;AAClB,CAAC,CAAC;AACF;AACA,OAAO,CAAC,GAAG,CAAC,GAAG,GAAG,UAAU,GAAG,EAAE;AACjC,CAAC,IAAI,CAAC,GAAG,GAAG,CAAC,CAAC,CAAC,GAAG,GAAG,CAAC;AACtB,CAAC,IAAI,CAAC,GAAG,GAAG,CAAC,CAAC,CAAC,GAAG,GAAG,CAAC;AACtB,CAAC,IAAI,CAAC,GAAG,GAAG,CAAC,CAAC,CAAC,GAAG,GAAG,CAAC;AACtB,CAAC,IAAI,EAAE,CAAC;AACR,CAAC,IAAI,EAAE,CAAC;AACR,CAAC,IAAI,EAAE,CAAC;AACR,CAAC,IAAI,GAAG,CAAC;AACT,CAAC,IAAI,GAAG,CAAC;AACT;AACA,CAAC,IAAI,CAAC,KAAK,CAAC,EAAE;AACd,EAAE,GAAG,GAAG,CAAC,GAAG,GAAG,CAAC;AAChB,EAAE,OAAO,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC,CAAC;AACzB,EAAE;AACF;AACA,CAAC,IAAI,CAAC,GAAG,GAAG,EAAE;AACd,EAAE,EAAE,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC;AACnB,EAAE,MAAM;AACR,EAAE,EAAE,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;AACrB,EAAE;AACF;AACA,CAAC,EAAE,GAAG,CAAC,GAAG,CAAC,GAAG,EAAE,CAAC;AACjB;AACA,CAAC,GAAG,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;AACjB,CAAC,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,EAAE,EAAE;AAC7B,EAAE,EAAE,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,EAAE,CAAC,GAAG,CAAC,CAAC,CAAC;AAC5B,EAAE,IAAI,EAAE,GAAG,CAAC,EAAE;AACd,GAAG,EAAE,EAAE,CAAC;AACR,GAAG;AACH,EAAE,IAAI,EAAE,GAAG,CAAC,EAAE;AACd,GAAG,EAAE,EAAE,CAAC;AACR,GAAG;AACH;AACA,EAAE,IAAI,CAAC,GAAG,EAAE,GAAG,CAAC,EAAE;AAClB,GAAG,GAAG,GAAG,EAAE,GAAG,CAAC,EAAE,GAAG,EAAE,IAAI,CAAC,GAAG,EAAE,CAAC;AACjC,GAAG,MAAM,IAAI,CAAC,GAAG,EAAE,GAAG,CAAC,EAAE;AACzB,GAAG,GAAG,GAAG,EAAE,CAAC;AACZ,GAAG,MAAM,IAAI,CAAC,GAAG,EAAE,GAAG,CAAC,EAAE;AACzB,GAAG,GAAG,GAAG,EAAE,GAAG,CAAC,EAAE,GAAG,EAAE,KAAK,CAAC,GAAG,CAAC,GAAG,EAAE,CAAC,GAAG,CAAC,CAAC;AAC3C,GAAG,MAAM;AACT,GAAG,GAAG,GAAG,EAAE,CAAC;AACZ,GAAG;AACH;AACA,EAAE,GAAG,CAAC,CAAC,CAAC,GAAG,GAAG,GAAG,GAAG,CAAC;AACrB,EAAE;AACF;AACA,CAAC,OAAO,GAAG,CAAC;AACZ,CAAC,CAAC;AACF;AACA,OAAO,CAAC,GAAG,CAAC,GAAG,GAAG,UAAU,GAAG,EAAE;AACjC,CAAC,IAAI,CAAC,GAAG,GAAG,CAAC,CAAC,CAAC,CAAC;AAChB,CAAC,IAAI,CAAC,GAAG,GAAG,CAAC,CAAC,CAAC,GAAG,GAAG,CAAC;AACtB,CAAC,IAAI,CAAC,GAAG,GAAG,CAAC,CAAC,CAAC,GAAG,GAAG,CAAC;AACtB,CAAC,IAAI,IAAI,GAAG,CAAC,CAAC;AACd,CAAC,IAAI,IAAI,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,IAAI,CAAC,CAAC;AAC9B,CAAC,IAAI,EAAE,CAAC;AACR,CAAC,IAAI,CAAC,CAAC;AACP;AACA,CAAC,CAAC,IAAI,CAAC,CAAC;AACR,CAAC,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;AAC3B,CAAC,IAAI,IAAI,IAAI,IAAI,CAAC,GAAG,IAAI,GAAG,CAAC,GAAG,IAAI,CAAC;AACrC,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;AACjB,CAAC,EAAE,GAAG,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,GAAG,IAAI,KAAK,IAAI,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC;AAC/D;AACA,CAAC,OAAO,CAAC,CAAC,EAAE,EAAE,GAAG,GAAG,EAAE,CAAC,GAAG,GAAG,CAAC,CAAC;AAC/B,CAAC,CAAC;AACF;AACA,OAAO,CAAC,GAAG,CAAC,GAAG,GAAG,UAAU,GAAG,EAAE;AACjC,CAAC,IAAI,CAAC,GAAG,GAAG,CAAC,CAAC,CAAC,GAAG,EAAE,CAAC;AACrB,CAAC,IAAI,CAAC,GAAG,GAAG,CAAC,CAAC,CAAC,GAAG,GAAG,CAAC;AACtB,CAAC,IAAI,CAAC,GAAG,GAAG,CAAC,CAAC,CAAC,GAAG,GAAG,CAAC;AACtB,CAAC,IAAI,EAAE,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;AAC5B;AACA,CAAC,IAAI,CAAC,GAAG,CAAC,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;AAC3B,CAAC,IAAI,CAAC,GAAG,GAAG,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC;AAC3B,CAAC,IAAI,CAAC,GAAG,GAAG,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;AACjC,CAAC,IAAI,CAAC,GAAG,GAAG,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;AACvC,CAAC,CAAC,IAAI,GAAG,CAAC;AACV;AACA,CAAC,QAAQ,EAAE;AACX,EAAE,KAAK,CAAC;AACR,GAAG,OAAO,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;AACpB,EAAE,KAAK,CAAC;AACR,GAAG,OAAO,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;AACpB,EAAE,KAAK,CAAC;AACR,GAAG,OAAO,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;AACpB,EAAE,KAAK,CAAC;AACR,GAAG,OAAO,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;AACpB,EAAE,KAAK,CAAC;AACR,GAAG,OAAO,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;AACpB,EAAE,KAAK,CAAC;AACR,GAAG,OAAO,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;AACpB,EAAE;AACF,CAAC,CAAC;AACF;AACA,OAAO,CAAC,GAAG,CAAC,GAAG,GAAG,UAAU,GAAG,EAAE;AACjC,CAAC,IAAI,CAAC,GAAG,GAAG,CAAC,CAAC,CAAC,CAAC;AAChB,CAAC,IAAI,CAAC,GAAG,GAAG,CAAC,CAAC,CAAC,GAAG,GAAG,CAAC;AACtB,CAAC,IAAI,CAAC,GAAG,GAAG,CAAC,CAAC,CAAC,GAAG,GAAG,CAAC;AACtB,CAAC,IAAI,IAAI,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,IAAI,CAAC,CAAC;AAC9B,CAAC,IAAI,IAAI,CAAC;AACV,CAAC,IAAI,EAAE,CAAC;AACR,CAAC,IAAI,CAAC,CAAC;AACP;AACA,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;AACjB,CAAC,IAAI,GAAG,CAAC,CAAC,GAAG,CAAC,IAAI,IAAI,CAAC;AACvB,CAAC,EAAE,GAAG,CAAC,GAAG,IAAI,CAAC;AACf,CAAC,EAAE,IAAI,CAAC,IAAI,IAAI,CAAC,IAAI,IAAI,GAAG,CAAC,GAAG,IAAI,CAAC;AACrC,CAAC,EAAE,GAAG,EAAE,IAAI,CAAC,CAAC;AACd,CAAC,CAAC,IAAI,CAAC,CAAC;AACR;AACA,CAAC,OAAO,CAAC,CAAC,EAAE,EAAE,GAAG,GAAG,EAAE,CAAC,GAAG,GAAG,CAAC,CAAC;AAC/B,CAAC,CAAC;AACF;AACA;AACA,OAAO,CAAC,GAAG,CAAC,GAAG,GAAG,UAAU,GAAG,EAAE;AACjC,CAAC,IAAI,CAAC,GAAG,GAAG,CAAC,CAAC,CAAC,GAAG,GAAG,CAAC;AACtB,CAAC,IAAI,EAAE,GAAG,GAAG,CAAC,CAAC,CAAC,GAAG,GAAG,CAAC;AACvB,CAAC,IAAI,EAAE,GAAG,GAAG,CAAC,CAAC,CAAC,GAAG,GAAG,CAAC;AACvB,CAAC,IAAI,KAAK,GAAG,EAAE,GAAG,EAAE,CAAC;AACrB,CAAC,IAAI,CAAC,CAAC;AACP,CAAC,IAAI,CAAC,CAAC;AACP,CAAC,IAAI,CAAC,CAAC;AACP,CAAC,IAAI,CAAC,CAAC;AACP;AACA;AACA,CAAC,IAAI,KAAK,GAAG,CAAC,EAAE;AAChB,EAAE,EAAE,IAAI,KAAK,CAAC;AACd,EAAE,EAAE,IAAI,KAAK,CAAC;AACd,EAAE;AACF;AACA,CAAC,CAAC,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC;AACvB,CAAC,CAAC,GAAG,CAAC,GAAG,EAAE,CAAC;AACZ,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;AACf;AACA,CAAC,IAAI,CAAC,CAAC,GAAG,IAAI,MAAM,CAAC,EAAE;AACvB,EAAE,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;AACZ,EAAE;AACF;AACA,CAAC,CAAC,GAAG,EAAE,GAAG,CAAC,IAAI,CAAC,GAAG,EAAE,CAAC,CAAC;AACvB;AACA,CAAC,IAAI,CAAC,CAAC;AACP,CAAC,IAAI,CAAC,CAAC;AACP,CAAC,IAAI,CAAC,CAAC;AACP,CAAC,QAAQ,CAAC;AACV,EAAE,QAAQ;AACV,EAAE,KAAK,CAAC,CAAC;AACT,EAAE,KAAK,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,GAAG,EAAE,CAAC,CAAC,MAAM;AACtC,EAAE,KAAK,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,GAAG,EAAE,CAAC,CAAC,MAAM;AACtC,EAAE,KAAK,CAAC,EAAE,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,MAAM;AACtC,EAAE,KAAK,CAAC,EAAE,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,MAAM;AACtC,EAAE,KAAK,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,MAAM;AACtC,EAAE,KAAK,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,MAAM;AACtC,EAAE;AACF;AACA,CAAC,OAAO,CAAC,CAAC,GAAG,GAAG,EAAE,CAAC,GAAG,GAAG,EAAE,CAAC,GAAG,GAAG,CAAC,CAAC;AACpC,CAAC,CAAC;AACF;AACA,OAAO,CAAC,IAAI,CAAC,GAAG,GAAG,UAAU,IAAI,EAAE;AACnC,CAAC,IAAI,CAAC,GAAG,IAAI,CAAC,CAAC,CAAC,GAAG,GAAG,CAAC;AACvB,CAAC,IAAI,CAAC,GAAG,IAAI,CAAC,CAAC,CAAC,GAAG,GAAG,CAAC;AACvB,CAAC,IAAI,CAAC,GAAG,IAAI,CAAC,CAAC,CAAC,GAAG,GAAG,CAAC;AACvB,CAAC,IAAI,CAAC,GAAG,IAAI,CAAC,CAAC,CAAC,GAAG,GAAG,CAAC;AACvB,CAAC,IAAI,CAAC,CAAC;AACP,CAAC,IAAI,CAAC,CAAC;AACP,CAAC,IAAI,CAAC,CAAC;AACP;AACA,CAAC,CAAC,GAAG,CAAC,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC;AACtC,CAAC,CAAC,GAAG,CAAC,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC;AACtC,CAAC,CAAC,GAAG,CAAC,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC;AACtC;AACA,CAAC,OAAO,CAAC,CAAC,GAAG,GAAG,EAAE,CAAC,GAAG,GAAG,EAAE,CAAC,GAAG,GAAG,CAAC,CAAC;AACpC,CAAC,CAAC;AACF;AACA,OAAO,CAAC,GAAG,CAAC,GAAG,GAAG,UAAU,GAAG,EAAE;AACjC,CAAC,IAAI,CAAC,GAAG,GAAG,CAAC,CAAC,CAAC,GAAG,GAAG,CAAC;AACtB,CAAC,IAAI,CAAC,GAAG,GAAG,CAAC,CAAC,CAAC,GAAG,GAAG,CAAC;AACtB,CAAC,IAAI,CAAC,GAAG,GAAG,CAAC,CAAC,CAAC,GAAG,GAAG,CAAC;AACtB,CAAC,IAAI,CAAC,CAAC;AACP,CAAC,IAAI,CAAC,CAAC;AACP,CAAC,IAAI,CAAC,CAAC;AACP;AACA,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,MAAM,KAAK,CAAC,GAAG,CAAC,MAAM,CAAC,IAAI,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC;AAClD,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,MAAM,KAAK,CAAC,GAAG,MAAM,CAAC,IAAI,CAAC,GAAG,MAAM,CAAC,CAAC;AACjD,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,MAAM,KAAK,CAAC,GAAG,CAAC,MAAM,CAAC,IAAI,CAAC,GAAG,MAAM,CAAC,CAAC;AACjD;AACA;AACA,CAAC,CAAC,GAAG,CAAC,GAAG,SAAS;AAClB,KAAK,CAAC,KAAK,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,GAAG,GAAG,GAAG,CAAC,IAAI,KAAK;AAC7C,IAAI,CAAC,GAAG,KAAK,CAAC;AACd;AACA,CAAC,CAAC,GAAG,CAAC,GAAG,SAAS;AAClB,KAAK,CAAC,KAAK,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,GAAG,GAAG,GAAG,CAAC,IAAI,KAAK;AAC7C,IAAI,CAAC,GAAG,KAAK,CAAC;AACd;AACA,CAAC,CAAC,GAAG,CAAC,GAAG,SAAS;AAClB,KAAK,CAAC,KAAK,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,GAAG,GAAG,GAAG,CAAC,IAAI,KAAK;AAC7C,IAAI,CAAC,GAAG,KAAK,CAAC;AACd;AACA,CAAC,CAAC,GAAG,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;AACjC,CAAC,CAAC,GAAG,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;AACjC,CAAC,CAAC,GAAG,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;AACjC;AACA,CAAC,OAAO,CAAC,CAAC,GAAG,GAAG,EAAE,CAAC,GAAG,GAAG,EAAE,CAAC,GAAG,GAAG,CAAC,CAAC;AACpC,CAAC,CAAC;AACF;AACA,OAAO,CAAC,GAAG,CAAC,GAAG,GAAG,UAAU,GAAG,EAAE;AACjC,CAAC,IAAI,CAAC,GAAG,GAAG,CAAC,CAAC,CAAC,CAAC;AAChB,CAAC,IAAI,CAAC,GAAG,GAAG,CAAC,CAAC,CAAC,CAAC;AAChB,CAAC,IAAI,CAAC,GAAG,GAAG,CAAC,CAAC,CAAC,CAAC;AAChB,CAAC,IAAI,CAAC,CAAC;AACP,CAAC,IAAI,CAAC,CAAC;AACP,CAAC,IAAI,CAAC,CAAC;AACP;AACA,CAAC,CAAC,IAAI,MAAM,CAAC;AACb,CAAC,CAAC,IAAI,GAAG,CAAC;AACV,CAAC,CAAC,IAAI,OAAO,CAAC;AACd;AACA,CAAC,CAAC,GAAG,CAAC,GAAG,QAAQ,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,KAAK,GAAG,CAAC,KAAK,EAAE,GAAG,GAAG,CAAC,CAAC;AAClE,CAAC,CAAC,GAAG,CAAC,GAAG,QAAQ,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,KAAK,GAAG,CAAC,KAAK,EAAE,GAAG,GAAG,CAAC,CAAC;AAClE,CAAC,CAAC,GAAG,CAAC,GAAG,QAAQ,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,KAAK,GAAG,CAAC,KAAK,EAAE,GAAG,GAAG,CAAC,CAAC;AAClE;AACA,CAAC,CAAC,GAAG,CAAC,GAAG,GAAG,CAAC,IAAI,EAAE,CAAC;AACpB,CAAC,CAAC,GAAG,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC;AACnB,CAAC,CAAC,GAAG,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC;AACnB;AACA,CAAC,OAAO,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;AAClB,CAAC,CAAC;AACF;AACA,OAAO,CAAC,GAAG,CAAC,GAAG,GAAG,UAAU,GAAG,EAAE;AACjC,CAAC,IAAI,CAAC,GAAG,GAAG,CAAC,CAAC,CAAC,CAAC;AAChB,CAAC,IAAI,CAAC,GAAG,GAAG,CAAC,CAAC,CAAC,CAAC;AAChB,CAAC,IAAI,CAAC,GAAG,GAAG,CAAC,CAAC,CAAC,CAAC;AAChB,CAAC,IAAI,CAAC,CAAC;AACP,CAAC,IAAI,CAAC,CAAC;AACP,CAAC,IAAI,CAAC,CAAC;AACP;AACA,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,EAAE,IAAI,GAAG,CAAC;AACpB,CAAC,CAAC,GAAG,CAAC,GAAG,GAAG,GAAG,CAAC,CAAC;AACjB,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,GAAG,CAAC;AACjB;AACA,CAAC,IAAI,EAAE,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;AACzB,CAAC,IAAI,EAAE,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;AACzB,CAAC,IAAI,EAAE,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;AACzB,CAAC,CAAC,GAAG,EAAE,GAAG,QAAQ,GAAG,EAAE,GAAG,CAAC,CAAC,GAAG,EAAE,GAAG,GAAG,IAAI,KAAK,CAAC;AACjD,CAAC,CAAC,GAAG,EAAE,GAAG,QAAQ,GAAG,EAAE,GAAG,CAAC,CAAC,GAAG,EAAE,GAAG,GAAG,IAAI,KAAK,CAAC;AACjD,CAAC,CAAC,GAAG,EAAE,GAAG,QAAQ,GAAG,EAAE,GAAG,CAAC,CAAC,GAAG,EAAE,GAAG,GAAG,IAAI,KAAK,CAAC;AACjD;AACA,CAAC,CAAC,IAAI,MAAM,CAAC;AACb,CAAC,CAAC,IAAI,GAAG,CAAC;AACV,CAAC,CAAC,IAAI,OAAO,CAAC;AACd;AACA,CAAC,OAAO,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;AAClB,CAAC,CAAC;AACF;AACA,OAAO,CAAC,GAAG,CAAC,GAAG,GAAG,UAAU,GAAG,EAAE;AACjC,CAAC,IAAI,CAAC,GAAG,GAAG,CAAC,CAAC,CAAC,CAAC;AAChB,CAAC,IAAI,CAAC,GAAG,GAAG,CAAC,CAAC,CAAC,CAAC;AAChB,CAAC,IAAI,CAAC,GAAG,GAAG,CAAC,CAAC,CAAC,CAAC;AAChB,CAAC,IAAI,EAAE,CAAC;AACR,CAAC,IAAI,CAAC,CAAC;AACP,CAAC,IAAI,CAAC,CAAC;AACP;AACA,CAAC,EAAE,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;AACvB,CAAC,CAAC,GAAG,EAAE,GAAG,GAAG,GAAG,CAAC,GAAG,IAAI,CAAC,EAAE,CAAC;AAC5B;AACA,CAAC,IAAI,CAAC,GAAG,CAAC,EAAE;AACZ,EAAE,CAAC,IAAI,GAAG,CAAC;AACX,EAAE;AACF;AACA,CAAC,CAAC,GAAG,IAAI,CAAC,IAAI,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC;AAC9B;AACA,CAAC,OAAO,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;AAClB,CAAC,CAAC;AACF;AACA,OAAO,CAAC,GAAG,CAAC,GAAG,GAAG,UAAU,GAAG,EAAE;AACjC,CAAC,IAAI,CAAC,GAAG,GAAG,CAAC,CAAC,CAAC,CAAC;AAChB,CAAC,IAAI,CAAC,GAAG,GAAG,CAAC,CAAC,CAAC,CAAC;AAChB,CAAC,IAAI,CAAC,GAAG,GAAG,CAAC,CAAC,CAAC,CAAC;AAChB,CAAC,IAAI,CAAC,CAAC;AACP,CAAC,IAAI,CAAC,CAAC;AACP,CAAC,IAAI,EAAE,CAAC;AACR;AACA,CAAC,EAAE,GAAG,CAAC,GAAG,GAAG,GAAG,CAAC,GAAG,IAAI,CAAC,EAAE,CAAC;AAC5B,CAAC,CAAC,GAAG,CAAC,GAAG,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC;AACtB,CAAC,CAAC,GAAG,CAAC,GAAG,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC;AACtB;AACA,CAAC,OAAO,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;AAClB,CAAC,CAAC;AACF;AACA,OAAO,CAAC,GAAG,CAAC,MAAM,GAAG,UAAU,IAAI,EAAE;AACrC,CAAC,IAAI,CAAC,GAAG,IAAI,CAAC,CAAC,CAAC,CAAC;AACjB,CAAC,IAAI,CAAC,GAAG,IAAI,CAAC,CAAC,CAAC,CAAC;AACjB,CAAC,IAAI,CAAC,GAAG,IAAI,CAAC,CAAC,CAAC,CAAC;AACjB,CAAC,IAAI,KAAK,GAAG,CAAC,IAAI,SAAS,GAAG,SAAS,CAAC,CAAC,CAAC,GAAG,OAAO,CAAC,GAAG,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC;AACtE;AACA,CAAC,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC,KAAK,GAAG,EAAE,CAAC,CAAC;AAChC;AACA,CAAC,IAAI,KAAK,KAAK,CAAC,EAAE;AAClB,EAAE,OAAO,EAAE,CAAC;AACZ,EAAE;AACF;AACA,CAAC,IAAI,IAAI,GAAG,EAAE;AACd,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,GAAG,GAAG,CAAC,IAAI,CAAC;AAC9B,KAAK,IAAI,CAAC,KAAK,CAAC,CAAC,GAAG,GAAG,CAAC,IAAI,CAAC,CAAC;AAC9B,IAAI,IAAI,CAAC,KAAK,CAAC,CAAC,GAAG,GAAG,CAAC,CAAC,CAAC;AACzB;AACA,CAAC,IAAI,KAAK,KAAK,CAAC,EAAE;AAClB,EAAE,IAAI,IAAI,EAAE,CAAC;AACb,EAAE;AACF;AACA,CAAC,OAAO,IAAI,CAAC;AACb,CAAC,CAAC;AACF;AACA,OAAO,CAAC,GAAG,CAAC,MAAM,GAAG,UAAU,IAAI,EAAE;AACrC;AACA;AACA,CAAC,OAAO,OAAO,CAAC,GAAG,CAAC,MAAM,CAAC,OAAO,CAAC,GAAG,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC;AAC3D,CAAC,CAAC;AACF;AACA,OAAO,CAAC,GAAG,CAAC,OAAO,GAAG,UAAU,IAAI,EAAE;AACtC,CAAC,IAAI,CAAC,GAAG,IAAI,CAAC,CAAC,CAAC,CAAC;AACjB,CAAC,IAAI,CAAC,GAAG,IAAI,CAAC,CAAC,CAAC,CAAC;AACjB,CAAC,IAAI,CAAC,GAAG,IAAI,CAAC,CAAC,CAAC,CAAC;AACjB;AACA;AACA;AACA,CAAC,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC,EAAE;AACzB,EAAE,IAAI,CAAC,GAAG,CAAC,EAAE;AACb,GAAG,OAAO,EAAE,CAAC;AACb,GAAG;AACH;AACA,EAAE,IAAI,CAAC,GAAG,GAAG,EAAE;AACf,GAAG,OAAO,GAAG,CAAC;AACd,GAAG;AACH;AACA,EAAE,OAAO,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,IAAI,GAAG,IAAI,EAAE,CAAC,GAAG,GAAG,CAAC;AAChD,EAAE;AACF;AACA,CAAC,IAAI,IAAI,GAAG,EAAE;AACd,KAAK,EAAE,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC,GAAG,GAAG,GAAG,CAAC,CAAC,CAAC;AAClC,KAAK,CAAC,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC,GAAG,GAAG,GAAG,CAAC,CAAC,CAAC;AACjC,IAAI,IAAI,CAAC,KAAK,CAAC,CAAC,GAAG,GAAG,GAAG,CAAC,CAAC,CAAC;AAC5B;AACA,CAAC,OAAO,IAAI,CAAC;AACb,CAAC,CAAC;AACF;AACA,OAAO,CAAC,MAAM,CAAC,GAAG,GAAG,UAAU,IAAI,EAAE;AACrC,CAAC,IAAI,KAAK,GAAG,IAAI,GAAG,EAAE,CAAC;AACvB;AACA;AACA,CAAC,IAAI,KAAK,KAAK,CAAC,IAAI,KAAK,KAAK,CAAC,EAAE;AACjC,EAAE,IAAI,IAAI,GAAG,EAAE,EAAE;AACjB,GAAG,KAAK,IAAI,GAAG,CAAC;AAChB,GAAG;AACH;AACA,EAAE,KAAK,GAAG,KAAK,GAAG,IAAI,GAAG,GAAG,CAAC;AAC7B;AACA,EAAE,OAAO,CAAC,KAAK,EAAE,KAAK,EAAE,KAAK,CAAC,CAAC;AAC/B,EAAE;AACF;AACA,CAAC,IAAI,IAAI,GAAG,CAAC,CAAC,EAAE,IAAI,GAAG,EAAE,CAAC,GAAG,CAAC,IAAI,GAAG,CAAC;AACtC,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,KAAK,GAAG,CAAC,IAAI,IAAI,IAAI,GAAG,CAAC;AACpC,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC,KAAK,IAAI,CAAC,IAAI,CAAC,IAAI,IAAI,IAAI,GAAG,CAAC;AAC3C,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC,KAAK,IAAI,CAAC,IAAI,CAAC,IAAI,IAAI,IAAI,GAAG,CAAC;AAC3C;AACA,CAAC,OAAO,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;AAClB,CAAC,CAAC;AACF;AACA,OAAO,CAAC,OAAO,CAAC,GAAG,GAAG,UAAU,IAAI,EAAE;AACtC;AACA,CAAC,IAAI,IAAI,IAAI,GAAG,EAAE;AAClB,EAAE,IAAI,CAAC,GAAG,CAAC,IAAI,GAAG,GAAG,IAAI,EAAE,GAAG,CAAC,CAAC;AAChC,EAAE,OAAO,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;AACnB,EAAE;AACF;AACA,CAAC,IAAI,IAAI,EAAE,CAAC;AACZ;AACA,CAAC,IAAI,GAAG,CAAC;AACT,CAAC,IAAI,CAAC,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,GAAG,EAAE,CAAC,GAAG,CAAC,GAAG,GAAG,CAAC;AACzC,CAAC,IAAI,CAAC,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC,GAAG,GAAG,IAAI,GAAG,EAAE,IAAI,CAAC,CAAC,GAAG,CAAC,GAAG,GAAG,CAAC;AACrD,CAAC,IAAI,CAAC,GAAG,CAAC,GAAG,GAAG,CAAC,IAAI,CAAC,GAAG,GAAG,CAAC;AAC7B;AACA,CAAC,OAAO,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;AAClB,CAAC,CAAC;AACF;AACA,OAAO,CAAC,GAAG,CAAC,GAAG,GAAG,UAAU,IAAI,EAAE;AAClC,CAAC,IAAI,OAAO,GAAG,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,GAAG,IAAI,KAAK,EAAE;AAClD,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,GAAG,IAAI,KAAK,CAAC,CAAC;AACvC,KAAK,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,CAAC;AACjC;AACA,CAAC,IAAI,MAAM,GAAG,OAAO,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC,WAAW,EAAE,CAAC;AACjD,CAAC,OAAO,QAAQ,CAAC,SAAS,CAAC,MAAM,CAAC,MAAM,CAAC,GAAG,MAAM,CAAC;AACnD,CAAC,CAAC;AACF;AACA,OAAO,CAAC,GAAG,CAAC,GAAG,GAAG,UAAU,IAAI,EAAE;AAClC,CAAC,IAAI,KAAK,GAAG,IAAI,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC,KAAK,CAAC,0BAA0B,CAAC,CAAC;AACjE,CAAC,IAAI,CAAC,KAAK,EAAE;AACb,EAAE,OAAO,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;AACnB,EAAE;AACF;AACA,CAAC,IAAI,WAAW,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC;AAC5B;AACA,CAAC,IAAI,KAAK,CAAC,CAAC,CAAC,CAAC,MAAM,KAAK,CAAC,EAAE;AAC5B,EAAE,WAAW,GAAG,WAAW,CAAC,KAAK,CAAC,EAAE,CAAC,CAAC,GAAG,CAAC,UAAU,IAAI,EAAE;AAC1D,GAAG,OAAO,IAAI,GAAG,IAAI,CAAC;AACtB,GAAG,CAAC,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;AACd,EAAE;AACF;AACA,CAAC,IAAI,OAAO,GAAG,QAAQ,CAAC,WAAW,EAAE,EAAE,CAAC,CAAC;AACzC,CAAC,IAAI,CAAC,GAAG,CAAC,OAAO,IAAI,EAAE,IAAI,IAAI,CAAC;AAChC,CAAC,IAAI,CAAC,GAAG,CAAC,OAAO,IAAI,CAAC,IAAI,IAAI,CAAC;AAC/B,CAAC,IAAI,CAAC,GAAG,OAAO,GAAG,IAAI,CAAC;AACxB;AACA,CAAC,OAAO,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;AAClB,CAAC,CAAC;AACF;AACA,OAAO,CAAC,GAAG,CAAC,GAAG,GAAG,UAAU,GAAG,EAAE;AACjC,CAAC,IAAI,CAAC,GAAG,GAAG,CAAC,CAAC,CAAC,GAAG,GAAG,CAAC;AACtB,CAAC,IAAI,CAAC,GAAG,GAAG,CAAC,CAAC,CAAC,GAAG,GAAG,CAAC;AACtB,CAAC,IAAI,CAAC,GAAG,GAAG,CAAC,CAAC,CAAC,GAAG,GAAG,CAAC;AACtB,CAAC,IAAI,GAAG,GAAG,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;AACvC,CAAC,IAAI,GAAG,GAAG,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;AACvC,CAAC,IAAI,MAAM,IAAI,GAAG,GAAG,GAAG,CAAC,CAAC;AAC1B,CAAC,IAAI,SAAS,CAAC;AACf,CAAC,IAAI,GAAG,CAAC;AACT;AACA,CAAC,IAAI,MAAM,GAAG,CAAC,EAAE;AACjB,EAAE,SAAS,GAAG,GAAG,IAAI,CAAC,GAAG,MAAM,CAAC,CAAC;AACjC,EAAE,MAAM;AACR,EAAE,SAAS,GAAG,CAAC,CAAC;AAChB,EAAE;AACF;AACA,CAAC,IAAI,MAAM,IAAI,CAAC,EAAE;AAClB,EAAE,GAAG,GAAG,CAAC,CAAC;AACV,EAAE;AACF,CAAC,IAAI,GAAG,KAAK,CAAC,EAAE;AAChB,EAAE,GAAG,GAAG,CAAC,CAAC,CAAC,GAAG,CAAC,IAAI,MAAM,IAAI,CAAC,CAAC;AAC/B,EAAE;AACF,CAAC,IAAI,GAAG,KAAK,CAAC,EAAE;AAChB,EAAE,GAAG,GAAG,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,IAAI,MAAM,CAAC;AAC7B,EAAE,MAAM;AACR,EAAE,GAAG,GAAG,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,IAAI,MAAM,GAAG,CAAC,CAAC;AACjC,EAAE;AACF;AACA,CAAC,GAAG,IAAI,CAAC,CAAC;AACV,CAAC,GAAG,IAAI,CAAC,CAAC;AACV;AACA,CAAC,OAAO,CAAC,GAAG,GAAG,GAAG,EAAE,MAAM,GAAG,GAAG,EAAE,SAAS,GAAG,GAAG,CAAC,CAAC;AACnD,CAAC,CAAC;AACF;AACA,OAAO,CAAC,GAAG,CAAC,GAAG,GAAG,UAAU,GAAG,EAAE;AACjC,CAAC,IAAI,CAAC,GAAG,GAAG,CAAC,CAAC,CAAC,GAAG,GAAG,CAAC;AACtB,CAAC,IAAI,CAAC,GAAG,GAAG,CAAC,CAAC,CAAC,GAAG,GAAG,CAAC;AACtB,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;AACX,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;AACX;AACA,CAAC,IAAI,CAAC,GAAG,GAAG,EAAE;AACd,EAAE,CAAC,GAAG,GAAG,GAAG,CAAC,GAAG,CAAC,CAAC;AAClB,EAAE,MAAM;AACR,EAAE,CAAC,GAAG,GAAG,GAAG,CAAC,IAAI,GAAG,GAAG,CAAC,CAAC,CAAC;AAC1B,EAAE;AACF;AACA,CAAC,IAAI,CAAC,GAAG,GAAG,EAAE;AACd,EAAE,CAAC,GAAG,CAAC,CAAC,GAAG,GAAG,GAAG,CAAC,KAAK,GAAG,GAAG,CAAC,CAAC,CAAC;AAChC,EAAE;AACF;AACA,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,GAAG,GAAG,EAAE,CAAC,GAAG,GAAG,CAAC,CAAC;AACnC,CAAC,CAAC;AACF;AACA,OAAO,CAAC,GAAG,CAAC,GAAG,GAAG,UAAU,GAAG,EAAE;AACjC,CAAC,IAAI,CAAC,GAAG,GAAG,CAAC,CAAC,CAAC,GAAG,GAAG,CAAC;AACtB,CAAC,IAAI,CAAC,GAAG,GAAG,CAAC,CAAC,CAAC,GAAG,GAAG,CAAC;AACtB;AACA,CAAC,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;AACf,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;AACX;AACA,CAAC,IAAI,CAAC,GAAG,GAAG,EAAE;AACd,EAAE,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC;AACxB,EAAE;AACF;AACA,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,GAAG,GAAG,EAAE,CAAC,GAAG,GAAG,CAAC,CAAC;AACnC,CAAC,CAAC;AACF;AACA,OAAO,CAAC,GAAG,CAAC,GAAG,GAAG,UAAU,GAAG,EAAE;AACjC,CAAC,IAAI,CAAC,GAAG,GAAG,CAAC,CAAC,CAAC,GAAG,GAAG,CAAC;AACtB,CAAC,IAAI,CAAC,GAAG,GAAG,CAAC,CAAC,CAAC,GAAG,GAAG,CAAC;AACtB,CAAC,IAAI,CAAC,GAAG,GAAG,CAAC,CAAC,CAAC,GAAG,GAAG,CAAC;AACtB;AACA,CAAC,IAAI,CAAC,KAAK,GAAG,EAAE;AAChB,EAAE,OAAO,CAAC,CAAC,GAAG,GAAG,EAAE,CAAC,GAAG,GAAG,EAAE,CAAC,GAAG,GAAG,CAAC,CAAC;AACrC,EAAE;AACF;AACA,CAAC,IAAI,IAAI,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;AACtB,CAAC,IAAI,EAAE,GAAG,CAAC,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;AACtB,CAAC,IAAI,CAAC,GAAG,EAAE,GAAG,CAAC,CAAC;AAChB,CAAC,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;AACf,CAAC,IAAI,EAAE,GAAG,CAAC,CAAC;AACZ;AACA,CAAC,QAAQ,IAAI,CAAC,KAAK,CAAC,EAAE,CAAC;AACvB,EAAE,KAAK,CAAC;AACR,GAAG,IAAI,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,MAAM;AAChD,EAAE,KAAK,CAAC;AACR,GAAG,IAAI,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,MAAM;AAChD,EAAE,KAAK,CAAC;AACR,GAAG,IAAI,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,MAAM;AAChD,EAAE,KAAK,CAAC;AACR,GAAG,IAAI,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,MAAM;AAChD,EAAE,KAAK,CAAC;AACR,GAAG,IAAI,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,MAAM;AAChD,EAAE;AACF,GAAG,IAAI,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;AACzC,EAAE;AACF;AACA,CAAC,EAAE,GAAG,CAAC,GAAG,GAAG,CAAC,IAAI,CAAC,CAAC;AACpB;AACA,CAAC,OAAO;AACR,EAAE,CAAC,CAAC,GAAG,IAAI,CAAC,CAAC,CAAC,GAAG,EAAE,IAAI,GAAG;AAC1B,EAAE,CAAC,CAAC,GAAG,IAAI,CAAC,CAAC,CAAC,GAAG,EAAE,IAAI,GAAG;AAC1B,EAAE,CAAC,CAAC,GAAG,IAAI,CAAC,CAAC,CAAC,GAAG,EAAE,IAAI,GAAG;AAC1B,EAAE,CAAC;AACH,CAAC,CAAC;AACF;AACA,OAAO,CAAC,GAAG,CAAC,GAAG,GAAG,UAAU,GAAG,EAAE;AACjC,CAAC,IAAI,CAAC,GAAG,GAAG,CAAC,CAAC,CAAC,GAAG,GAAG,CAAC;AACtB,CAAC,IAAI,CAAC,GAAG,GAAG,CAAC,CAAC,CAAC,GAAG,GAAG,CAAC;AACtB;AACA,CAAC,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,IAAI,GAAG,GAAG,CAAC,CAAC,CAAC;AAC3B,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;AACX;AACA,CAAC,IAAI,CAAC,GAAG,GAAG,EAAE;AACd,EAAE,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;AACZ,EAAE;AACF;AACA,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,GAAG,GAAG,EAAE,CAAC,GAAG,GAAG,CAAC,CAAC;AACnC,CAAC,CAAC;AACF;AACA,OAAO,CAAC,GAAG,CAAC,GAAG,GAAG,UAAU,GAAG,EAAE;AACjC,CAAC,IAAI,CAAC,GAAG,GAAG,CAAC,CAAC,CAAC,GAAG,GAAG,CAAC;AACtB,CAAC,IAAI,CAAC,GAAG,GAAG,CAAC,CAAC,CAAC,GAAG,GAAG,CAAC;AACtB;AACA,CAAC,IAAI,CAAC,GAAG,CAAC,IAAI,GAAG,GAAG,CAAC,CAAC,GAAG,GAAG,GAAG,CAAC,CAAC;AACjC,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;AACX;AACA,CAAC,IAAI,CAAC,GAAG,GAAG,IAAI,CAAC,GAAG,GAAG,EAAE;AACzB,EAAE,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC;AAClB,EAAE;AACF,CAAC,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,GAAG,GAAG,EAAE;AAC1B,EAAE,CAAC,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;AACxB,EAAE;AACF;AACA,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,GAAG,GAAG,EAAE,CAAC,GAAG,GAAG,CAAC,CAAC;AACnC,CAAC,CAAC;AACF;AACA,OAAO,CAAC,GAAG,CAAC,GAAG,GAAG,UAAU,GAAG,EAAE;AACjC,CAAC,IAAI,CAAC,GAAG,GAAG,CAAC,CAAC,CAAC,GAAG,GAAG,CAAC;AACtB,CAAC,IAAI,CAAC,GAAG,GAAG,CAAC,CAAC,CAAC,GAAG,GAAG,CAAC;AACtB,CAAC,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,IAAI,GAAG,GAAG,CAAC,CAAC,CAAC;AAC3B,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,GAAG,CAAC,IAAI,GAAG,EAAE,CAAC,CAAC,GAAG,CAAC,IAAI,GAAG,CAAC,CAAC;AAC/C,CAAC,CAAC;AACF;AACA,OAAO,CAAC,GAAG,CAAC,GAAG,GAAG,UAAU,GAAG,EAAE;AACjC,CAAC,IAAI,CAAC,GAAG,GAAG,CAAC,CAAC,CAAC,GAAG,GAAG,CAAC;AACtB,CAAC,IAAI,CAAC,GAAG,GAAG,CAAC,CAAC,CAAC,GAAG,GAAG,CAAC;AACtB,CAAC,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;AACf,CAAC,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;AACf,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;AACX;AACA,CAAC,IAAI,CAAC,GAAG,CAAC,EAAE;AACZ,EAAE,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC;AACxB,EAAE;AACF;AACA,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,GAAG,GAAG,EAAE,CAAC,GAAG,GAAG,CAAC,CAAC;AACnC,CAAC,CAAC;AACF;AACA,OAAO,CAAC,KAAK,CAAC,GAAG,GAAG,UAAU,KAAK,EAAE;AACrC,CAAC,OAAO,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,GAAG,KAAK,IAAI,GAAG,EAAE,CAAC,KAAK,CAAC,CAAC,CAAC,GAAG,KAAK,IAAI,GAAG,EAAE,CAAC,KAAK,CAAC,CAAC,CAAC,GAAG,KAAK,IAAI,GAAG,CAAC,CAAC;AACvF,CAAC,CAAC;AACF;AACA,OAAO,CAAC,GAAG,CAAC,KAAK,GAAG,UAAU,GAAG,EAAE;AACnC,CAAC,OAAO,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,GAAG,GAAG,IAAI,KAAK,EAAE,CAAC,GAAG,CAAC,CAAC,CAAC,GAAG,GAAG,IAAI,KAAK,EAAE,CAAC,GAAG,CAAC,CAAC,CAAC,GAAG,GAAG,IAAI,KAAK,CAAC,CAAC;AACjF,CAAC,CAAC;AACF;AACA,OAAO,CAAC,IAAI,CAAC,GAAG,GAAG,UAAU,IAAI,EAAE;AACnC,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,GAAG,GAAG,GAAG,GAAG,EAAE,IAAI,CAAC,CAAC,CAAC,GAAG,GAAG,GAAG,GAAG,EAAE,IAAI,CAAC,CAAC,CAAC,GAAG,GAAG,GAAG,GAAG,CAAC,CAAC;AACxE,CAAC,CAAC;AACF;AACA,OAAO,CAAC,IAAI,CAAC,GAAG,GAAG,OAAO,CAAC,IAAI,CAAC,GAAG,GAAG,UAAU,IAAI,EAAE;AACtD,CAAC,OAAO,CAAC,CAAC,EAAE,CAAC,EAAE,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC;AACxB,CAAC,CAAC;AACF;AACA,OAAO,CAAC,IAAI,CAAC,GAAG,GAAG,UAAU,IAAI,EAAE;AACnC,CAAC,OAAO,CAAC,CAAC,EAAE,GAAG,EAAE,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC;AAC1B,CAAC,CAAC;AACF;AACA,OAAO,CAAC,IAAI,CAAC,IAAI,GAAG,UAAU,IAAI,EAAE;AACpC,CAAC,OAAO,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC;AAC3B,CAAC,CAAC;AACF;AACA,OAAO,CAAC,IAAI,CAAC,GAAG,GAAG,UAAU,IAAI,EAAE;AACnC,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;AACxB,CAAC,CAAC;AACF;AACA,OAAO,CAAC,IAAI,CAAC,GAAG,GAAG,UAAU,IAAI,EAAE;AACnC,CAAC,IAAI,GAAG,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,CAAC,GAAG,GAAG,GAAG,GAAG,CAAC,GAAG,IAAI,CAAC;AAClD,CAAC,IAAI,OAAO,GAAG,CAAC,GAAG,IAAI,EAAE,KAAK,GAAG,IAAI,CAAC,CAAC,GAAG,GAAG,CAAC;AAC9C;AACA,CAAC,IAAI,MAAM,GAAG,OAAO,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC,WAAW,EAAE,CAAC;AACjD,CAAC,OAAO,QAAQ,CAAC,SAAS,CAAC,MAAM,CAAC,MAAM,CAAC,GAAG,MAAM,CAAC;AACnD,CAAC,CAAC;AACF;AACA,OAAO,CAAC,GAAG,CAAC,IAAI,GAAG,UAAU,GAAG,EAAE;AAClC,CAAC,IAAI,GAAG,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC,GAAG,GAAG,CAAC,CAAC,CAAC,GAAG,GAAG,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC;AAC1C,CAAC,OAAO,CAAC,GAAG,GAAG,GAAG,GAAG,GAAG,CAAC,CAAC;AAC1B,CAAC,CAAA;;;;;;;;;;;;;;;;;;ACj2BD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS,UAAU,GAAG;AACtB,CAAC,IAAI,KAAK,GAAG,EAAE,CAAC;AAChB;AACA,CAAC,IAAI,MAAM,GAAG,MAAM,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;AACvC;AACA,CAAC,KAAK,IAAI,GAAG,GAAG,MAAM,CAAC,MAAM,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,GAAG,EAAE,CAAC,EAAE,EAAE;AACpD,EAAE,KAAK,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,GAAG;AACrB;AACA;AACA,GAAG,QAAQ,EAAE,CAAC,CAAC;AACf,GAAG,MAAM,EAAE,IAAI;AACf,GAAG,CAAC;AACJ,EAAE;AACF;AACA,CAAC,OAAO,KAAK,CAAC;AACd,CAAC;AACD;AACA;AACA,SAAS,SAAS,CAAC,SAAS,EAAE;AAC9B,CAAC,IAAI,KAAK,GAAG,UAAU,EAAE,CAAC;AAC1B,CAAC,IAAI,KAAK,GAAG,CAAC,SAAS,CAAC,CAAC;AACzB;AACA,CAAC,KAAK,CAAC,SAAS,CAAC,CAAC,QAAQ,GAAG,CAAC,CAAC;AAC/B;AACA,CAAC,OAAO,KAAK,CAAC,MAAM,EAAE;AACtB,EAAE,IAAI,OAAO,GAAG,KAAK,CAAC,GAAG,EAAE,CAAC;AAC5B,EAAE,IAAI,SAAS,GAAG,MAAM,CAAC,IAAI,CAAC,WAAW,CAAC,OAAO,CAAC,CAAC,CAAC;AACpD;AACA,EAAE,KAAK,IAAI,GAAG,GAAG,SAAS,CAAC,MAAM,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,GAAG,EAAE,CAAC,EAAE,EAAE;AACxD,GAAG,IAAI,QAAQ,GAAG,SAAS,CAAC,CAAC,CAAC,CAAC;AAC/B,GAAG,IAAI,IAAI,GAAG,KAAK,CAAC,QAAQ,CAAC,CAAC;AAC9B;AACA,GAAG,IAAI,IAAI,CAAC,QAAQ,KAAK,CAAC,CAAC,EAAE;AAC7B,IAAI,IAAI,CAAC,QAAQ,GAAG,KAAK,CAAC,OAAO,CAAC,CAAC,QAAQ,GAAG,CAAC,CAAC;AAChD,IAAI,IAAI,CAAC,MAAM,GAAG,OAAO,CAAC;AAC1B,IAAI,KAAK,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC;AAC5B,IAAI;AACJ,GAAG;AACH,EAAE;AACF;AACA,CAAC,OAAO,KAAK,CAAC;AACd,CAAC;AACD;AACA,SAAS,IAAI,CAAC,IAAI,EAAE,EAAE,EAAE;AACxB,CAAC,OAAO,UAAU,IAAI,EAAE;AACxB,EAAE,OAAO,EAAE,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC;AACxB,EAAE,CAAC;AACH,CAAC;AACD;AACA,SAAS,cAAc,CAAC,OAAO,EAAE,KAAK,EAAE;AACxC,CAAC,IAAI,IAAI,GAAG,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,MAAM,EAAE,OAAO,CAAC,CAAC;AAC7C,CAAC,IAAI,EAAE,GAAG,WAAW,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,MAAM,CAAC,CAAC,OAAO,CAAC,CAAC;AACtD;AACA,CAAC,IAAI,GAAG,GAAG,KAAK,CAAC,OAAO,CAAC,CAAC,MAAM,CAAC;AACjC,CAAC,OAAO,KAAK,CAAC,GAAG,CAAC,CAAC,MAAM,EAAE;AAC3B,EAAE,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,MAAM,CAAC,CAAC;AAClC,EAAE,EAAE,GAAG,IAAI,CAAC,WAAW,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,MAAM,CAAC,CAAC,GAAG,CAAC,EAAE,EAAE,CAAC,CAAC;AACrD,EAAE,GAAG,GAAG,KAAK,CAAC,GAAG,CAAC,CAAC,MAAM,CAAC;AAC1B,EAAE;AACF;AACA,CAAC,EAAE,CAAC,UAAU,GAAG,IAAI,CAAC;AACtB,CAAC,OAAO,EAAE,CAAC;AACX,CAAC;AACD;AACA,IAAc,KAAA,GAAG,UAAU,SAAS,EAAE;AACtC,CAAC,IAAI,KAAK,GAAG,SAAS,CAAC,SAAS,CAAC,CAAC;AAClC,CAAC,IAAI,UAAU,GAAG,EAAE,CAAC;AACrB;AACA,CAAC,IAAI,MAAM,GAAG,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;AACjC,CAAC,KAAK,IAAI,GAAG,GAAG,MAAM,CAAC,MAAM,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,GAAG,EAAE,CAAC,EAAE,EAAE;AACpD,EAAE,IAAI,OAAO,GAAG,MAAM,CAAC,CAAC,CAAC,CAAC;AAC1B,EAAE,IAAI,IAAI,GAAG,KAAK,CAAC,OAAO,CAAC,CAAC;AAC5B;AACA,EAAE,IAAI,IAAI,CAAC,MAAM,KAAK,IAAI,EAAE;AAC5B;AACA,GAAG,SAAS;AACZ,GAAG;AACH;AACA,EAAE,UAAU,CAAC,OAAO,CAAC,GAAG,cAAc,CAAC,OAAO,EAAE,KAAK,CAAC,CAAC;AACvD,EAAE;AACF;AACA,CAAC,OAAO,UAAU,CAAC;AACnB,CAAC;;AC5FD,IAAI,OAAO,GAAG,EAAE,CAAC;AACjB;AACA,IAAI,MAAM,GAAG,MAAM,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;AACtC;AACA,SAAS,OAAO,CAAC,EAAE,EAAE;AACrB,CAAC,IAAI,SAAS,GAAG,UAAU,IAAI,EAAE;AACjC,EAAE,IAAI,IAAI,KAAK,SAAS,IAAI,IAAI,KAAK,IAAI,EAAE;AAC3C,GAAG,OAAO,IAAI,CAAC;AACf,GAAG;AACH;AACA,EAAE,IAAI,SAAS,CAAC,MAAM,GAAG,CAAC,EAAE;AAC5B,GAAG,IAAI,GAAG,KAAK,CAAC,SAAS,CAAC,KAAK,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;AAChD,GAAG;AACH;AACA,EAAE,OAAO,EAAE,CAAC,IAAI,CAAC,CAAC;AAClB,EAAE,CAAC;AACH;AACA;AACA,CAAC,IAAI,YAAY,IAAI,EAAE,EAAE;AACzB,EAAE,SAAS,CAAC,UAAU,GAAG,EAAE,CAAC,UAAU,CAAC;AACvC,EAAE;AACF;AACA,CAAC,OAAO,SAAS,CAAC;AAClB,CAAC;AACD;AACA,SAAS,WAAW,CAAC,EAAE,EAAE;AACzB,CAAC,IAAI,SAAS,GAAG,UAAU,IAAI,EAAE;AACjC,EAAE,IAAI,IAAI,KAAK,SAAS,IAAI,IAAI,KAAK,IAAI,EAAE;AAC3C,GAAG,OAAO,IAAI,CAAC;AACf,GAAG;AACH;AACA,EAAE,IAAI,SAAS,CAAC,MAAM,GAAG,CAAC,EAAE;AAC5B,GAAG,IAAI,GAAG,KAAK,CAAC,SAAS,CAAC,KAAK,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;AAChD,GAAG;AACH;AACA,EAAE,IAAI,MAAM,GAAG,EAAE,CAAC,IAAI,CAAC,CAAC;AACxB;AACA;AACA;AACA;AACA,EAAE,IAAI,OAAO,MAAM,KAAK,QAAQ,EAAE;AAClC,GAAG,KAAK,IAAI,GAAG,GAAG,MAAM,CAAC,MAAM,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,GAAG,EAAE,CAAC,EAAE,EAAE;AACtD,IAAI,MAAM,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC;AACtC,IAAI;AACJ,GAAG;AACH;AACA,EAAE,OAAO,MAAM,CAAC;AAChB,EAAE,CAAC;AACH;AACA;AACA,CAAC,IAAI,YAAY,IAAI,EAAE,EAAE;AACzB,EAAE,SAAS,CAAC,UAAU,GAAG,EAAE,CAAC,UAAU,CAAC;AACvC,EAAE;AACF;AACA,CAAC,OAAO,SAAS,CAAC;AAClB,CAAC;AACD;AACA,MAAM,CAAC,OAAO,CAAC,UAAU,SAAS,EAAE;AACpC,CAAC,OAAO,CAAC,SAAS,CAAC,GAAG,EAAE,CAAC;AACzB;AACA,CAAC,MAAM,CAAC,cAAc,CAAC,OAAO,CAAC,SAAS,CAAC,EAAE,UAAU,EAAE,CAAC,KAAK,EAAE,WAAW,CAAC,SAAS,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC;AACjG,CAAC,MAAM,CAAC,cAAc,CAAC,OAAO,CAAC,SAAS,CAAC,EAAE,QAAQ,EAAE,CAAC,KAAK,EAAE,WAAW,CAAC,SAAS,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC;AAC7F;AACA,CAAC,IAAI,MAAM,GAAG,KAAK,CAAC,SAAS,CAAC,CAAC;AAC/B,CAAC,IAAI,WAAW,GAAG,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;AACvC;AACA,CAAC,WAAW,CAAC,OAAO,CAAC,UAAU,OAAO,EAAE;AACxC,EAAE,IAAI,EAAE,GAAG,MAAM,CAAC,OAAO,CAAC,CAAC;AAC3B;AACA,EAAE,OAAO,CAAC,SAAS,CAAC,CAAC,OAAO,CAAC,GAAG,WAAW,CAAC,EAAE,CAAC,CAAC;AAChD,EAAE,OAAO,CAAC,SAAS,CAAC,CAAC,OAAO,CAAC,CAAC,GAAG,GAAG,OAAO,CAAC,EAAE,CAAC,CAAC;AAChD,EAAE,CAAC,CAAC;AACJ,CAAC,CAAC,CAAC;AACH;AACA,IAAA,YAAc,GAAG,OAAO;;ACxExB,IAAI,MAAM,GAAG,EAAE,CAAC,KAAK,CAAC;AACtB;AACA,IAAI,aAAa,GAAG;AACpB;AACA,CAAC,SAAS;AACV;AACA;AACA,CAAC,MAAM;AACP;AACA;AACA,CAAC,KAAK;AACN,CAAC,CAAC;AACF;AACA,IAAI,eAAe,GAAG,EAAE,CAAC;AACzB,MAAM,CAAC,IAAI,CAACC,YAAO,CAAC,CAAC,OAAO,CAAC,UAAU,KAAK,EAAE;AAC9C,CAAC,eAAe,CAAC,MAAM,CAAC,IAAI,CAACA,YAAO,CAAC,KAAK,CAAC,CAAC,MAAM,CAAC,CAAC,IAAI,EAAE,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC,GAAG,KAAK,CAAC;AAC7E,CAAC,CAAC,CAAC;AACH;AACA,IAAI,QAAQ,GAAG,EAAE,CAAC;AAClB;AACA,SAAS,KAAK,CAAC,GAAG,EAAE,KAAK,EAAE;AAC3B,CAAC,IAAI,EAAE,IAAI,YAAY,KAAK,CAAC,EAAE;AAC/B,EAAE,OAAO,IAAI,KAAK,CAAC,GAAG,EAAE,KAAK,CAAC,CAAC;AAC/B,EAAE;AACF;AACA,CAAC,IAAI,KAAK,IAAI,KAAK,IAAI,aAAa,EAAE;AACtC,EAAE,KAAK,GAAG,IAAI,CAAC;AACf,EAAE;AACF;AACA,CAAC,IAAI,KAAK,IAAI,EAAE,KAAK,IAAIA,YAAO,CAAC,EAAE;AACnC,EAAE,MAAM,IAAI,KAAK,CAAC,iBAAiB,GAAG,KAAK,CAAC,CAAC;AAC7C,EAAE;AACF;AACA,CAAC,IAAI,CAAC,CAAC;AACP,CAAC,IAAI,QAAQ,CAAC;AACd;AACA,CAAC,IAAI,GAAG,IAAI,IAAI,EAAE;AAClB,EAAE,IAAI,CAAC,KAAK,GAAG,KAAK,CAAC;AACrB,EAAE,IAAI,CAAC,KAAK,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;AACzB,EAAE,IAAI,CAAC,MAAM,GAAG,CAAC,CAAC;AAClB,EAAE,MAAM,IAAI,GAAG,YAAY,KAAK,EAAE;AAClC,EAAE,IAAI,CAAC,KAAK,GAAG,GAAG,CAAC,KAAK,CAAC;AACzB,EAAE,IAAI,CAAC,KAAK,GAAG,GAAG,CAAC,KAAK,CAAC,KAAK,EAAE,CAAC;AACjC,EAAE,IAAI,CAAC,MAAM,GAAG,GAAG,CAAC,MAAM,CAAC;AAC3B,EAAE,MAAM,IAAI,OAAO,GAAG,KAAK,QAAQ,EAAE;AACrC,EAAE,IAAI,MAAM,GAAG,WAAW,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;AACpC,EAAE,IAAI,MAAM,KAAK,IAAI,EAAE;AACvB,GAAG,MAAM,IAAI,KAAK,CAAC,qCAAqC,GAAG,GAAG,CAAC,CAAC;AAChE,GAAG;AACH;AACA,EAAE,IAAI,CAAC,KAAK,GAAG,MAAM,CAAC,KAAK,CAAC;AAC5B,EAAE,QAAQ,GAAGA,YAAO,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,QAAQ,CAAC;AAC1C,EAAE,IAAI,CAAC,KAAK,GAAG,MAAM,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,EAAE,QAAQ,CAAC,CAAC;AAC/C,EAAE,IAAI,CAAC,MAAM,GAAG,OAAO,MAAM,CAAC,KAAK,CAAC,QAAQ,CAAC,KAAK,QAAQ,GAAG,MAAM,CAAC,KAAK,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC;AACxF,EAAE,MAAM,IAAI,GAAG,CAAC,MAAM,EAAE;AACxB,EAAE,IAAI,CAAC,KAAK,GAAG,KAAK,IAAI,KAAK,CAAC;AAC9B,EAAE,QAAQ,GAAGA,YAAO,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,QAAQ,CAAC;AAC1C,EAAE,IAAI,MAAM,GAAG,MAAM,CAAC,IAAI,CAAC,GAAG,EAAE,CAAC,EAAE,QAAQ,CAAC,CAAC;AAC7C,EAAE,IAAI,CAAC,KAAK,GAAG,SAAS,CAAC,MAAM,EAAE,QAAQ,CAAC,CAAC;AAC3C,EAAE,IAAI,CAAC,MAAM,GAAG,OAAO,GAAG,CAAC,QAAQ,CAAC,KAAK,QAAQ,GAAG,GAAG,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC;AACtE,EAAE,MAAM,IAAI,OAAO,GAAG,KAAK,QAAQ,EAAE;AACrC;AACA,EAAE,GAAG,IAAI,QAAQ,CAAC;AAClB,EAAE,IAAI,CAAC,KAAK,GAAG,KAAK,CAAC;AACrB,EAAE,IAAI,CAAC,KAAK,GAAG;AACf,GAAG,CAAC,GAAG,IAAI,EAAE,IAAI,IAAI;AACrB,GAAG,CAAC,GAAG,IAAI,CAAC,IAAI,IAAI;AACpB,GAAG,GAAG,GAAG,IAAI;AACb,GAAG,CAAC;AACJ,EAAE,IAAI,CAAC,MAAM,GAAG,CAAC,CAAC;AAClB,EAAE,MAAM;AACR,EAAE,IAAI,CAAC,MAAM,GAAG,CAAC,CAAC;AAClB;AACA,EAAE,IAAI,IAAI,GAAG,MAAM,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;AAC9B,EAAE,IAAI,OAAO,IAAI,GAAG,EAAE;AACtB,GAAG,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,EAAE,CAAC,CAAC,CAAC;AACzC,GAAG,IAAI,CAAC,MAAM,GAAG,OAAO,GAAG,CAAC,KAAK,KAAK,QAAQ,GAAG,GAAG,CAAC,KAAK,GAAG,CAAC,CAAC;AAC/D,GAAG;AACH;AACA,EAAE,IAAI,UAAU,GAAG,IAAI,CAAC,IAAI,EAAE,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;AACxC,EAAE,IAAI,EAAE,UAAU,IAAI,eAAe,CAAC,EAAE;AACxC,GAAG,MAAM,IAAI,KAAK,CAAC,qCAAqC,GAAG,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,CAAC,CAAC;AAChF,GAAG;AACH;AACA,EAAE,IAAI,CAAC,KAAK,GAAG,eAAe,CAAC,UAAU,CAAC,CAAC;AAC3C;AACA,EAAE,IAAI,MAAM,GAAGA,YAAO,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,MAAM,CAAC;AAC1C,EAAE,IAAI,KAAK,GAAG,EAAE,CAAC;AACjB,EAAE,KAAK,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,MAAM,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;AACtC,GAAG,KAAK,CAAC,IAAI,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AAC9B,GAAG;AACH;AACA,EAAE,IAAI,CAAC,KAAK,GAAG,SAAS,CAAC,KAAK,CAAC,CAAC;AAChC,EAAE;AACF;AACA;AACA,CAAC,IAAI,QAAQ,CAAC,IAAI,CAAC,KAAK,CAAC,EAAE;AAC3B,EAAE,QAAQ,GAAGA,YAAO,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,QAAQ,CAAC;AAC1C,EAAE,KAAK,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,QAAQ,EAAE,CAAC,EAAE,EAAE;AACjC,GAAG,IAAI,KAAK,GAAG,QAAQ,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC;AACvC,GAAG,IAAI,KAAK,EAAE;AACd,IAAI,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,GAAG,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC;AACzC,IAAI;AACJ,GAAG;AACH,EAAE;AACF;AACA,CAAC,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC;AACrD;AACA,CAAC,IAAI,MAAM,CAAC,MAAM,EAAE;AACpB,EAAE,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;AACtB,EAAE;AACF,CAAC;AACD;AACA,KAAK,CAAC,SAAS,GAAG;AAClB,CAAC,QAAQ,EAAE,YAAY;AACvB,EAAE,OAAO,IAAI,CAAC,MAAM,EAAE,CAAC;AACvB,EAAE;AACF;AACA,CAAC,MAAM,EAAE,YAAY;AACrB,EAAE,OAAO,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,EAAE,CAAC;AAC5B,EAAE;AACF;AACA,CAAC,MAAM,EAAE,UAAU,MAAM,EAAE;AAC3B,EAAE,IAAI,IAAI,GAAG,IAAI,CAAC,KAAK,IAAI,WAAW,CAAC,EAAE,GAAG,IAAI,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;AAC9D,EAAE,IAAI,GAAG,IAAI,CAAC,KAAK,CAAC,OAAO,MAAM,KAAK,QAAQ,GAAG,MAAM,GAAG,CAAC,CAAC,CAAC;AAC7D,EAAE,IAAI,IAAI,GAAG,IAAI,CAAC,MAAM,KAAK,CAAC,GAAG,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;AAC7E,EAAE,OAAO,WAAW,CAAC,EAAE,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,CAAC;AAC1C,EAAE;AACF;AACA,CAAC,aAAa,EAAE,UAAU,MAAM,EAAE;AAClC,EAAE,IAAI,IAAI,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC,KAAK,CAAC,OAAO,MAAM,KAAK,QAAQ,GAAG,MAAM,GAAG,CAAC,CAAC,CAAC;AACvE,EAAE,IAAI,IAAI,GAAG,IAAI,CAAC,MAAM,KAAK,CAAC,GAAG,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;AAC7E,EAAE,OAAO,WAAW,CAAC,EAAE,CAAC,GAAG,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;AAC1C,EAAE;AACF;AACA,CAAC,KAAK,EAAE,YAAY;AACpB,EAAE,OAAO,IAAI,CAAC,MAAM,KAAK,CAAC,GAAG,IAAI,CAAC,KAAK,CAAC,KAAK,EAAE,GAAG,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;AACjF,EAAE;AACF;AACA,CAAC,MAAM,EAAE,YAAY;AACrB,EAAE,IAAI,MAAM,GAAG,EAAE,CAAC;AAClB,EAAE,IAAI,QAAQ,GAAGA,YAAO,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,QAAQ,CAAC;AAC9C,EAAE,IAAI,MAAM,GAAGA,YAAO,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,MAAM,CAAC;AAC1C;AACA,EAAE,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,QAAQ,EAAE,CAAC,EAAE,EAAE;AACrC,GAAG,MAAM,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;AACrC,GAAG;AACH;AACA,EAAE,IAAI,IAAI,CAAC,MAAM,KAAK,CAAC,EAAE;AACzB,GAAG,MAAM,CAAC,KAAK,GAAG,IAAI,CAAC,MAAM,CAAC;AAC9B,GAAG;AACH;AACA,EAAE,OAAO,MAAM,CAAC;AAChB,EAAE;AACF;AACA,CAAC,SAAS,EAAE,YAAY;AACxB,EAAE,IAAI,GAAG,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC,KAAK,CAAC;AAC7B,EAAE,GAAG,CAAC,CAAC,CAAC,IAAI,GAAG,CAAC;AAChB,EAAE,GAAG,CAAC,CAAC,CAAC,IAAI,GAAG,CAAC;AAChB,EAAE,GAAG,CAAC,CAAC,CAAC,IAAI,GAAG,CAAC;AAChB;AACA,EAAE,IAAI,IAAI,CAAC,MAAM,KAAK,CAAC,EAAE;AACzB,GAAG,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;AACzB,GAAG;AACH;AACA,EAAE,OAAO,GAAG,CAAC;AACb,EAAE;AACF;AACA,CAAC,UAAU,EAAE,YAAY;AACzB,EAAE,IAAI,GAAG,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC,MAAM,EAAE,CAAC;AAChC,EAAE,GAAG,CAAC,CAAC,IAAI,GAAG,CAAC;AACf,EAAE,GAAG,CAAC,CAAC,IAAI,GAAG,CAAC;AACf,EAAE,GAAG,CAAC,CAAC,IAAI,GAAG,CAAC;AACf;AACA,EAAE,IAAI,IAAI,CAAC,MAAM,KAAK,CAAC,EAAE;AACzB,GAAG,GAAG,CAAC,KAAK,GAAG,IAAI,CAAC,MAAM,CAAC;AAC3B,GAAG;AACH;AACA,EAAE,OAAO,GAAG,CAAC;AACb,EAAE;AACF;AACA,CAAC,KAAK,EAAE,UAAU,MAAM,EAAE;AAC1B,EAAE,MAAM,GAAG,IAAI,CAAC,GAAG,CAAC,MAAM,IAAI,CAAC,EAAE,CAAC,CAAC,CAAC;AACpC,EAAE,OAAO,IAAI,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,YAAY,CAAC,MAAM,CAAC,CAAC,CAAC,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,EAAE,IAAI,CAAC,KAAK,CAAC,CAAC;AACzF,EAAE;AACF;AACA,CAAC,KAAK,EAAE,UAAU,GAAG,EAAE;AACvB,EAAE,IAAI,SAAS,CAAC,MAAM,EAAE;AACxB,GAAG,OAAO,IAAI,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,CAAC,EAAE,IAAI,CAAC,KAAK,CAAC,CAAC;AAClF,GAAG;AACH;AACA,EAAE,OAAO,IAAI,CAAC,MAAM,CAAC;AACrB,EAAE;AACF;AACA;AACA,CAAC,GAAG,EAAE,MAAM,CAAC,KAAK,EAAE,CAAC,EAAE,KAAK,CAAC,GAAG,CAAC,CAAC;AAClC,CAAC,KAAK,EAAE,MAAM,CAAC,KAAK,EAAE,CAAC,EAAE,KAAK,CAAC,GAAG,CAAC,CAAC;AACpC,CAAC,IAAI,EAAE,MAAM,CAAC,KAAK,EAAE,CAAC,EAAE,KAAK,CAAC,GAAG,CAAC,CAAC;AACnC;AACA,CAAC,GAAG,EAAE,MAAM,CAAC,CAAC,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,CAAC,EAAE,CAAC,EAAE,UAAU,GAAG,EAAE,EAAE,OAAO,CAAC,CAAC,GAAG,GAAG,GAAG,IAAI,GAAG,IAAI,GAAG,CAAC,EAAE,CAAC;AAC1G;AACA,CAAC,WAAW,EAAE,MAAM,CAAC,KAAK,EAAE,CAAC,EAAE,KAAK,CAAC,GAAG,CAAC,CAAC;AAC1C,CAAC,SAAS,EAAE,MAAM,CAAC,KAAK,EAAE,CAAC,EAAE,KAAK,CAAC,GAAG,CAAC,CAAC;AACxC;AACA,CAAC,WAAW,EAAE,MAAM,CAAC,KAAK,EAAE,CAAC,EAAE,KAAK,CAAC,GAAG,CAAC,CAAC;AAC1C,CAAC,KAAK,EAAE,MAAM,CAAC,KAAK,EAAE,CAAC,EAAE,KAAK,CAAC,GAAG,CAAC,CAAC;AACpC;AACA,CAAC,MAAM,EAAE,MAAM,CAAC,KAAK,EAAE,CAAC,EAAE,KAAK,CAAC,GAAG,CAAC,CAAC;AACrC,CAAC,IAAI,EAAE,MAAM,CAAC,KAAK,EAAE,CAAC,EAAE,KAAK,CAAC,GAAG,CAAC,CAAC;AACnC;AACA,CAAC,KAAK,EAAE,MAAM,CAAC,KAAK,EAAE,CAAC,EAAE,KAAK,CAAC,GAAG,CAAC,CAAC;AACpC,CAAC,MAAM,EAAE,MAAM,CAAC,KAAK,EAAE,CAAC,EAAE,KAAK,CAAC,GAAG,CAAC,CAAC;AACrC;AACA,CAAC,IAAI,EAAE,MAAM,CAAC,MAAM,EAAE,CAAC,EAAE,KAAK,CAAC,GAAG,CAAC,CAAC;AACpC,CAAC,OAAO,EAAE,MAAM,CAAC,MAAM,EAAE,CAAC,EAAE,KAAK,CAAC,GAAG,CAAC,CAAC;AACvC,CAAC,MAAM,EAAE,MAAM,CAAC,MAAM,EAAE,CAAC,EAAE,KAAK,CAAC,GAAG,CAAC,CAAC;AACtC,CAAC,KAAK,EAAE,MAAM,CAAC,MAAM,EAAE,CAAC,EAAE,KAAK,CAAC,GAAG,CAAC,CAAC;AACrC;AACA,CAAC,CAAC,EAAE,MAAM,CAAC,KAAK,EAAE,CAAC,EAAE,KAAK,CAAC,GAAG,CAAC,CAAC;AAChC,CAAC,CAAC,EAAE,MAAM,CAAC,KAAK,EAAE,CAAC,EAAE,KAAK,CAAC,GAAG,CAAC,CAAC;AAChC,CAAC,CAAC,EAAE,MAAM,CAAC,KAAK,EAAE,CAAC,EAAE,KAAK,CAAC,GAAG,CAAC,CAAC;AAChC;AACA,CAAC,CAAC,EAAE,MAAM,CAAC,KAAK,EAAE,CAAC,EAAE,KAAK,CAAC,GAAG,CAAC,CAAC;AAChC,CAAC,CAAC,EAAE,MAAM,CAAC,KAAK,EAAE,CAAC,CAAC;AACpB,CAAC,CAAC,EAAE,MAAM,CAAC,KAAK,EAAE,CAAC,CAAC;AACpB;AACA,CAAC,OAAO,EAAE,UAAU,GAAG,EAAE;AACzB,EAAE,IAAI,SAAS,CAAC,MAAM,EAAE;AACxB,GAAG,OAAO,IAAI,KAAK,CAAC,GAAG,CAAC,CAAC;AACzB,GAAG;AACH;AACA,EAAE,OAAOA,YAAO,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;AACjD,EAAE;AACF;AACA,CAAC,GAAG,EAAE,UAAU,GAAG,EAAE;AACrB,EAAE,IAAI,SAAS,CAAC,MAAM,EAAE;AACxB,GAAG,OAAO,IAAI,KAAK,CAAC,GAAG,CAAC,CAAC;AACzB,GAAG;AACH;AACA,EAAE,OAAO,WAAW,CAAC,EAAE,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,EAAE,CAAC,KAAK,EAAE,CAAC,KAAK,CAAC,CAAC;AACtD,EAAE;AACF;AACA,CAAC,SAAS,EAAE,YAAY;AACxB,EAAE,IAAI,GAAG,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC,KAAK,CAAC;AAC7B,EAAE,OAAO,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,GAAG,IAAI,KAAK,EAAE,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,GAAG,IAAI,KAAK,CAAC,CAAC,IAAI,GAAG,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,CAAC;AAC5E,EAAE;AACF;AACA,CAAC,UAAU,EAAE,YAAY;AACzB;AACA,EAAE,IAAI,GAAG,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC,KAAK,CAAC;AAC7B;AACA,EAAE,IAAI,GAAG,GAAG,EAAE,CAAC;AACf,EAAE,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,GAAG,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;AACvC,GAAG,IAAI,IAAI,GAAG,GAAG,CAAC,CAAC,CAAC,GAAG,GAAG,CAAC;AAC3B,GAAG,GAAG,CAAC,CAAC,CAAC,GAAG,CAAC,IAAI,IAAI,OAAO,IAAI,IAAI,GAAG,KAAK,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC,IAAI,GAAG,KAAK,IAAI,KAAK,GAAG,GAAG,CAAC,CAAC;AACvF,GAAG;AACH;AACA,EAAE,OAAO,MAAM,GAAG,GAAG,CAAC,CAAC,CAAC,GAAG,MAAM,GAAG,GAAG,CAAC,CAAC,CAAC,GAAG,MAAM,GAAG,GAAG,CAAC,CAAC,CAAC,CAAC;AAC7D,EAAE;AACF;AACA,CAAC,QAAQ,EAAE,UAAU,MAAM,EAAE;AAC7B;AACA,EAAE,IAAI,IAAI,GAAG,IAAI,CAAC,UAAU,EAAE,CAAC;AAC/B,EAAE,IAAI,IAAI,GAAG,MAAM,CAAC,UAAU,EAAE,CAAC;AACjC;AACA,EAAE,IAAI,IAAI,GAAG,IAAI,EAAE;AACnB,GAAG,OAAO,CAAC,IAAI,GAAG,IAAI,KAAK,IAAI,GAAG,IAAI,CAAC,CAAC;AACxC,GAAG;AACH;AACA,EAAE,OAAO,CAAC,IAAI,GAAG,IAAI,KAAK,IAAI,GAAG,IAAI,CAAC,CAAC;AACvC,EAAE;AACF;AACA,CAAC,KAAK,EAAE,UAAU,MAAM,EAAE;AAC1B,EAAE,IAAI,aAAa,GAAG,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC;AAC5C,EAAE,IAAI,aAAa,IAAI,GAAG,EAAE;AAC5B,GAAG,OAAO,KAAK,CAAC;AAChB,GAAG;AACH;AACA,EAAE,OAAO,CAAC,aAAa,IAAI,GAAG,IAAI,IAAI,GAAG,EAAE,CAAC;AAC5C,EAAE;AACF;AACA,CAAC,MAAM,EAAE,YAAY;AACrB;AACA,EAAE,IAAI,GAAG,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC,KAAK,CAAC;AAC7B,EAAE,IAAI,GAAG,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC,GAAG,GAAG,GAAG,GAAG,CAAC,CAAC,CAAC,GAAG,GAAG,GAAG,GAAG,CAAC,CAAC,CAAC,GAAG,GAAG,IAAI,IAAI,CAAC;AAChE,EAAE,OAAO,GAAG,GAAG,GAAG,CAAC;AACnB,EAAE;AACF;AACA,CAAC,OAAO,EAAE,YAAY;AACtB,EAAE,OAAO,CAAC,IAAI,CAAC,MAAM,EAAE,CAAC;AACxB,EAAE;AACF;AACA,CAAC,MAAM,EAAE,YAAY;AACrB,EAAE,IAAI,GAAG,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;AACvB,EAAE,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,EAAE,EAAE;AAC9B,GAAG,GAAG,CAAC,KAAK,CAAC,CAAC,CAAC,GAAG,GAAG,GAAG,GAAG,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;AACrC,GAAG;AACH,EAAE,OAAO,GAAG,CAAC;AACb,EAAE;AACF;AACA,CAAC,OAAO,EAAE,UAAU,KAAK,EAAE;AAC3B,EAAE,IAAI,GAAG,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;AACvB,EAAE,GAAG,CAAC,KAAK,CAAC,CAAC,CAAC,IAAI,GAAG,CAAC,KAAK,CAAC,CAAC,CAAC,GAAG,KAAK,CAAC;AACvC,EAAE,OAAO,GAAG,CAAC;AACb,EAAE;AACF;AACA,CAAC,MAAM,EAAE,UAAU,KAAK,EAAE;AAC1B,EAAE,IAAI,GAAG,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;AACvB,EAAE,GAAG,CAAC,KAAK,CAAC,CAAC,CAAC,IAAI,GAAG,CAAC,KAAK,CAAC,CAAC,CAAC,GAAG,KAAK,CAAC;AACvC,EAAE,OAAO,GAAG,CAAC;AACb,EAAE;AACF;AACA,CAAC,QAAQ,EAAE,UAAU,KAAK,EAAE;AAC5B,EAAE,IAAI,GAAG,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;AACvB,EAAE,GAAG,CAAC,KAAK,CAAC,CAAC,CAAC,IAAI,GAAG,CAAC,KAAK,CAAC,CAAC,CAAC,GAAG,KAAK,CAAC;AACvC,EAAE,OAAO,GAAG,CAAC;AACb,EAAE;AACF;AACA,CAAC,UAAU,EAAE,UAAU,KAAK,EAAE;AAC9B,EAAE,IAAI,GAAG,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;AACvB,EAAE,GAAG,CAAC,KAAK,CAAC,CAAC,CAAC,IAAI,GAAG,CAAC,KAAK,CAAC,CAAC,CAAC,GAAG,KAAK,CAAC;AACvC,EAAE,OAAO,GAAG,CAAC;AACb,EAAE;AACF;AACA,CAAC,MAAM,EAAE,UAAU,KAAK,EAAE;AAC1B,EAAE,IAAI,GAAG,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;AACvB,EAAE,GAAG,CAAC,KAAK,CAAC,CAAC,CAAC,IAAI,GAAG,CAAC,KAAK,CAAC,CAAC,CAAC,GAAG,KAAK,CAAC;AACvC,EAAE,OAAO,GAAG,CAAC;AACb,EAAE;AACF;AACA,CAAC,OAAO,EAAE,UAAU,KAAK,EAAE;AAC3B,EAAE,IAAI,GAAG,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;AACvB,EAAE,GAAG,CAAC,KAAK,CAAC,CAAC,CAAC,IAAI,GAAG,CAAC,KAAK,CAAC,CAAC,CAAC,GAAG,KAAK,CAAC;AACvC,EAAE,OAAO,GAAG,CAAC;AACb,EAAE;AACF;AACA,CAAC,SAAS,EAAE,YAAY;AACxB;AACA,EAAE,IAAI,GAAG,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC,KAAK,CAAC;AAC7B,EAAE,IAAI,GAAG,GAAG,GAAG,CAAC,CAAC,CAAC,GAAG,GAAG,GAAG,GAAG,CAAC,CAAC,CAAC,GAAG,IAAI,GAAG,GAAG,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC;AACzD,EAAE,OAAO,KAAK,CAAC,GAAG,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC,CAAC;AAClC,EAAE;AACF;AACA,CAAC,IAAI,EAAE,UAAU,KAAK,EAAE;AACxB,EAAE,OAAO,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,MAAM,IAAI,IAAI,CAAC,MAAM,GAAG,KAAK,CAAC,CAAC,CAAC;AACzD,EAAE;AACF;AACA,CAAC,OAAO,EAAE,UAAU,KAAK,EAAE;AAC3B,EAAE,OAAO,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,MAAM,IAAI,IAAI,CAAC,MAAM,GAAG,KAAK,CAAC,CAAC,CAAC;AACzD,EAAE;AACF;AACA,CAAC,MAAM,EAAE,UAAU,OAAO,EAAE;AAC5B,EAAE,IAAI,GAAG,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;AACvB,EAAE,IAAI,GAAG,GAAG,GAAG,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;AACzB,EAAE,GAAG,GAAG,CAAC,GAAG,GAAG,OAAO,IAAI,GAAG,CAAC;AAC9B,EAAE,GAAG,GAAG,GAAG,GAAG,CAAC,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,CAAC;AAClC,EAAE,GAAG,CAAC,KAAK,CAAC,CAAC,CAAC,GAAG,GAAG,CAAC;AACrB,EAAE,OAAO,GAAG,CAAC;AACb,EAAE;AACF;AACA,CAAC,GAAG,EAAE,UAAU,UAAU,EAAE,MAAM,EAAE;AACpC;AACA;AACA,EAAE,IAAI,CAAC,UAAU,IAAI,CAAC,UAAU,CAAC,GAAG,EAAE;AACtC,GAAG,MAAM,IAAI,KAAK,CAAC,wEAAwE,GAAG,OAAO,UAAU,CAAC,CAAC;AACjH,GAAG;AACH,EAAE,IAAI,MAAM,GAAG,UAAU,CAAC,GAAG,EAAE,CAAC;AAChC,EAAE,IAAI,MAAM,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;AAC1B,EAAE,IAAI,CAAC,GAAG,MAAM,KAAK,SAAS,GAAG,GAAG,GAAG,MAAM,CAAC;AAC9C;AACA,EAAE,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;AACpB,EAAE,IAAI,CAAC,GAAG,MAAM,CAAC,KAAK,EAAE,GAAG,MAAM,CAAC,KAAK,EAAE,CAAC;AAC1C;AACA,EAAE,IAAI,EAAE,GAAG,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,KAAK,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,IAAI,GAAG,CAAC;AACpE,EAAE,IAAI,EAAE,GAAG,CAAC,GAAG,EAAE,CAAC;AAClB;AACA,EAAE,OAAO,KAAK,CAAC,GAAG;AAClB,IAAI,EAAE,GAAG,MAAM,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,MAAM,CAAC,GAAG,EAAE;AACzC,IAAI,EAAE,GAAG,MAAM,CAAC,KAAK,EAAE,GAAG,EAAE,GAAG,MAAM,CAAC,KAAK,EAAE;AAC7C,IAAI,EAAE,GAAG,MAAM,CAAC,IAAI,EAAE,GAAG,EAAE,GAAG,MAAM,CAAC,IAAI,EAAE;AAC3C,IAAI,MAAM,CAAC,KAAK,EAAE,GAAG,CAAC,GAAG,MAAM,CAAC,KAAK,EAAE,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;AACnD,EAAE;AACF,CAAC,CAAC;AACF;AACA;AACA,MAAM,CAAC,IAAI,CAACA,YAAO,CAAC,CAAC,OAAO,CAAC,UAAU,KAAK,EAAE;AAC9C,CAAC,IAAI,aAAa,CAAC,OAAO,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,EAAE;AAC1C,EAAE,OAAO;AACT,EAAE;AACF;AACA,CAAC,IAAI,QAAQ,GAAGA,YAAO,CAAC,KAAK,CAAC,CAAC,QAAQ,CAAC;AACxC;AACA;AACA,CAAC,KAAK,CAAC,SAAS,CAAC,KAAK,CAAC,GAAG,YAAY;AACtC,EAAE,IAAI,IAAI,CAAC,KAAK,KAAK,KAAK,EAAE;AAC5B,GAAG,OAAO,IAAI,KAAK,CAAC,IAAI,CAAC,CAAC;AAC1B,GAAG;AACH;AACA,EAAE,IAAI,SAAS,CAAC,MAAM,EAAE;AACxB,GAAG,OAAO,IAAI,KAAK,CAAC,SAAS,EAAE,KAAK,CAAC,CAAC;AACtC,GAAG;AACH;AACA,EAAE,IAAI,QAAQ,GAAG,OAAO,SAAS,CAAC,QAAQ,CAAC,KAAK,QAAQ,GAAG,QAAQ,GAAG,IAAI,CAAC,MAAM,CAAC;AAClF,EAAE,OAAO,IAAI,KAAK,CAAC,WAAW,CAACA,YAAO,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,KAAK,CAAC,CAAC,GAAG,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,MAAM,CAAC,QAAQ,CAAC,EAAE,KAAK,CAAC,CAAC;AACpG,EAAE,CAAC;AACH;AACA;AACA,CAAC,KAAK,CAAC,KAAK,CAAC,GAAG,UAAU,KAAK,EAAE;AACjC,EAAE,IAAI,OAAO,KAAK,KAAK,QAAQ,EAAE;AACjC,GAAG,KAAK,GAAG,SAAS,CAAC,MAAM,CAAC,IAAI,CAAC,SAAS,CAAC,EAAE,QAAQ,CAAC,CAAC;AACvD,GAAG;AACH,EAAE,OAAO,IAAI,KAAK,CAAC,KAAK,EAAE,KAAK,CAAC,CAAC;AACjC,EAAE,CAAC;AACH,CAAC,CAAC,CAAC;AACH;AACA,SAAS,OAAO,CAAC,GAAG,EAAE,MAAM,EAAE;AAC9B,CAAC,OAAO,MAAM,CAAC,GAAG,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC,CAAC;AACpC,CAAC;AACD;AACA,SAAS,YAAY,CAAC,MAAM,EAAE;AAC9B,CAAC,OAAO,UAAU,GAAG,EAAE;AACvB,EAAE,OAAO,OAAO,CAAC,GAAG,EAAE,MAAM,CAAC,CAAC;AAC9B,EAAE,CAAC;AACH,CAAC;AACD;AACA,SAAS,MAAM,CAAC,KAAK,EAAE,OAAO,EAAE,QAAQ,EAAE;AAC1C,CAAC,KAAK,GAAG,KAAK,CAAC,OAAO,CAAC,KAAK,CAAC,GAAG,KAAK,GAAG,CAAC,KAAK,CAAC,CAAC;AAChD;AACA,CAAC,KAAK,CAAC,OAAO,CAAC,UAAU,CAAC,EAAE;AAC5B,EAAE,CAAC,QAAQ,CAAC,CAAC,CAAC,KAAK,QAAQ,CAAC,CAAC,CAAC,GAAG,EAAE,CAAC,EAAE,OAAO,CAAC,GAAG,QAAQ,CAAC;AAC1D,EAAE,CAAC,CAAC;AACJ;AACA,CAAC,KAAK,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC;AAClB;AACA,CAAC,OAAO,UAAU,GAAG,EAAE;AACvB,EAAE,IAAI,MAAM,CAAC;AACb;AACA,EAAE,IAAI,SAAS,CAAC,MAAM,EAAE;AACxB,GAAG,IAAI,QAAQ,EAAE;AACjB,IAAI,GAAG,GAAG,QAAQ,CAAC,GAAG,CAAC,CAAC;AACxB,IAAI;AACJ;AACA,GAAG,MAAM,GAAG,IAAI,CAAC,KAAK,CAAC,EAAE,CAAC;AAC1B,GAAG,MAAM,CAAC,KAAK,CAAC,OAAO,CAAC,GAAG,GAAG,CAAC;AAC/B,GAAG,OAAO,MAAM,CAAC;AACjB,GAAG;AACH;AACA,EAAE,MAAM,GAAG,IAAI,CAAC,KAAK,CAAC,EAAE,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC;AACxC,EAAE,IAAI,QAAQ,EAAE;AAChB,GAAG,MAAM,GAAG,QAAQ,CAAC,MAAM,CAAC,CAAC;AAC7B,GAAG;AACH;AACA,EAAE,OAAO,MAAM,CAAC;AAChB,EAAE,CAAC;AACH,CAAC;AACD;AACA,SAAS,KAAK,CAAC,GAAG,EAAE;AACpB,CAAC,OAAO,UAAU,CAAC,EAAE;AACrB,EAAE,OAAO,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,IAAI,CAAC,GAAG,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC,CAAC;AACvC,EAAE,CAAC;AACH,CAAC;AACD;AACA,SAAS,WAAW,CAAC,GAAG,EAAE;AAC1B,CAAC,OAAO,KAAK,CAAC,OAAO,CAAC,GAAG,CAAC,GAAG,GAAG,GAAG,CAAC,GAAG,CAAC,CAAC;AACzC,CAAC;AACD;AACA,SAAS,SAAS,CAAC,GAAG,EAAE,MAAM,EAAE;AAChC,CAAC,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,MAAM,EAAE,CAAC,EAAE,EAAE;AAClC,EAAE,IAAI,OAAO,GAAG,CAAC,CAAC,CAAC,KAAK,QAAQ,EAAE;AAClC,GAAG,GAAG,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;AACd,GAAG;AACH,EAAE;AACF;AACA,CAAC,OAAO,GAAG,CAAC;AACZ,CAAC;AACD;AACA,IAAA,KAAc,GAAG;;SC7dD,0BAA0B,CAAC,UAA0B,EAAE,UAA0B,EAAE,iBAAyB,EAAA;AACxH,IAAA,MAAM,eAAe,GAAGC,KAAK,CAAC,UAAU,CAAC,CAAC;AAC1C,IAAA,MAAM,eAAe,GAAGA,KAAK,CAAC,UAAU,CAAC,CAAC;AAE1C,IAAA,MAAM,EAAC,CAAC,EAAC,CAAC,EAAC,GAAG,eAAe,CAAC,GAAG,EAAE,CAAC,MAAM,EAAE,CAAC;IAE7C,IAAI,QAAQ,GAAG,eAAe,CAAC;IAE/B,OAAO,QAAQ,CAAC,QAAQ,CAAC,eAAe,CAAC,GAAG,iBAAiB,EAAE;AAC3D,QAAA,IAAI,QAAQ,CAAC,SAAS,EAAE,IAAI,GAAG,EAAE;YAC7B,MAAM;AACT,SAAA;AAED,QAAA,QAAQ,GAAGA,KAAK,CAAC,EAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,QAAQ,CAAC,SAAS,EAAE,GAAG,CAAC,EAAC,CAAC,CAAC;AACzD,KAAA;AAED,IAAA,OAAO,QAAQ,CAAC;AACpB,CAAC;SAEe,yBAAyB,CAAC,UAA0B,EAAE,UAA0B,EAAE,iBAAyB,EAAA;AACvH,IAAA,MAAM,eAAe,GAAGA,KAAK,CAAC,UAAU,CAAC,CAAC;AAC1C,IAAA,MAAM,eAAe,GAAGA,KAAK,CAAC,UAAU,CAAC,CAAC;AAE1C,IAAA,MAAM,EAAC,CAAC,EAAC,CAAC,EAAC,GAAG,eAAe,CAAC,GAAG,EAAE,CAAC,MAAM,EAAE,CAAC;IAE7C,IAAI,QAAQ,GAAG,eAAe,CAAC;IAE/B,OAAO,QAAQ,CAAC,QAAQ,CAAC,eAAe,CAAC,GAAG,iBAAiB,EAAE;AAC3D,QAAA,IAAI,QAAQ,CAAC,SAAS,EAAE,IAAI,CAAC,EAAE;YAC3B,MAAM;AACT,SAAA;AAED,QAAA,QAAQ,GAAGA,KAAK,CAAC,EAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,QAAQ,CAAC,SAAS,EAAE,GAAG,CAAC,EAAC,CAAC,CAAC;AACzD,KAAA;AAED,IAAA,OAAO,QAAQ,CAAC;AACpB,CAAC;AAEK,SAAU,2BAA2B,CAAC,UAA0B,EAAA;AAClE,IAAA,MAAM,eAAe,GAAGA,KAAK,CAAC,UAAU,CAAC,CAAC;AAE1C,IAAA,MAAM,KAAK,GAAGA,KAAK,CAAC,EAAC,CAAC,EAAE,GAAG,EAAE,CAAC,EAAE,GAAG,EAAE,CAAC,EAAE,GAAG,EAAC,CAAC,CAAC;AAC9C,IAAA,MAAM,KAAK,GAAGA,KAAK,CAAC,EAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAC,CAAC,CAAC;;IAGxC,MAAM,GAAG,IACL,eAAe,CAAC,GAAG,EAAE,GAAG,KAAK;AAC7B,QAAA,eAAe,CAAC,KAAK,EAAE,GAAG,KAAK;AAC/B,QAAA,eAAe,CAAC,CAAC,EAAE,GAAG,KAAK,CAC9B,CAAC;AAEF,IAAA,OAAO,CAAC,GAAG,IAAI,GAAG,IAAI,KAAK,GAAG,KAAK,CAAC;AACxC;;;;"}