!function(r,e){"object"==typeof exports&&"undefined"!=typeof module?e(exports):"function"==typeof define&&define.amd?define(["exports"],e):e((r="undefined"!=typeof globalThis?globalThis:r||self).GhostContentAPI={})}(this,(function(r){"use strict";function e(r,e){return r(e={exports:{}},e.exports),e.exports}var n={aliceblue:[240,248,255],antiquewhite:[250,235,215],aqua:[0,255,255],aquamarine:[127,255,212],azure:[240,255,255],beige:[245,245,220],bisque:[255,228,196],black:[0,0,0],blanchedalmond:[255,235,205],blue:[0,0,255],blueviolet:[138,43,226],brown:[165,42,42],burlywood:[222,184,135],cadetblue:[95,158,160],chartreuse:[127,255,0],chocolate:[210,105,30],coral:[255,127,80],cornflowerblue:[100,149,237],cornsilk:[255,248,220],crimson:[220,20,60],cyan:[0,255,255],darkblue:[0,0,139],darkcyan:[0,139,139],darkgoldenrod:[184,134,11],darkgray:[169,169,169],darkgreen:[0,100,0],darkgrey:[169,169,169],darkkhaki:[189,183,107],darkmagenta:[139,0,139],darkolivegreen:[85,107,47],darkorange:[255,140,0],darkorchid:[153,50,204],darkred:[139,0,0],darksalmon:[233,150,122],darkseagreen:[143,188,143],darkslateblue:[72,61,139],darkslategray:[47,79,79],darkslategrey:[47,79,79],darkturquoise:[0,206,209],darkviolet:[148,0,211],deeppink:[255,20,147],deepskyblue:[0,191,255],dimgray:[105,105,105],dimgrey:[105,105,105],dodgerblue:[30,144,255],firebrick:[178,34,34],floralwhite:[255,250,240],forestgreen:[34,139,34],fuchsia:[255,0,255],gainsboro:[220,220,220],ghostwhite:[248,248,255],gold:[255,215,0],goldenrod:[218,165,32],gray:[128,128,128],green:[0,128,0],greenyellow:[173,255,47],grey:[128,128,128],honeydew:[240,255,240],hotpink:[255,105,180],indianred:[205,92,92],indigo:[75,0,130],ivory:[255,255,240],khaki:[240,230,140],lavender:[230,230,250],lavenderblush:[255,240,245],lawngreen:[124,252,0],lemonchiffon:[255,250,205],lightblue:[173,216,230],lightcoral:[240,128,128],lightcyan:[224,255,255],lightgoldenrodyellow:[250,250,210],lightgray:[211,211,211],lightgreen:[144,238,144],lightgrey:[211,211,211],lightpink:[255,182,193],lightsalmon:[255,160,122],lightseagreen:[32,178,170],lightskyblue:[135,206,250],lightslategray:[119,136,153],lightslategrey:[119,136,153],lightsteelblue:[176,196,222],lightyellow:[255,255,224],lime:[0,255,0],limegreen:[50,205,50],linen:[250,240,230],magenta:[255,0,255],maroon:[128,0,0],mediumaquamarine:[102,205,170],mediumblue:[0,0,205],mediumorchid:[186,85,211],mediumpurple:[147,112,219],mediumseagreen:[60,179,113],mediumslateblue:[123,104,238],mediumspringgreen:[0,250,154],mediumturquoise:[72,209,204],mediumvioletred:[199,21,133],midnightblue:[25,25,112],mintcream:[245,255,250],mistyrose:[255,228,225],moccasin:[255,228,181],navajowhite:[255,222,173],navy:[0,0,128],oldlace:[253,245,230],olive:[128,128,0],olivedrab:[107,142,35],orange:[255,165,0],orangered:[255,69,0],orchid:[218,112,214],palegoldenrod:[238,232,170],palegreen:[152,251,152],paleturquoise:[175,238,238],palevioletred:[219,112,147],papayawhip:[255,239,213],peachpuff:[255,218,185],peru:[205,133,63],pink:[255,192,203],plum:[221,160,221],powderblue:[176,224,230],purple:[128,0,128],rebeccapurple:[102,51,153],red:[255,0,0],rosybrown:[188,143,143],royalblue:[65,105,225],saddlebrown:[139,69,19],salmon:[250,128,114],sandybrown:[244,164,96],seagreen:[46,139,87],seashell:[255,245,238],sienna:[160,82,45],silver:[192,192,192],skyblue:[135,206,235],slateblue:[106,90,205],slategray:[112,128,144],slategrey:[112,128,144],snow:[255,250,250],springgreen:[0,255,127],steelblue:[70,130,180],tan:[210,180,140],teal:[0,128,128],thistle:[216,191,216],tomato:[255,99,71],turquoise:[64,224,208],violet:[238,130,238],wheat:[245,222,179],white:[255,255,255],whitesmoke:[245,245,245],yellow:[255,255,0],yellowgreen:[154,205,50]},t=e((function(r){var e=Array.prototype.concat,n=Array.prototype.slice,t=r.exports=function(r){for(var t,a=[],o=0,l=r.length;o<l;o++){var i=r[o];(t=i)&&"string"!=typeof t&&(t instanceof Array||Array.isArray(t)||t.length>=0&&(t.splice instanceof Function||Object.getOwnPropertyDescriptor(t,t.length-1)&&"String"!==t.constructor.name))?a=e.call(a,n.call(i)):a.push(i)}return a};t.wrap=function(r){return function(){return r(t(arguments))}}})),a=e((function(r){var e=Object.hasOwnProperty,a=Object.create(null);for(var o in n)e.call(n,o)&&(a[n[o]]=o);var l=r.exports={to:{},get:{}};function i(r,e,n){return Math.min(Math.max(e,r),n)}function s(r){var e=Math.round(r).toString(16).toUpperCase();return e.length<2?"0"+e:e}l.get=function(r){var e,n;switch(r.substring(0,3).toLowerCase()){case"hsl":e=l.get.hsl(r),n="hsl";break;case"hwb":e=l.get.hwb(r),n="hwb";break;default:e=l.get.rgb(r),n="rgb"}return e?{model:n,value:e}:null},l.get.rgb=function(r){if(!r)return null;var t,a,o,l=[0,0,0,1];if(t=r.match(/^#([a-f0-9]{6})([a-f0-9]{2})?$/i)){for(o=t[2],t=t[1],a=0;a<3;a++){var s=2*a;l[a]=parseInt(t.slice(s,s+2),16)}o&&(l[3]=parseInt(o,16)/255)}else if(t=r.match(/^#([a-f0-9]{3,4})$/i)){for(o=(t=t[1])[3],a=0;a<3;a++)l[a]=parseInt(t[a]+t[a],16);o&&(l[3]=parseInt(o+o,16)/255)}else if(t=r.match(/^rgba?\(\s*([+-]?\d+)(?=[\s,])\s*(?:,\s*)?([+-]?\d+)(?=[\s,])\s*(?:,\s*)?([+-]?\d+)\s*(?:[,|\/]\s*([+-]?[\d\.]+)(%?)\s*)?\)$/)){for(a=0;a<3;a++)l[a]=parseInt(t[a+1],0);t[4]&&(t[5]?l[3]=.01*parseFloat(t[4]):l[3]=parseFloat(t[4]))}else{if(!(t=r.match(/^rgba?\(\s*([+-]?[\d\.]+)\%\s*,?\s*([+-]?[\d\.]+)\%\s*,?\s*([+-]?[\d\.]+)\%\s*(?:[,|\/]\s*([+-]?[\d\.]+)(%?)\s*)?\)$/)))return(t=r.match(/^(\w+)$/))?"transparent"===t[1]?[0,0,0,0]:e.call(n,t[1])?((l=n[t[1]])[3]=1,l):null:null;for(a=0;a<3;a++)l[a]=Math.round(2.55*parseFloat(t[a+1]));t[4]&&(t[5]?l[3]=.01*parseFloat(t[4]):l[3]=parseFloat(t[4]))}for(a=0;a<3;a++)l[a]=i(l[a],0,255);return l[3]=i(l[3],0,1),l},l.get.hsl=function(r){if(!r)return null;var e=r.match(/^hsla?\(\s*([+-]?(?:\d{0,3}\.)?\d+)(?:deg)?\s*,?\s*([+-]?[\d\.]+)%\s*,?\s*([+-]?[\d\.]+)%\s*(?:[,|\/]\s*([+-]?(?=\.\d|\d)(?:0|[1-9]\d*)?(?:\.\d*)?(?:[eE][+-]?\d+)?)\s*)?\)$/);if(e){var n=parseFloat(e[4]);return[(parseFloat(e[1])%360+360)%360,i(parseFloat(e[2]),0,100),i(parseFloat(e[3]),0,100),i(isNaN(n)?1:n,0,1)]}return null},l.get.hwb=function(r){if(!r)return null;var e=r.match(/^hwb\(\s*([+-]?\d{0,3}(?:\.\d+)?)(?:deg)?\s*,\s*([+-]?[\d\.]+)%\s*,\s*([+-]?[\d\.]+)%\s*(?:,\s*([+-]?(?=\.\d|\d)(?:0|[1-9]\d*)?(?:\.\d*)?(?:[eE][+-]?\d+)?)\s*)?\)$/);if(e){var n=parseFloat(e[4]);return[(parseFloat(e[1])%360+360)%360,i(parseFloat(e[2]),0,100),i(parseFloat(e[3]),0,100),i(isNaN(n)?1:n,0,1)]}return null},l.to.hex=function(){var r=t(arguments);return"#"+s(r[0])+s(r[1])+s(r[2])+(r[3]<1?s(Math.round(255*r[3])):"")},l.to.rgb=function(){var r=t(arguments);return r.length<4||1===r[3]?"rgb("+Math.round(r[0])+", "+Math.round(r[1])+", "+Math.round(r[2])+")":"rgba("+Math.round(r[0])+", "+Math.round(r[1])+", "+Math.round(r[2])+", "+r[3]+")"},l.to.rgb.percent=function(){var r=t(arguments),e=Math.round(r[0]/255*100),n=Math.round(r[1]/255*100),a=Math.round(r[2]/255*100);return r.length<4||1===r[3]?"rgb("+e+"%, "+n+"%, "+a+"%)":"rgba("+e+"%, "+n+"%, "+a+"%, "+r[3]+")"},l.to.hsl=function(){var r=t(arguments);return r.length<4||1===r[3]?"hsl("+r[0]+", "+r[1]+"%, "+r[2]+"%)":"hsla("+r[0]+", "+r[1]+"%, "+r[2]+"%, "+r[3]+")"},l.to.hwb=function(){var r=t(arguments),e="";return r.length>=4&&1!==r[3]&&(e=", "+r[3]),"hwb("+r[0]+", "+r[1]+"%, "+r[2]+"%"+e+")"},l.to.keyword=function(r){return a[r.slice(0,3)]}}));a.to,a.get;var o={aliceblue:[240,248,255],antiquewhite:[250,235,215],aqua:[0,255,255],aquamarine:[127,255,212],azure:[240,255,255],beige:[245,245,220],bisque:[255,228,196],black:[0,0,0],blanchedalmond:[255,235,205],blue:[0,0,255],blueviolet:[138,43,226],brown:[165,42,42],burlywood:[222,184,135],cadetblue:[95,158,160],chartreuse:[127,255,0],chocolate:[210,105,30],coral:[255,127,80],cornflowerblue:[100,149,237],cornsilk:[255,248,220],crimson:[220,20,60],cyan:[0,255,255],darkblue:[0,0,139],darkcyan:[0,139,139],darkgoldenrod:[184,134,11],darkgray:[169,169,169],darkgreen:[0,100,0],darkgrey:[169,169,169],darkkhaki:[189,183,107],darkmagenta:[139,0,139],darkolivegreen:[85,107,47],darkorange:[255,140,0],darkorchid:[153,50,204],darkred:[139,0,0],darksalmon:[233,150,122],darkseagreen:[143,188,143],darkslateblue:[72,61,139],darkslategray:[47,79,79],darkslategrey:[47,79,79],darkturquoise:[0,206,209],darkviolet:[148,0,211],deeppink:[255,20,147],deepskyblue:[0,191,255],dimgray:[105,105,105],dimgrey:[105,105,105],dodgerblue:[30,144,255],firebrick:[178,34,34],floralwhite:[255,250,240],forestgreen:[34,139,34],fuchsia:[255,0,255],gainsboro:[220,220,220],ghostwhite:[248,248,255],gold:[255,215,0],goldenrod:[218,165,32],gray:[128,128,128],green:[0,128,0],greenyellow:[173,255,47],grey:[128,128,128],honeydew:[240,255,240],hotpink:[255,105,180],indianred:[205,92,92],indigo:[75,0,130],ivory:[255,255,240],khaki:[240,230,140],lavender:[230,230,250],lavenderblush:[255,240,245],lawngreen:[124,252,0],lemonchiffon:[255,250,205],lightblue:[173,216,230],lightcoral:[240,128,128],lightcyan:[224,255,255],lightgoldenrodyellow:[250,250,210],lightgray:[211,211,211],lightgreen:[144,238,144],lightgrey:[211,211,211],lightpink:[255,182,193],lightsalmon:[255,160,122],lightseagreen:[32,178,170],lightskyblue:[135,206,250],lightslategray:[119,136,153],lightslategrey:[119,136,153],lightsteelblue:[176,196,222],lightyellow:[255,255,224],lime:[0,255,0],limegreen:[50,205,50],linen:[250,240,230],magenta:[255,0,255],maroon:[128,0,0],mediumaquamarine:[102,205,170],mediumblue:[0,0,205],mediumorchid:[186,85,211],mediumpurple:[147,112,219],mediumseagreen:[60,179,113],mediumslateblue:[123,104,238],mediumspringgreen:[0,250,154],mediumturquoise:[72,209,204],mediumvioletred:[199,21,133],midnightblue:[25,25,112],mintcream:[245,255,250],mistyrose:[255,228,225],moccasin:[255,228,181],navajowhite:[255,222,173],navy:[0,0,128],oldlace:[253,245,230],olive:[128,128,0],olivedrab:[107,142,35],orange:[255,165,0],orangered:[255,69,0],orchid:[218,112,214],palegoldenrod:[238,232,170],palegreen:[152,251,152],paleturquoise:[175,238,238],palevioletred:[219,112,147],papayawhip:[255,239,213],peachpuff:[255,218,185],peru:[205,133,63],pink:[255,192,203],plum:[221,160,221],powderblue:[176,224,230],purple:[128,0,128],rebeccapurple:[102,51,153],red:[255,0,0],rosybrown:[188,143,143],royalblue:[65,105,225],saddlebrown:[139,69,19],salmon:[250,128,114],sandybrown:[244,164,96],seagreen:[46,139,87],seashell:[255,245,238],sienna:[160,82,45],silver:[192,192,192],skyblue:[135,206,235],slateblue:[106,90,205],slategray:[112,128,144],slategrey:[112,128,144],snow:[255,250,250],springgreen:[0,255,127],steelblue:[70,130,180],tan:[210,180,140],teal:[0,128,128],thistle:[216,191,216],tomato:[255,99,71],turquoise:[64,224,208],violet:[238,130,238],wheat:[245,222,179],white:[255,255,255],whitesmoke:[245,245,245],yellow:[255,255,0],yellowgreen:[154,205,50]},l=e((function(r){var e={};for(var n in o)o.hasOwnProperty(n)&&(e[o[n]]=n);var t=r.exports={rgb:{channels:3,labels:"rgb"},hsl:{channels:3,labels:"hsl"},hsv:{channels:3,labels:"hsv"},hwb:{channels:3,labels:"hwb"},cmyk:{channels:4,labels:"cmyk"},xyz:{channels:3,labels:"xyz"},lab:{channels:3,labels:"lab"},lch:{channels:3,labels:"lch"},hex:{channels:1,labels:["hex"]},keyword:{channels:1,labels:["keyword"]},ansi16:{channels:1,labels:["ansi16"]},ansi256:{channels:1,labels:["ansi256"]},hcg:{channels:3,labels:["h","c","g"]},apple:{channels:3,labels:["r16","g16","b16"]},gray:{channels:1,labels:["gray"]}};for(var a in t)if(t.hasOwnProperty(a)){if(!("channels"in t[a]))throw new Error("missing channels property: "+a);if(!("labels"in t[a]))throw new Error("missing channel labels property: "+a);if(t[a].labels.length!==t[a].channels)throw new Error("channel and label counts mismatch: "+a);var l=t[a].channels,i=t[a].labels;delete t[a].channels,delete t[a].labels,Object.defineProperty(t[a],"channels",{value:l}),Object.defineProperty(t[a],"labels",{value:i})}t.rgb.hsl=function(r){var e,n,t=r[0]/255,a=r[1]/255,o=r[2]/255,l=Math.min(t,a,o),i=Math.max(t,a,o),s=i-l;return i===l?e=0:t===i?e=(a-o)/s:a===i?e=2+(o-t)/s:o===i&&(e=4+(t-a)/s),(e=Math.min(60*e,360))<0&&(e+=360),n=(l+i)/2,[e,100*(i===l?0:n<=.5?s/(i+l):s/(2-i-l)),100*n]},t.rgb.hsv=function(r){var e,n,t,a,o,l=r[0]/255,i=r[1]/255,s=r[2]/255,h=Math.max(l,i,s),u=h-Math.min(l,i,s),c=function(r){return(h-r)/6/u+.5};return 0===u?a=o=0:(o=u/h,e=c(l),n=c(i),t=c(s),l===h?a=t-n:i===h?a=1/3+e-t:s===h&&(a=2/3+n-e),a<0?a+=1:a>1&&(a-=1)),[360*a,100*o,100*h]},t.rgb.hwb=function(r){var e=r[0],n=r[1],a=r[2];return[t.rgb.hsl(r)[0],100*(1/255*Math.min(e,Math.min(n,a))),100*(a=1-1/255*Math.max(e,Math.max(n,a)))]},t.rgb.cmyk=function(r){var e,n=r[0]/255,t=r[1]/255,a=r[2]/255;return[100*((1-n-(e=Math.min(1-n,1-t,1-a)))/(1-e)||0),100*((1-t-e)/(1-e)||0),100*((1-a-e)/(1-e)||0),100*e]},t.rgb.keyword=function(r){var n=e[r];if(n)return n;var t,a,l,i=1/0;for(var s in o)if(o.hasOwnProperty(s)){var h=o[s],u=(a=r,l=h,Math.pow(a[0]-l[0],2)+Math.pow(a[1]-l[1],2)+Math.pow(a[2]-l[2],2));u<i&&(i=u,t=s)}return t},t.keyword.rgb=function(r){return o[r]},t.rgb.xyz=function(r){var e=r[0]/255,n=r[1]/255,t=r[2]/255;return[100*(.4124*(e=e>.04045?Math.pow((e+.055)/1.055,2.4):e/12.92)+.3576*(n=n>.04045?Math.pow((n+.055)/1.055,2.4):n/12.92)+.1805*(t=t>.04045?Math.pow((t+.055)/1.055,2.4):t/12.92)),100*(.2126*e+.7152*n+.0722*t),100*(.0193*e+.1192*n+.9505*t)]},t.rgb.lab=function(r){var e=t.rgb.xyz(r),n=e[0],a=e[1],o=e[2];return a/=100,o/=108.883,n=(n/=95.047)>.008856?Math.pow(n,1/3):7.787*n+16/116,[116*(a=a>.008856?Math.pow(a,1/3):7.787*a+16/116)-16,500*(n-a),200*(a-(o=o>.008856?Math.pow(o,1/3):7.787*o+16/116))]},t.hsl.rgb=function(r){var e,n,t,a,o,l=r[0]/360,i=r[1]/100,s=r[2]/100;if(0===i)return[o=255*s,o,o];e=2*s-(n=s<.5?s*(1+i):s+i-s*i),a=[0,0,0];for(var h=0;h<3;h++)(t=l+1/3*-(h-1))<0&&t++,t>1&&t--,o=6*t<1?e+6*(n-e)*t:2*t<1?n:3*t<2?e+(n-e)*(2/3-t)*6:e,a[h]=255*o;return a},t.hsl.hsv=function(r){var e=r[0],n=r[1]/100,t=r[2]/100,a=n,o=Math.max(t,.01);return n*=(t*=2)<=1?t:2-t,a*=o<=1?o:2-o,[e,100*(0===t?2*a/(o+a):2*n/(t+n)),100*((t+n)/2)]},t.hsv.rgb=function(r){var e=r[0]/60,n=r[1]/100,t=r[2]/100,a=Math.floor(e)%6,o=e-Math.floor(e),l=255*t*(1-n),i=255*t*(1-n*o),s=255*t*(1-n*(1-o));switch(t*=255,a){case 0:return[t,s,l];case 1:return[i,t,l];case 2:return[l,t,s];case 3:return[l,i,t];case 4:return[s,l,t];case 5:return[t,l,i]}},t.hsv.hsl=function(r){var e,n,t,a=r[0],o=r[1]/100,l=r[2]/100,i=Math.max(l,.01);return t=(2-o)*l,n=o*i,[a,100*(n=(n/=(e=(2-o)*i)<=1?e:2-e)||0),100*(t/=2)]},t.hwb.rgb=function(r){var e,n,t,a,o,l,i,s=r[0]/360,h=r[1]/100,u=r[2]/100,c=h+u;switch(c>1&&(h/=c,u/=c),t=6*s-(e=Math.floor(6*s)),0!=(1&e)&&(t=1-t),a=h+t*((n=1-u)-h),e){default:case 6:case 0:o=n,l=a,i=h;break;case 1:o=a,l=n,i=h;break;case 2:o=h,l=n,i=a;break;case 3:o=h,l=a,i=n;break;case 4:o=a,l=h,i=n;break;case 5:o=n,l=h,i=a}return[255*o,255*l,255*i]},t.cmyk.rgb=function(r){var e=r[0]/100,n=r[1]/100,t=r[2]/100,a=r[3]/100;return[255*(1-Math.min(1,e*(1-a)+a)),255*(1-Math.min(1,n*(1-a)+a)),255*(1-Math.min(1,t*(1-a)+a))]},t.xyz.rgb=function(r){var e,n,t,a=r[0]/100,o=r[1]/100,l=r[2]/100;return n=-.9689*a+1.8758*o+.0415*l,t=.0557*a+-.204*o+1.057*l,e=(e=3.2406*a+-1.5372*o+-.4986*l)>.0031308?1.055*Math.pow(e,1/2.4)-.055:12.92*e,n=n>.0031308?1.055*Math.pow(n,1/2.4)-.055:12.92*n,t=t>.0031308?1.055*Math.pow(t,1/2.4)-.055:12.92*t,[255*(e=Math.min(Math.max(0,e),1)),255*(n=Math.min(Math.max(0,n),1)),255*(t=Math.min(Math.max(0,t),1))]},t.xyz.lab=function(r){var e=r[0],n=r[1],t=r[2];return n/=100,t/=108.883,e=(e/=95.047)>.008856?Math.pow(e,1/3):7.787*e+16/116,[116*(n=n>.008856?Math.pow(n,1/3):7.787*n+16/116)-16,500*(e-n),200*(n-(t=t>.008856?Math.pow(t,1/3):7.787*t+16/116))]},t.lab.xyz=function(r){var e,n,t,a=r[0];e=r[1]/500+(n=(a+16)/116),t=n-r[2]/200;var o=Math.pow(n,3),l=Math.pow(e,3),i=Math.pow(t,3);return n=o>.008856?o:(n-16/116)/7.787,e=l>.008856?l:(e-16/116)/7.787,t=i>.008856?i:(t-16/116)/7.787,[e*=95.047,n*=100,t*=108.883]},t.lab.lch=function(r){var e,n=r[0],t=r[1],a=r[2];return(e=360*Math.atan2(a,t)/2/Math.PI)<0&&(e+=360),[n,Math.sqrt(t*t+a*a),e]},t.lch.lab=function(r){var e,n=r[0],t=r[1];return e=r[2]/360*2*Math.PI,[n,t*Math.cos(e),t*Math.sin(e)]},t.rgb.ansi16=function(r){var e=r[0],n=r[1],a=r[2],o=1 in arguments?arguments[1]:t.rgb.hsv(r)[2];if(0===(o=Math.round(o/50)))return 30;var l=30+(Math.round(a/255)<<2|Math.round(n/255)<<1|Math.round(e/255));return 2===o&&(l+=60),l},t.hsv.ansi16=function(r){return t.rgb.ansi16(t.hsv.rgb(r),r[2])},t.rgb.ansi256=function(r){var e=r[0],n=r[1],t=r[2];return e===n&&n===t?e<8?16:e>248?231:Math.round((e-8)/247*24)+232:16+36*Math.round(e/255*5)+6*Math.round(n/255*5)+Math.round(t/255*5)},t.ansi16.rgb=function(r){var e=r%10;if(0===e||7===e)return r>50&&(e+=3.5),[e=e/10.5*255,e,e];var n=.5*(1+~~(r>50));return[(1&e)*n*255,(e>>1&1)*n*255,(e>>2&1)*n*255]},t.ansi256.rgb=function(r){if(r>=232){var e=10*(r-232)+8;return[e,e,e]}var n;return r-=16,[Math.floor(r/36)/5*255,Math.floor((n=r%36)/6)/5*255,n%6/5*255]},t.rgb.hex=function(r){var e=(((255&Math.round(r[0]))<<16)+((255&Math.round(r[1]))<<8)+(255&Math.round(r[2]))).toString(16).toUpperCase();return"000000".substring(e.length)+e},t.hex.rgb=function(r){var e=r.toString(16).match(/[a-f0-9]{6}|[a-f0-9]{3}/i);if(!e)return[0,0,0];var n=e[0];3===e[0].length&&(n=n.split("").map((function(r){return r+r})).join(""));var t=parseInt(n,16);return[t>>16&255,t>>8&255,255&t]},t.rgb.hcg=function(r){var e,n=r[0]/255,t=r[1]/255,a=r[2]/255,o=Math.max(Math.max(n,t),a),l=Math.min(Math.min(n,t),a),i=o-l;return e=i<=0?0:o===n?(t-a)/i%6:o===t?2+(a-n)/i:4+(n-t)/i+4,e/=6,[360*(e%=1),100*i,100*(i<1?l/(1-i):0)]},t.hsl.hcg=function(r){var e=r[1]/100,n=r[2]/100,t=1,a=0;return(t=n<.5?2*e*n:2*e*(1-n))<1&&(a=(n-.5*t)/(1-t)),[r[0],100*t,100*a]},t.hsv.hcg=function(r){var e=r[1]/100,n=r[2]/100,t=e*n,a=0;return t<1&&(a=(n-t)/(1-t)),[r[0],100*t,100*a]},t.hcg.rgb=function(r){var e=r[0]/360,n=r[1]/100,t=r[2]/100;if(0===n)return[255*t,255*t,255*t];var a,o=[0,0,0],l=e%1*6,i=l%1,s=1-i;switch(Math.floor(l)){case 0:o[0]=1,o[1]=i,o[2]=0;break;case 1:o[0]=s,o[1]=1,o[2]=0;break;case 2:o[0]=0,o[1]=1,o[2]=i;break;case 3:o[0]=0,o[1]=s,o[2]=1;break;case 4:o[0]=i,o[1]=0,o[2]=1;break;default:o[0]=1,o[1]=0,o[2]=s}return a=(1-n)*t,[255*(n*o[0]+a),255*(n*o[1]+a),255*(n*o[2]+a)]},t.hcg.hsv=function(r){var e=r[1]/100,n=e+r[2]/100*(1-e),t=0;return n>0&&(t=e/n),[r[0],100*t,100*n]},t.hcg.hsl=function(r){var e=r[1]/100,n=r[2]/100*(1-e)+.5*e,t=0;return n>0&&n<.5?t=e/(2*n):n>=.5&&n<1&&(t=e/(2*(1-n))),[r[0],100*t,100*n]},t.hcg.hwb=function(r){var e=r[1]/100,n=e+r[2]/100*(1-e);return[r[0],100*(n-e),100*(1-n)]},t.hwb.hcg=function(r){var e=r[1]/100,n=1-r[2]/100,t=n-e,a=0;return t<1&&(a=(n-t)/(1-t)),[r[0],100*t,100*a]},t.apple.rgb=function(r){return[r[0]/65535*255,r[1]/65535*255,r[2]/65535*255]},t.rgb.apple=function(r){return[r[0]/255*65535,r[1]/255*65535,r[2]/255*65535]},t.gray.rgb=function(r){return[r[0]/100*255,r[0]/100*255,r[0]/100*255]},t.gray.hsl=t.gray.hsv=function(r){return[0,0,r[0]]},t.gray.hwb=function(r){return[0,100,r[0]]},t.gray.cmyk=function(r){return[0,0,0,r[0]]},t.gray.lab=function(r){return[r[0],0,0]},t.gray.hex=function(r){var e=255&Math.round(r[0]/100*255),n=((e<<16)+(e<<8)+e).toString(16).toUpperCase();return"000000".substring(n.length)+n},t.rgb.gray=function(r){return[(r[0]+r[1]+r[2])/3/255*100]}}));function i(r){var e=function(){for(var r={},e=Object.keys(l),n=e.length,t=0;t<n;t++)r[e[t]]={distance:-1,parent:null};return r}(),n=[r];for(e[r].distance=0;n.length;)for(var t=n.pop(),a=Object.keys(l[t]),o=a.length,i=0;i<o;i++){var s=a[i],h=e[s];-1===h.distance&&(h.distance=e[t].distance+1,h.parent=t,n.unshift(s))}return e}function s(r,e){return function(n){return e(r(n))}}function h(r,e){for(var n=[e[r].parent,r],t=l[e[r].parent][r],a=e[r].parent;e[a].parent;)n.unshift(e[a].parent),t=s(l[e[a].parent][a],t),a=e[a].parent;return t.conversion=n,t}l.rgb,l.hsl,l.hsv,l.hwb,l.cmyk,l.xyz,l.lab,l.lch,l.hex,l.keyword,l.ansi16,l.ansi256,l.hcg,l.apple,l.gray;var u={};Object.keys(l).forEach((function(r){u[r]={},Object.defineProperty(u[r],"channels",{value:l[r].channels}),Object.defineProperty(u[r],"labels",{value:l[r].labels});var e=function(r){for(var e=i(r),n={},t=Object.keys(e),a=t.length,o=0;o<a;o++){var l=t[o];null!==e[l].parent&&(n[l]=h(l,e))}return n}(r);Object.keys(e).forEach((function(n){var t=e[n];u[r][n]=function(r){var e=function(e){if(null==e)return e;arguments.length>1&&(e=Array.prototype.slice.call(arguments));var n=r(e);if("object"==typeof n)for(var t=n.length,a=0;a<t;a++)n[a]=Math.round(n[a]);return n};return"conversion"in r&&(e.conversion=r.conversion),e}(t),u[r][n].raw=function(r){var e=function(e){return null==e?e:(arguments.length>1&&(e=Array.prototype.slice.call(arguments)),r(e))};return"conversion"in r&&(e.conversion=r.conversion),e}(t)}))}));var c=u,g=[].slice,d=["keyword","gray","hex"],f={};Object.keys(c).forEach((function(r){f[g.call(c[r].labels).sort().join("")]=r}));var b={};function p(r,e){if(!(this instanceof p))return new p(r,e);if(e&&e in d&&(e=null),e&&!(e in c))throw new Error("Unknown model: "+e);var n,t;if(null==r)this.model="rgb",this.color=[0,0,0],this.valpha=1;else if(r instanceof p)this.model=r.model,this.color=r.color.slice(),this.valpha=r.valpha;else if("string"==typeof r){var o=a.get(r);if(null===o)throw new Error("Unable to parse color from string: "+r);this.model=o.model,t=c[this.model].channels,this.color=o.value.slice(0,t),this.valpha="number"==typeof o.value[t]?o.value[t]:1}else if(r.length){this.model=e||"rgb",t=c[this.model].channels;var l=g.call(r,0,t);this.color=y(l,t),this.valpha="number"==typeof r[t]?r[t]:1}else if("number"==typeof r)r&=16777215,this.model="rgb",this.color=[r>>16&255,r>>8&255,255&r],this.valpha=1;else{this.valpha=1;var i=Object.keys(r);"alpha"in r&&(i.splice(i.indexOf("alpha"),1),this.valpha="number"==typeof r.alpha?r.alpha:0);var s=i.sort().join("");if(!(s in f))throw new Error("Unable to parse color from object: "+JSON.stringify(r));this.model=f[s];var h=c[this.model].labels,u=[];for(n=0;n<h.length;n++)u.push(r[h[n]]);this.color=y(u)}if(b[this.model])for(t=c[this.model].channels,n=0;n<t;n++){var v=b[this.model][n];v&&(this.color[n]=v(this.color[n]))}this.valpha=Math.max(0,Math.min(1,this.valpha)),Object.freeze&&Object.freeze(this)}function v(r,e,n){return(r=Array.isArray(r)?r:[r]).forEach((function(r){(b[r]||(b[r]=[]))[e]=n})),r=r[0],function(t){var a;return arguments.length?(n&&(t=n(t)),(a=this[r]()).color[e]=t,a):(a=this[r]().color[e],n&&(a=n(a)),a)}}function m(r){return function(e){return Math.max(0,Math.min(r,e))}}function y(r,e){for(var n=0;n<e;n++)"number"!=typeof r[n]&&(r[n]=0);return r}p.prototype={toString:function(){return this.string()},toJSON:function(){return this[this.model]()},string:function(r){var e=this.model in a.to?this:this.rgb(),n=1===(e=e.round("number"==typeof r?r:1)).valpha?e.color:e.color.concat(this.valpha);return a.to[e.model](n)},percentString:function(r){var e=this.rgb().round("number"==typeof r?r:1),n=1===e.valpha?e.color:e.color.concat(this.valpha);return a.to.rgb.percent(n)},array:function(){return 1===this.valpha?this.color.slice():this.color.concat(this.valpha)},object:function(){for(var r={},e=c[this.model].channels,n=c[this.model].labels,t=0;t<e;t++)r[n[t]]=this.color[t];return 1!==this.valpha&&(r.alpha=this.valpha),r},unitArray:function(){var r=this.rgb().color;return r[0]/=255,r[1]/=255,r[2]/=255,1!==this.valpha&&r.push(this.valpha),r},unitObject:function(){var r=this.rgb().object();return r.r/=255,r.g/=255,r.b/=255,1!==this.valpha&&(r.alpha=this.valpha),r},round:function(r){return r=Math.max(r||0,0),new p(this.color.map(function(r){return function(e){return function(r,e){return Number(r.toFixed(e))}(e,r)}}(r)).concat(this.valpha),this.model)},alpha:function(r){return arguments.length?new p(this.color.concat(Math.max(0,Math.min(1,r))),this.model):this.valpha},red:v("rgb",0,m(255)),green:v("rgb",1,m(255)),blue:v("rgb",2,m(255)),hue:v(["hsl","hsv","hsl","hwb","hcg"],0,(function(r){return(r%360+360)%360})),saturationl:v("hsl",1,m(100)),lightness:v("hsl",2,m(100)),saturationv:v("hsv",1,m(100)),value:v("hsv",2,m(100)),chroma:v("hcg",1,m(100)),gray:v("hcg",2,m(100)),white:v("hwb",1,m(100)),wblack:v("hwb",2,m(100)),cyan:v("cmyk",0,m(100)),magenta:v("cmyk",1,m(100)),yellow:v("cmyk",2,m(100)),black:v("cmyk",3,m(100)),x:v("xyz",0,m(100)),y:v("xyz",1,m(100)),z:v("xyz",2,m(100)),l:v("lab",0,m(100)),a:v("lab",1),b:v("lab",2),keyword:function(r){return arguments.length?new p(r):c[this.model].keyword(this.color)},hex:function(r){return arguments.length?new p(r):a.to.hex(this.rgb().round().color)},rgbNumber:function(){var r=this.rgb().color;return(255&r[0])<<16|(255&r[1])<<8|255&r[2]},luminosity:function(){for(var r=this.rgb().color,e=[],n=0;n<r.length;n++){var t=r[n]/255;e[n]=t<=.03928?t/12.92:Math.pow((t+.055)/1.055,2.4)}return.2126*e[0]+.7152*e[1]+.0722*e[2]},contrast:function(r){var e=this.luminosity(),n=r.luminosity();return e>n?(e+.05)/(n+.05):(n+.05)/(e+.05)},level:function(r){var e=this.contrast(r);return e>=7.1?"AAA":e>=4.5?"AA":""},isDark:function(){var r=this.rgb().color;return(299*r[0]+587*r[1]+114*r[2])/1e3<128},isLight:function(){return!this.isDark()},negate:function(){for(var r=this.rgb(),e=0;e<3;e++)r.color[e]=255-r.color[e];return r},lighten:function(r){var e=this.hsl();return e.color[2]+=e.color[2]*r,e},darken:function(r){var e=this.hsl();return e.color[2]-=e.color[2]*r,e},saturate:function(r){var e=this.hsl();return e.color[1]+=e.color[1]*r,e},desaturate:function(r){var e=this.hsl();return e.color[1]-=e.color[1]*r,e},whiten:function(r){var e=this.hwb();return e.color[1]+=e.color[1]*r,e},blacken:function(r){var e=this.hwb();return e.color[2]+=e.color[2]*r,e},grayscale:function(){var r=this.rgb().color,e=.3*r[0]+.59*r[1]+.11*r[2];return p.rgb(e,e,e)},fade:function(r){return this.alpha(this.valpha-this.valpha*r)},opaquer:function(r){return this.alpha(this.valpha+this.valpha*r)},rotate:function(r){var e=this.hsl(),n=e.color[0];return n=(n=(n+r)%360)<0?360+n:n,e.color[0]=n,e},mix:function(r,e){if(!r||!r.rgb)throw new Error('Argument to "mix" was not a Color instance, but rather an instance of '+typeof r);var n=r.rgb(),t=this.rgb(),a=void 0===e?.5:e,o=2*a-1,l=n.alpha()-t.alpha(),i=((o*l==-1?o:(o+l)/(1+o*l))+1)/2,s=1-i;return p.rgb(i*n.red()+s*t.red(),i*n.green()+s*t.green(),i*n.blue()+s*t.blue(),n.alpha()*a+t.alpha()*(1-a))}},Object.keys(c).forEach((function(r){if(-1===d.indexOf(r)){var e=c[r].channels;p.prototype[r]=function(){if(this.model===r)return new p(this);if(arguments.length)return new p(arguments,r);var n,t="number"==typeof arguments[e]?e:this.valpha;return new p((n=c[this.model][r].raw(this.color),Array.isArray(n)?n:[n]).concat(t),r)},p[r]=function(n){return"number"==typeof n&&(n=y(g.call(arguments),e)),new p(n,r)}}}));var w=p;r.Color=w,r.darkenToContrastThreshold=function(r,e,n){const t=w(r),a=w(e),{h:o,s:l}=t.hsl().object();let i=t;for(;i.contrast(a)<n&&!(i.lightness()<=0);)i=w({h:o,s:l,l:i.lightness()-5});return i},r.lightenToContrastThreshold=function(r,e,n){const t=w(r),a=w(e),{h:o,s:l}=t.hsl().object();let i=t;for(;i.contrast(a)<n&&!(i.lightness()>=100);)i=w({h:o,s:l,l:i.lightness()+5});return i},r.textColorForBackgroundColor=function(r){const e=w(r),n=w({r:255,g:255,b:255}),t=w({r:0,g:0,b:0});return.299*e.red()+.587*e.green()+.114*e.b()>=186?t:n},Object.defineProperty(r,"__esModule",{value:!0})}));
//# sourceMappingURL=color-utils.min.js.map
