{"version": 3, "file": "color-utils.min.js", "sources": ["../../../node_modules/color-name/index.js", "../../../node_modules/simple-swizzle/index.js", "../../../node_modules/simple-swizzle/node_modules/is-arrayish/index.js", "../../../node_modules/color-string/index.js", "../../../node_modules/color-convert/node_modules/color-name/index.js", "../../../node_modules/color-convert/conversions.js", "../../../node_modules/color-convert/route.js", "../../../node_modules/color-convert/index.js", "../../../node_modules/color/index.js", "../src/color-utils.ts"], "sourcesContent": ["'use strict'\r\n\r\nmodule.exports = {\r\n\t\"aliceblue\": [240, 248, 255],\r\n\t\"antiquewhite\": [250, 235, 215],\r\n\t\"aqua\": [0, 255, 255],\r\n\t\"aquamarine\": [127, 255, 212],\r\n\t\"azure\": [240, 255, 255],\r\n\t\"beige\": [245, 245, 220],\r\n\t\"bisque\": [255, 228, 196],\r\n\t\"black\": [0, 0, 0],\r\n\t\"blanchedalmond\": [255, 235, 205],\r\n\t\"blue\": [0, 0, 255],\r\n\t\"blueviolet\": [138, 43, 226],\r\n\t\"brown\": [165, 42, 42],\r\n\t\"burlywood\": [222, 184, 135],\r\n\t\"cadetblue\": [95, 158, 160],\r\n\t\"chartreuse\": [127, 255, 0],\r\n\t\"chocolate\": [210, 105, 30],\r\n\t\"coral\": [255, 127, 80],\r\n\t\"cornflowerblue\": [100, 149, 237],\r\n\t\"cornsilk\": [255, 248, 220],\r\n\t\"crimson\": [220, 20, 60],\r\n\t\"cyan\": [0, 255, 255],\r\n\t\"darkblue\": [0, 0, 139],\r\n\t\"darkcyan\": [0, 139, 139],\r\n\t\"darkgoldenrod\": [184, 134, 11],\r\n\t\"darkgray\": [169, 169, 169],\r\n\t\"darkgreen\": [0, 100, 0],\r\n\t\"darkgrey\": [169, 169, 169],\r\n\t\"darkkhaki\": [189, 183, 107],\r\n\t\"darkmagenta\": [139, 0, 139],\r\n\t\"darkolivegreen\": [85, 107, 47],\r\n\t\"darkorange\": [255, 140, 0],\r\n\t\"darkorchid\": [153, 50, 204],\r\n\t\"darkred\": [139, 0, 0],\r\n\t\"darksalmon\": [233, 150, 122],\r\n\t\"darkseagreen\": [143, 188, 143],\r\n\t\"darkslateblue\": [72, 61, 139],\r\n\t\"darkslategray\": [47, 79, 79],\r\n\t\"darkslategrey\": [47, 79, 79],\r\n\t\"darkturquoise\": [0, 206, 209],\r\n\t\"darkviolet\": [148, 0, 211],\r\n\t\"deeppink\": [255, 20, 147],\r\n\t\"deepskyblue\": [0, 191, 255],\r\n\t\"dimgray\": [105, 105, 105],\r\n\t\"dimgrey\": [105, 105, 105],\r\n\t\"dodgerblue\": [30, 144, 255],\r\n\t\"firebrick\": [178, 34, 34],\r\n\t\"floralwhite\": [255, 250, 240],\r\n\t\"forestgreen\": [34, 139, 34],\r\n\t\"fuchsia\": [255, 0, 255],\r\n\t\"gainsboro\": [220, 220, 220],\r\n\t\"ghostwhite\": [248, 248, 255],\r\n\t\"gold\": [255, 215, 0],\r\n\t\"goldenrod\": [218, 165, 32],\r\n\t\"gray\": [128, 128, 128],\r\n\t\"green\": [0, 128, 0],\r\n\t\"greenyellow\": [173, 255, 47],\r\n\t\"grey\": [128, 128, 128],\r\n\t\"honeydew\": [240, 255, 240],\r\n\t\"hotpink\": [255, 105, 180],\r\n\t\"indianred\": [205, 92, 92],\r\n\t\"indigo\": [75, 0, 130],\r\n\t\"ivory\": [255, 255, 240],\r\n\t\"khaki\": [240, 230, 140],\r\n\t\"lavender\": [230, 230, 250],\r\n\t\"lavenderblush\": [255, 240, 245],\r\n\t\"lawngreen\": [124, 252, 0],\r\n\t\"lemonchiffon\": [255, 250, 205],\r\n\t\"lightblue\": [173, 216, 230],\r\n\t\"lightcoral\": [240, 128, 128],\r\n\t\"lightcyan\": [224, 255, 255],\r\n\t\"lightgoldenrodyellow\": [250, 250, 210],\r\n\t\"lightgray\": [211, 211, 211],\r\n\t\"lightgreen\": [144, 238, 144],\r\n\t\"lightgrey\": [211, 211, 211],\r\n\t\"lightpink\": [255, 182, 193],\r\n\t\"lightsalmon\": [255, 160, 122],\r\n\t\"lightseagreen\": [32, 178, 170],\r\n\t\"lightskyblue\": [135, 206, 250],\r\n\t\"lightslategray\": [119, 136, 153],\r\n\t\"lightslategrey\": [119, 136, 153],\r\n\t\"lightsteelblue\": [176, 196, 222],\r\n\t\"lightyellow\": [255, 255, 224],\r\n\t\"lime\": [0, 255, 0],\r\n\t\"limegreen\": [50, 205, 50],\r\n\t\"linen\": [250, 240, 230],\r\n\t\"magenta\": [255, 0, 255],\r\n\t\"maroon\": [128, 0, 0],\r\n\t\"mediumaquamarine\": [102, 205, 170],\r\n\t\"mediumblue\": [0, 0, 205],\r\n\t\"mediumorchid\": [186, 85, 211],\r\n\t\"mediumpurple\": [147, 112, 219],\r\n\t\"mediumseagreen\": [60, 179, 113],\r\n\t\"mediumslateblue\": [123, 104, 238],\r\n\t\"mediumspringgreen\": [0, 250, 154],\r\n\t\"mediumturquoise\": [72, 209, 204],\r\n\t\"mediumvioletred\": [199, 21, 133],\r\n\t\"midnightblue\": [25, 25, 112],\r\n\t\"mintcream\": [245, 255, 250],\r\n\t\"mistyrose\": [255, 228, 225],\r\n\t\"moccasin\": [255, 228, 181],\r\n\t\"navajowhite\": [255, 222, 173],\r\n\t\"navy\": [0, 0, 128],\r\n\t\"oldlace\": [253, 245, 230],\r\n\t\"olive\": [128, 128, 0],\r\n\t\"olivedrab\": [107, 142, 35],\r\n\t\"orange\": [255, 165, 0],\r\n\t\"orangered\": [255, 69, 0],\r\n\t\"orchid\": [218, 112, 214],\r\n\t\"palegoldenrod\": [238, 232, 170],\r\n\t\"palegreen\": [152, 251, 152],\r\n\t\"paleturquoise\": [175, 238, 238],\r\n\t\"palevioletred\": [219, 112, 147],\r\n\t\"papayawhip\": [255, 239, 213],\r\n\t\"peachpuff\": [255, 218, 185],\r\n\t\"peru\": [205, 133, 63],\r\n\t\"pink\": [255, 192, 203],\r\n\t\"plum\": [221, 160, 221],\r\n\t\"powderblue\": [176, 224, 230],\r\n\t\"purple\": [128, 0, 128],\r\n\t\"rebeccapurple\": [102, 51, 153],\r\n\t\"red\": [255, 0, 0],\r\n\t\"rosybrown\": [188, 143, 143],\r\n\t\"royalblue\": [65, 105, 225],\r\n\t\"saddlebrown\": [139, 69, 19],\r\n\t\"salmon\": [250, 128, 114],\r\n\t\"sandybrown\": [244, 164, 96],\r\n\t\"seagreen\": [46, 139, 87],\r\n\t\"seashell\": [255, 245, 238],\r\n\t\"sienna\": [160, 82, 45],\r\n\t\"silver\": [192, 192, 192],\r\n\t\"skyblue\": [135, 206, 235],\r\n\t\"slateblue\": [106, 90, 205],\r\n\t\"slategray\": [112, 128, 144],\r\n\t\"slategrey\": [112, 128, 144],\r\n\t\"snow\": [255, 250, 250],\r\n\t\"springgreen\": [0, 255, 127],\r\n\t\"steelblue\": [70, 130, 180],\r\n\t\"tan\": [210, 180, 140],\r\n\t\"teal\": [0, 128, 128],\r\n\t\"thistle\": [216, 191, 216],\r\n\t\"tomato\": [255, 99, 71],\r\n\t\"turquoise\": [64, 224, 208],\r\n\t\"violet\": [238, 130, 238],\r\n\t\"wheat\": [245, 222, 179],\r\n\t\"white\": [255, 255, 255],\r\n\t\"whitesmoke\": [245, 245, 245],\r\n\t\"yellow\": [255, 255, 0],\r\n\t\"yellowgreen\": [154, 205, 50]\r\n};\r\n", "'use strict';\n\nvar isArrayish = require('is-arrayish');\n\nvar concat = Array.prototype.concat;\nvar slice = Array.prototype.slice;\n\nvar swizzle = module.exports = function swizzle(args) {\n\tvar results = [];\n\n\tfor (var i = 0, len = args.length; i < len; i++) {\n\t\tvar arg = args[i];\n\n\t\tif (isArrayish(arg)) {\n\t\t\t// http://jsperf.com/javascript-array-concat-vs-push/98\n\t\t\tresults = concat.call(results, slice.call(arg));\n\t\t} else {\n\t\t\tresults.push(arg);\n\t\t}\n\t}\n\n\treturn results;\n};\n\nswizzle.wrap = function (fn) {\n\treturn function () {\n\t\treturn fn(swizzle(arguments));\n\t};\n};\n", "module.exports = function isArrayish(obj) {\n\tif (!obj || typeof obj === 'string') {\n\t\treturn false;\n\t}\n\n\treturn obj instanceof Array || Array.isArray(obj) ||\n\t\t(obj.length >= 0 && (obj.splice instanceof Function ||\n\t\t\t(Object.getOwnPropertyDescriptor(obj, (obj.length - 1)) && obj.constructor.name !== 'String')));\n};\n", "/* MIT license */\nvar colorNames = require('color-name');\nvar swizzle = require('simple-swizzle');\nvar hasOwnProperty = Object.hasOwnProperty;\n\nvar reverseNames = Object.create(null);\n\n// create a list of reverse color names\nfor (var name in colorNames) {\n\tif (hasOwnProperty.call(colorNames, name)) {\n\t\treverseNames[colorNames[name]] = name;\n\t}\n}\n\nvar cs = module.exports = {\n\tto: {},\n\tget: {}\n};\n\ncs.get = function (string) {\n\tvar prefix = string.substring(0, 3).toLowerCase();\n\tvar val;\n\tvar model;\n\tswitch (prefix) {\n\t\tcase 'hsl':\n\t\t\tval = cs.get.hsl(string);\n\t\t\tmodel = 'hsl';\n\t\t\tbreak;\n\t\tcase 'hwb':\n\t\t\tval = cs.get.hwb(string);\n\t\t\tmodel = 'hwb';\n\t\t\tbreak;\n\t\tdefault:\n\t\t\tval = cs.get.rgb(string);\n\t\t\tmodel = 'rgb';\n\t\t\tbreak;\n\t}\n\n\tif (!val) {\n\t\treturn null;\n\t}\n\n\treturn {model: model, value: val};\n};\n\ncs.get.rgb = function (string) {\n\tif (!string) {\n\t\treturn null;\n\t}\n\n\tvar abbr = /^#([a-f0-9]{3,4})$/i;\n\tvar hex = /^#([a-f0-9]{6})([a-f0-9]{2})?$/i;\n\tvar rgba = /^rgba?\\(\\s*([+-]?\\d+)(?=[\\s,])\\s*(?:,\\s*)?([+-]?\\d+)(?=[\\s,])\\s*(?:,\\s*)?([+-]?\\d+)\\s*(?:[,|\\/]\\s*([+-]?[\\d\\.]+)(%?)\\s*)?\\)$/;\n\tvar per = /^rgba?\\(\\s*([+-]?[\\d\\.]+)\\%\\s*,?\\s*([+-]?[\\d\\.]+)\\%\\s*,?\\s*([+-]?[\\d\\.]+)\\%\\s*(?:[,|\\/]\\s*([+-]?[\\d\\.]+)(%?)\\s*)?\\)$/;\n\tvar keyword = /^(\\w+)$/;\n\n\tvar rgb = [0, 0, 0, 1];\n\tvar match;\n\tvar i;\n\tvar hexAlpha;\n\n\tif (match = string.match(hex)) {\n\t\thexAlpha = match[2];\n\t\tmatch = match[1];\n\n\t\tfor (i = 0; i < 3; i++) {\n\t\t\t// https://jsperf.com/slice-vs-substr-vs-substring-methods-long-string/19\n\t\t\tvar i2 = i * 2;\n\t\t\trgb[i] = parseInt(match.slice(i2, i2 + 2), 16);\n\t\t}\n\n\t\tif (hexAlpha) {\n\t\t\trgb[3] = parseInt(hexAlpha, 16) / 255;\n\t\t}\n\t} else if (match = string.match(abbr)) {\n\t\tmatch = match[1];\n\t\thexAlpha = match[3];\n\n\t\tfor (i = 0; i < 3; i++) {\n\t\t\trgb[i] = parseInt(match[i] + match[i], 16);\n\t\t}\n\n\t\tif (hexAlpha) {\n\t\t\trgb[3] = parseInt(hexAlpha + hexAlpha, 16) / 255;\n\t\t}\n\t} else if (match = string.match(rgba)) {\n\t\tfor (i = 0; i < 3; i++) {\n\t\t\trgb[i] = parseInt(match[i + 1], 0);\n\t\t}\n\n\t\tif (match[4]) {\n\t\t\tif (match[5]) {\n\t\t\t\trgb[3] = parseFloat(match[4]) * 0.01;\n\t\t\t} else {\n\t\t\t\trgb[3] = parseFloat(match[4]);\n\t\t\t}\n\t\t}\n\t} else if (match = string.match(per)) {\n\t\tfor (i = 0; i < 3; i++) {\n\t\t\trgb[i] = Math.round(parseFloat(match[i + 1]) * 2.55);\n\t\t}\n\n\t\tif (match[4]) {\n\t\t\tif (match[5]) {\n\t\t\t\trgb[3] = parseFloat(match[4]) * 0.01;\n\t\t\t} else {\n\t\t\t\trgb[3] = parseFloat(match[4]);\n\t\t\t}\n\t\t}\n\t} else if (match = string.match(keyword)) {\n\t\tif (match[1] === 'transparent') {\n\t\t\treturn [0, 0, 0, 0];\n\t\t}\n\n\t\tif (!hasOwnProperty.call(colorNames, match[1])) {\n\t\t\treturn null;\n\t\t}\n\n\t\trgb = colorNames[match[1]];\n\t\trgb[3] = 1;\n\n\t\treturn rgb;\n\t} else {\n\t\treturn null;\n\t}\n\n\tfor (i = 0; i < 3; i++) {\n\t\trgb[i] = clamp(rgb[i], 0, 255);\n\t}\n\trgb[3] = clamp(rgb[3], 0, 1);\n\n\treturn rgb;\n};\n\ncs.get.hsl = function (string) {\n\tif (!string) {\n\t\treturn null;\n\t}\n\n\tvar hsl = /^hsla?\\(\\s*([+-]?(?:\\d{0,3}\\.)?\\d+)(?:deg)?\\s*,?\\s*([+-]?[\\d\\.]+)%\\s*,?\\s*([+-]?[\\d\\.]+)%\\s*(?:[,|\\/]\\s*([+-]?(?=\\.\\d|\\d)(?:0|[1-9]\\d*)?(?:\\.\\d*)?(?:[eE][+-]?\\d+)?)\\s*)?\\)$/;\n\tvar match = string.match(hsl);\n\n\tif (match) {\n\t\tvar alpha = parseFloat(match[4]);\n\t\tvar h = ((parseFloat(match[1]) % 360) + 360) % 360;\n\t\tvar s = clamp(parseFloat(match[2]), 0, 100);\n\t\tvar l = clamp(parseFloat(match[3]), 0, 100);\n\t\tvar a = clamp(isNaN(alpha) ? 1 : alpha, 0, 1);\n\n\t\treturn [h, s, l, a];\n\t}\n\n\treturn null;\n};\n\ncs.get.hwb = function (string) {\n\tif (!string) {\n\t\treturn null;\n\t}\n\n\tvar hwb = /^hwb\\(\\s*([+-]?\\d{0,3}(?:\\.\\d+)?)(?:deg)?\\s*,\\s*([+-]?[\\d\\.]+)%\\s*,\\s*([+-]?[\\d\\.]+)%\\s*(?:,\\s*([+-]?(?=\\.\\d|\\d)(?:0|[1-9]\\d*)?(?:\\.\\d*)?(?:[eE][+-]?\\d+)?)\\s*)?\\)$/;\n\tvar match = string.match(hwb);\n\n\tif (match) {\n\t\tvar alpha = parseFloat(match[4]);\n\t\tvar h = ((parseFloat(match[1]) % 360) + 360) % 360;\n\t\tvar w = clamp(parseFloat(match[2]), 0, 100);\n\t\tvar b = clamp(parseFloat(match[3]), 0, 100);\n\t\tvar a = clamp(isNaN(alpha) ? 1 : alpha, 0, 1);\n\t\treturn [h, w, b, a];\n\t}\n\n\treturn null;\n};\n\ncs.to.hex = function () {\n\tvar rgba = swizzle(arguments);\n\n\treturn (\n\t\t'#' +\n\t\thexDouble(rgba[0]) +\n\t\thexDouble(rgba[1]) +\n\t\thexDouble(rgba[2]) +\n\t\t(rgba[3] < 1\n\t\t\t? (hexDouble(Math.round(rgba[3] * 255)))\n\t\t\t: '')\n\t);\n};\n\ncs.to.rgb = function () {\n\tvar rgba = swizzle(arguments);\n\n\treturn rgba.length < 4 || rgba[3] === 1\n\t\t? 'rgb(' + Math.round(rgba[0]) + ', ' + Math.round(rgba[1]) + ', ' + Math.round(rgba[2]) + ')'\n\t\t: 'rgba(' + Math.round(rgba[0]) + ', ' + Math.round(rgba[1]) + ', ' + Math.round(rgba[2]) + ', ' + rgba[3] + ')';\n};\n\ncs.to.rgb.percent = function () {\n\tvar rgba = swizzle(arguments);\n\n\tvar r = Math.round(rgba[0] / 255 * 100);\n\tvar g = Math.round(rgba[1] / 255 * 100);\n\tvar b = Math.round(rgba[2] / 255 * 100);\n\n\treturn rgba.length < 4 || rgba[3] === 1\n\t\t? 'rgb(' + r + '%, ' + g + '%, ' + b + '%)'\n\t\t: 'rgba(' + r + '%, ' + g + '%, ' + b + '%, ' + rgba[3] + ')';\n};\n\ncs.to.hsl = function () {\n\tvar hsla = swizzle(arguments);\n\treturn hsla.length < 4 || hsla[3] === 1\n\t\t? 'hsl(' + hsla[0] + ', ' + hsla[1] + '%, ' + hsla[2] + '%)'\n\t\t: 'hsla(' + hsla[0] + ', ' + hsla[1] + '%, ' + hsla[2] + '%, ' + hsla[3] + ')';\n};\n\n// hwb is a bit different than rgb(a) & hsl(a) since there is no alpha specific syntax\n// (hwb have alpha optional & 1 is default value)\ncs.to.hwb = function () {\n\tvar hwba = swizzle(arguments);\n\n\tvar a = '';\n\tif (hwba.length >= 4 && hwba[3] !== 1) {\n\t\ta = ', ' + hwba[3];\n\t}\n\n\treturn 'hwb(' + hwba[0] + ', ' + hwba[1] + '%, ' + hwba[2] + '%' + a + ')';\n};\n\ncs.to.keyword = function (rgb) {\n\treturn reverseNames[rgb.slice(0, 3)];\n};\n\n// helpers\nfunction clamp(num, min, max) {\n\treturn Math.min(Math.max(min, num), max);\n}\n\nfunction hexDouble(num) {\n\tvar str = Math.round(num).toString(16).toUpperCase();\n\treturn (str.length < 2) ? '0' + str : str;\n}\n", "'use strict'\r\n\r\nmodule.exports = {\r\n\t\"aliceblue\": [240, 248, 255],\r\n\t\"antiquewhite\": [250, 235, 215],\r\n\t\"aqua\": [0, 255, 255],\r\n\t\"aquamarine\": [127, 255, 212],\r\n\t\"azure\": [240, 255, 255],\r\n\t\"beige\": [245, 245, 220],\r\n\t\"bisque\": [255, 228, 196],\r\n\t\"black\": [0, 0, 0],\r\n\t\"blanchedalmond\": [255, 235, 205],\r\n\t\"blue\": [0, 0, 255],\r\n\t\"blueviolet\": [138, 43, 226],\r\n\t\"brown\": [165, 42, 42],\r\n\t\"burlywood\": [222, 184, 135],\r\n\t\"cadetblue\": [95, 158, 160],\r\n\t\"chartreuse\": [127, 255, 0],\r\n\t\"chocolate\": [210, 105, 30],\r\n\t\"coral\": [255, 127, 80],\r\n\t\"cornflowerblue\": [100, 149, 237],\r\n\t\"cornsilk\": [255, 248, 220],\r\n\t\"crimson\": [220, 20, 60],\r\n\t\"cyan\": [0, 255, 255],\r\n\t\"darkblue\": [0, 0, 139],\r\n\t\"darkcyan\": [0, 139, 139],\r\n\t\"darkgoldenrod\": [184, 134, 11],\r\n\t\"darkgray\": [169, 169, 169],\r\n\t\"darkgreen\": [0, 100, 0],\r\n\t\"darkgrey\": [169, 169, 169],\r\n\t\"darkkhaki\": [189, 183, 107],\r\n\t\"darkmagenta\": [139, 0, 139],\r\n\t\"darkolivegreen\": [85, 107, 47],\r\n\t\"darkorange\": [255, 140, 0],\r\n\t\"darkorchid\": [153, 50, 204],\r\n\t\"darkred\": [139, 0, 0],\r\n\t\"darksalmon\": [233, 150, 122],\r\n\t\"darkseagreen\": [143, 188, 143],\r\n\t\"darkslateblue\": [72, 61, 139],\r\n\t\"darkslategray\": [47, 79, 79],\r\n\t\"darkslategrey\": [47, 79, 79],\r\n\t\"darkturquoise\": [0, 206, 209],\r\n\t\"darkviolet\": [148, 0, 211],\r\n\t\"deeppink\": [255, 20, 147],\r\n\t\"deepskyblue\": [0, 191, 255],\r\n\t\"dimgray\": [105, 105, 105],\r\n\t\"dimgrey\": [105, 105, 105],\r\n\t\"dodgerblue\": [30, 144, 255],\r\n\t\"firebrick\": [178, 34, 34],\r\n\t\"floralwhite\": [255, 250, 240],\r\n\t\"forestgreen\": [34, 139, 34],\r\n\t\"fuchsia\": [255, 0, 255],\r\n\t\"gainsboro\": [220, 220, 220],\r\n\t\"ghostwhite\": [248, 248, 255],\r\n\t\"gold\": [255, 215, 0],\r\n\t\"goldenrod\": [218, 165, 32],\r\n\t\"gray\": [128, 128, 128],\r\n\t\"green\": [0, 128, 0],\r\n\t\"greenyellow\": [173, 255, 47],\r\n\t\"grey\": [128, 128, 128],\r\n\t\"honeydew\": [240, 255, 240],\r\n\t\"hotpink\": [255, 105, 180],\r\n\t\"indianred\": [205, 92, 92],\r\n\t\"indigo\": [75, 0, 130],\r\n\t\"ivory\": [255, 255, 240],\r\n\t\"khaki\": [240, 230, 140],\r\n\t\"lavender\": [230, 230, 250],\r\n\t\"lavenderblush\": [255, 240, 245],\r\n\t\"lawngreen\": [124, 252, 0],\r\n\t\"lemonchiffon\": [255, 250, 205],\r\n\t\"lightblue\": [173, 216, 230],\r\n\t\"lightcoral\": [240, 128, 128],\r\n\t\"lightcyan\": [224, 255, 255],\r\n\t\"lightgoldenrodyellow\": [250, 250, 210],\r\n\t\"lightgray\": [211, 211, 211],\r\n\t\"lightgreen\": [144, 238, 144],\r\n\t\"lightgrey\": [211, 211, 211],\r\n\t\"lightpink\": [255, 182, 193],\r\n\t\"lightsalmon\": [255, 160, 122],\r\n\t\"lightseagreen\": [32, 178, 170],\r\n\t\"lightskyblue\": [135, 206, 250],\r\n\t\"lightslategray\": [119, 136, 153],\r\n\t\"lightslategrey\": [119, 136, 153],\r\n\t\"lightsteelblue\": [176, 196, 222],\r\n\t\"lightyellow\": [255, 255, 224],\r\n\t\"lime\": [0, 255, 0],\r\n\t\"limegreen\": [50, 205, 50],\r\n\t\"linen\": [250, 240, 230],\r\n\t\"magenta\": [255, 0, 255],\r\n\t\"maroon\": [128, 0, 0],\r\n\t\"mediumaquamarine\": [102, 205, 170],\r\n\t\"mediumblue\": [0, 0, 205],\r\n\t\"mediumorchid\": [186, 85, 211],\r\n\t\"mediumpurple\": [147, 112, 219],\r\n\t\"mediumseagreen\": [60, 179, 113],\r\n\t\"mediumslateblue\": [123, 104, 238],\r\n\t\"mediumspringgreen\": [0, 250, 154],\r\n\t\"mediumturquoise\": [72, 209, 204],\r\n\t\"mediumvioletred\": [199, 21, 133],\r\n\t\"midnightblue\": [25, 25, 112],\r\n\t\"mintcream\": [245, 255, 250],\r\n\t\"mistyrose\": [255, 228, 225],\r\n\t\"moccasin\": [255, 228, 181],\r\n\t\"navajowhite\": [255, 222, 173],\r\n\t\"navy\": [0, 0, 128],\r\n\t\"oldlace\": [253, 245, 230],\r\n\t\"olive\": [128, 128, 0],\r\n\t\"olivedrab\": [107, 142, 35],\r\n\t\"orange\": [255, 165, 0],\r\n\t\"orangered\": [255, 69, 0],\r\n\t\"orchid\": [218, 112, 214],\r\n\t\"palegoldenrod\": [238, 232, 170],\r\n\t\"palegreen\": [152, 251, 152],\r\n\t\"paleturquoise\": [175, 238, 238],\r\n\t\"palevioletred\": [219, 112, 147],\r\n\t\"papayawhip\": [255, 239, 213],\r\n\t\"peachpuff\": [255, 218, 185],\r\n\t\"peru\": [205, 133, 63],\r\n\t\"pink\": [255, 192, 203],\r\n\t\"plum\": [221, 160, 221],\r\n\t\"powderblue\": [176, 224, 230],\r\n\t\"purple\": [128, 0, 128],\r\n\t\"rebeccapurple\": [102, 51, 153],\r\n\t\"red\": [255, 0, 0],\r\n\t\"rosybrown\": [188, 143, 143],\r\n\t\"royalblue\": [65, 105, 225],\r\n\t\"saddlebrown\": [139, 69, 19],\r\n\t\"salmon\": [250, 128, 114],\r\n\t\"sandybrown\": [244, 164, 96],\r\n\t\"seagreen\": [46, 139, 87],\r\n\t\"seashell\": [255, 245, 238],\r\n\t\"sienna\": [160, 82, 45],\r\n\t\"silver\": [192, 192, 192],\r\n\t\"skyblue\": [135, 206, 235],\r\n\t\"slateblue\": [106, 90, 205],\r\n\t\"slategray\": [112, 128, 144],\r\n\t\"slategrey\": [112, 128, 144],\r\n\t\"snow\": [255, 250, 250],\r\n\t\"springgreen\": [0, 255, 127],\r\n\t\"steelblue\": [70, 130, 180],\r\n\t\"tan\": [210, 180, 140],\r\n\t\"teal\": [0, 128, 128],\r\n\t\"thistle\": [216, 191, 216],\r\n\t\"tomato\": [255, 99, 71],\r\n\t\"turquoise\": [64, 224, 208],\r\n\t\"violet\": [238, 130, 238],\r\n\t\"wheat\": [245, 222, 179],\r\n\t\"white\": [255, 255, 255],\r\n\t\"whitesmoke\": [245, 245, 245],\r\n\t\"yellow\": [255, 255, 0],\r\n\t\"yellowgreen\": [154, 205, 50]\r\n};\r\n", "/* MIT license */\nvar cssKeywords = require('color-name');\n\n// NOTE: conversions should only return primitive values (i.e. arrays, or\n//       values that give correct `typeof` results).\n//       do not use box values types (i.e. Number(), String(), etc.)\n\nvar reverseKeywords = {};\nfor (var key in cssKeywords) {\n\tif (cssKeywords.hasOwnProperty(key)) {\n\t\treverseKeywords[cssKeywords[key]] = key;\n\t}\n}\n\nvar convert = module.exports = {\n\trgb: {channels: 3, labels: 'rgb'},\n\thsl: {channels: 3, labels: 'hsl'},\n\thsv: {channels: 3, labels: 'hsv'},\n\thwb: {channels: 3, labels: 'hwb'},\n\tcmyk: {channels: 4, labels: 'cmyk'},\n\txyz: {channels: 3, labels: 'xyz'},\n\tlab: {channels: 3, labels: 'lab'},\n\tlch: {channels: 3, labels: 'lch'},\n\thex: {channels: 1, labels: ['hex']},\n\tkeyword: {channels: 1, labels: ['keyword']},\n\tansi16: {channels: 1, labels: ['ansi16']},\n\tansi256: {channels: 1, labels: ['ansi256']},\n\thcg: {channels: 3, labels: ['h', 'c', 'g']},\n\tapple: {channels: 3, labels: ['r16', 'g16', 'b16']},\n\tgray: {channels: 1, labels: ['gray']}\n};\n\n// hide .channels and .labels properties\nfor (var model in convert) {\n\tif (convert.hasOwnProperty(model)) {\n\t\tif (!('channels' in convert[model])) {\n\t\t\tthrow new Error('missing channels property: ' + model);\n\t\t}\n\n\t\tif (!('labels' in convert[model])) {\n\t\t\tthrow new Error('missing channel labels property: ' + model);\n\t\t}\n\n\t\tif (convert[model].labels.length !== convert[model].channels) {\n\t\t\tthrow new Error('channel and label counts mismatch: ' + model);\n\t\t}\n\n\t\tvar channels = convert[model].channels;\n\t\tvar labels = convert[model].labels;\n\t\tdelete convert[model].channels;\n\t\tdelete convert[model].labels;\n\t\tObject.defineProperty(convert[model], 'channels', {value: channels});\n\t\tObject.defineProperty(convert[model], 'labels', {value: labels});\n\t}\n}\n\nconvert.rgb.hsl = function (rgb) {\n\tvar r = rgb[0] / 255;\n\tvar g = rgb[1] / 255;\n\tvar b = rgb[2] / 255;\n\tvar min = Math.min(r, g, b);\n\tvar max = Math.max(r, g, b);\n\tvar delta = max - min;\n\tvar h;\n\tvar s;\n\tvar l;\n\n\tif (max === min) {\n\t\th = 0;\n\t} else if (r === max) {\n\t\th = (g - b) / delta;\n\t} else if (g === max) {\n\t\th = 2 + (b - r) / delta;\n\t} else if (b === max) {\n\t\th = 4 + (r - g) / delta;\n\t}\n\n\th = Math.min(h * 60, 360);\n\n\tif (h < 0) {\n\t\th += 360;\n\t}\n\n\tl = (min + max) / 2;\n\n\tif (max === min) {\n\t\ts = 0;\n\t} else if (l <= 0.5) {\n\t\ts = delta / (max + min);\n\t} else {\n\t\ts = delta / (2 - max - min);\n\t}\n\n\treturn [h, s * 100, l * 100];\n};\n\nconvert.rgb.hsv = function (rgb) {\n\tvar rdif;\n\tvar gdif;\n\tvar bdif;\n\tvar h;\n\tvar s;\n\n\tvar r = rgb[0] / 255;\n\tvar g = rgb[1] / 255;\n\tvar b = rgb[2] / 255;\n\tvar v = Math.max(r, g, b);\n\tvar diff = v - Math.min(r, g, b);\n\tvar diffc = function (c) {\n\t\treturn (v - c) / 6 / diff + 1 / 2;\n\t};\n\n\tif (diff === 0) {\n\t\th = s = 0;\n\t} else {\n\t\ts = diff / v;\n\t\trdif = diffc(r);\n\t\tgdif = diffc(g);\n\t\tbdif = diffc(b);\n\n\t\tif (r === v) {\n\t\t\th = bdif - gdif;\n\t\t} else if (g === v) {\n\t\t\th = (1 / 3) + rdif - bdif;\n\t\t} else if (b === v) {\n\t\t\th = (2 / 3) + gdif - rdif;\n\t\t}\n\t\tif (h < 0) {\n\t\t\th += 1;\n\t\t} else if (h > 1) {\n\t\t\th -= 1;\n\t\t}\n\t}\n\n\treturn [\n\t\th * 360,\n\t\ts * 100,\n\t\tv * 100\n\t];\n};\n\nconvert.rgb.hwb = function (rgb) {\n\tvar r = rgb[0];\n\tvar g = rgb[1];\n\tvar b = rgb[2];\n\tvar h = convert.rgb.hsl(rgb)[0];\n\tvar w = 1 / 255 * Math.min(r, Math.min(g, b));\n\n\tb = 1 - 1 / 255 * Math.max(r, Math.max(g, b));\n\n\treturn [h, w * 100, b * 100];\n};\n\nconvert.rgb.cmyk = function (rgb) {\n\tvar r = rgb[0] / 255;\n\tvar g = rgb[1] / 255;\n\tvar b = rgb[2] / 255;\n\tvar c;\n\tvar m;\n\tvar y;\n\tvar k;\n\n\tk = Math.min(1 - r, 1 - g, 1 - b);\n\tc = (1 - r - k) / (1 - k) || 0;\n\tm = (1 - g - k) / (1 - k) || 0;\n\ty = (1 - b - k) / (1 - k) || 0;\n\n\treturn [c * 100, m * 100, y * 100, k * 100];\n};\n\n/**\n * See https://en.m.wikipedia.org/wiki/Euclidean_distance#Squared_Euclidean_distance\n * */\nfunction comparativeDistance(x, y) {\n\treturn (\n\t\tMath.pow(x[0] - y[0], 2) +\n\t\tMath.pow(x[1] - y[1], 2) +\n\t\tMath.pow(x[2] - y[2], 2)\n\t);\n}\n\nconvert.rgb.keyword = function (rgb) {\n\tvar reversed = reverseKeywords[rgb];\n\tif (reversed) {\n\t\treturn reversed;\n\t}\n\n\tvar currentClosestDistance = Infinity;\n\tvar currentClosestKeyword;\n\n\tfor (var keyword in cssKeywords) {\n\t\tif (cssKeywords.hasOwnProperty(keyword)) {\n\t\t\tvar value = cssKeywords[keyword];\n\n\t\t\t// Compute comparative distance\n\t\t\tvar distance = comparativeDistance(rgb, value);\n\n\t\t\t// Check if its less, if so set as closest\n\t\t\tif (distance < currentClosestDistance) {\n\t\t\t\tcurrentClosestDistance = distance;\n\t\t\t\tcurrentClosestKeyword = keyword;\n\t\t\t}\n\t\t}\n\t}\n\n\treturn currentClosestKeyword;\n};\n\nconvert.keyword.rgb = function (keyword) {\n\treturn cssKeywords[keyword];\n};\n\nconvert.rgb.xyz = function (rgb) {\n\tvar r = rgb[0] / 255;\n\tvar g = rgb[1] / 255;\n\tvar b = rgb[2] / 255;\n\n\t// assume sRGB\n\tr = r > 0.04045 ? Math.pow(((r + 0.055) / 1.055), 2.4) : (r / 12.92);\n\tg = g > 0.04045 ? Math.pow(((g + 0.055) / 1.055), 2.4) : (g / 12.92);\n\tb = b > 0.04045 ? Math.pow(((b + 0.055) / 1.055), 2.4) : (b / 12.92);\n\n\tvar x = (r * 0.4124) + (g * 0.3576) + (b * 0.1805);\n\tvar y = (r * 0.2126) + (g * 0.7152) + (b * 0.0722);\n\tvar z = (r * 0.0193) + (g * 0.1192) + (b * 0.9505);\n\n\treturn [x * 100, y * 100, z * 100];\n};\n\nconvert.rgb.lab = function (rgb) {\n\tvar xyz = convert.rgb.xyz(rgb);\n\tvar x = xyz[0];\n\tvar y = xyz[1];\n\tvar z = xyz[2];\n\tvar l;\n\tvar a;\n\tvar b;\n\n\tx /= 95.047;\n\ty /= 100;\n\tz /= 108.883;\n\n\tx = x > 0.008856 ? Math.pow(x, 1 / 3) : (7.787 * x) + (16 / 116);\n\ty = y > 0.008856 ? Math.pow(y, 1 / 3) : (7.787 * y) + (16 / 116);\n\tz = z > 0.008856 ? Math.pow(z, 1 / 3) : (7.787 * z) + (16 / 116);\n\n\tl = (116 * y) - 16;\n\ta = 500 * (x - y);\n\tb = 200 * (y - z);\n\n\treturn [l, a, b];\n};\n\nconvert.hsl.rgb = function (hsl) {\n\tvar h = hsl[0] / 360;\n\tvar s = hsl[1] / 100;\n\tvar l = hsl[2] / 100;\n\tvar t1;\n\tvar t2;\n\tvar t3;\n\tvar rgb;\n\tvar val;\n\n\tif (s === 0) {\n\t\tval = l * 255;\n\t\treturn [val, val, val];\n\t}\n\n\tif (l < 0.5) {\n\t\tt2 = l * (1 + s);\n\t} else {\n\t\tt2 = l + s - l * s;\n\t}\n\n\tt1 = 2 * l - t2;\n\n\trgb = [0, 0, 0];\n\tfor (var i = 0; i < 3; i++) {\n\t\tt3 = h + 1 / 3 * -(i - 1);\n\t\tif (t3 < 0) {\n\t\t\tt3++;\n\t\t}\n\t\tif (t3 > 1) {\n\t\t\tt3--;\n\t\t}\n\n\t\tif (6 * t3 < 1) {\n\t\t\tval = t1 + (t2 - t1) * 6 * t3;\n\t\t} else if (2 * t3 < 1) {\n\t\t\tval = t2;\n\t\t} else if (3 * t3 < 2) {\n\t\t\tval = t1 + (t2 - t1) * (2 / 3 - t3) * 6;\n\t\t} else {\n\t\t\tval = t1;\n\t\t}\n\n\t\trgb[i] = val * 255;\n\t}\n\n\treturn rgb;\n};\n\nconvert.hsl.hsv = function (hsl) {\n\tvar h = hsl[0];\n\tvar s = hsl[1] / 100;\n\tvar l = hsl[2] / 100;\n\tvar smin = s;\n\tvar lmin = Math.max(l, 0.01);\n\tvar sv;\n\tvar v;\n\n\tl *= 2;\n\ts *= (l <= 1) ? l : 2 - l;\n\tsmin *= lmin <= 1 ? lmin : 2 - lmin;\n\tv = (l + s) / 2;\n\tsv = l === 0 ? (2 * smin) / (lmin + smin) : (2 * s) / (l + s);\n\n\treturn [h, sv * 100, v * 100];\n};\n\nconvert.hsv.rgb = function (hsv) {\n\tvar h = hsv[0] / 60;\n\tvar s = hsv[1] / 100;\n\tvar v = hsv[2] / 100;\n\tvar hi = Math.floor(h) % 6;\n\n\tvar f = h - Math.floor(h);\n\tvar p = 255 * v * (1 - s);\n\tvar q = 255 * v * (1 - (s * f));\n\tvar t = 255 * v * (1 - (s * (1 - f)));\n\tv *= 255;\n\n\tswitch (hi) {\n\t\tcase 0:\n\t\t\treturn [v, t, p];\n\t\tcase 1:\n\t\t\treturn [q, v, p];\n\t\tcase 2:\n\t\t\treturn [p, v, t];\n\t\tcase 3:\n\t\t\treturn [p, q, v];\n\t\tcase 4:\n\t\t\treturn [t, p, v];\n\t\tcase 5:\n\t\t\treturn [v, p, q];\n\t}\n};\n\nconvert.hsv.hsl = function (hsv) {\n\tvar h = hsv[0];\n\tvar s = hsv[1] / 100;\n\tvar v = hsv[2] / 100;\n\tvar vmin = Math.max(v, 0.01);\n\tvar lmin;\n\tvar sl;\n\tvar l;\n\n\tl = (2 - s) * v;\n\tlmin = (2 - s) * vmin;\n\tsl = s * vmin;\n\tsl /= (lmin <= 1) ? lmin : 2 - lmin;\n\tsl = sl || 0;\n\tl /= 2;\n\n\treturn [h, sl * 100, l * 100];\n};\n\n// http://dev.w3.org/csswg/css-color/#hwb-to-rgb\nconvert.hwb.rgb = function (hwb) {\n\tvar h = hwb[0] / 360;\n\tvar wh = hwb[1] / 100;\n\tvar bl = hwb[2] / 100;\n\tvar ratio = wh + bl;\n\tvar i;\n\tvar v;\n\tvar f;\n\tvar n;\n\n\t// wh + bl cant be > 1\n\tif (ratio > 1) {\n\t\twh /= ratio;\n\t\tbl /= ratio;\n\t}\n\n\ti = Math.floor(6 * h);\n\tv = 1 - bl;\n\tf = 6 * h - i;\n\n\tif ((i & 0x01) !== 0) {\n\t\tf = 1 - f;\n\t}\n\n\tn = wh + f * (v - wh); // linear interpolation\n\n\tvar r;\n\tvar g;\n\tvar b;\n\tswitch (i) {\n\t\tdefault:\n\t\tcase 6:\n\t\tcase 0: r = v; g = n; b = wh; break;\n\t\tcase 1: r = n; g = v; b = wh; break;\n\t\tcase 2: r = wh; g = v; b = n; break;\n\t\tcase 3: r = wh; g = n; b = v; break;\n\t\tcase 4: r = n; g = wh; b = v; break;\n\t\tcase 5: r = v; g = wh; b = n; break;\n\t}\n\n\treturn [r * 255, g * 255, b * 255];\n};\n\nconvert.cmyk.rgb = function (cmyk) {\n\tvar c = cmyk[0] / 100;\n\tvar m = cmyk[1] / 100;\n\tvar y = cmyk[2] / 100;\n\tvar k = cmyk[3] / 100;\n\tvar r;\n\tvar g;\n\tvar b;\n\n\tr = 1 - Math.min(1, c * (1 - k) + k);\n\tg = 1 - Math.min(1, m * (1 - k) + k);\n\tb = 1 - Math.min(1, y * (1 - k) + k);\n\n\treturn [r * 255, g * 255, b * 255];\n};\n\nconvert.xyz.rgb = function (xyz) {\n\tvar x = xyz[0] / 100;\n\tvar y = xyz[1] / 100;\n\tvar z = xyz[2] / 100;\n\tvar r;\n\tvar g;\n\tvar b;\n\n\tr = (x * 3.2406) + (y * -1.5372) + (z * -0.4986);\n\tg = (x * -0.9689) + (y * 1.8758) + (z * 0.0415);\n\tb = (x * 0.0557) + (y * -0.2040) + (z * 1.0570);\n\n\t// assume sRGB\n\tr = r > 0.0031308\n\t\t? ((1.055 * Math.pow(r, 1.0 / 2.4)) - 0.055)\n\t\t: r * 12.92;\n\n\tg = g > 0.0031308\n\t\t? ((1.055 * Math.pow(g, 1.0 / 2.4)) - 0.055)\n\t\t: g * 12.92;\n\n\tb = b > 0.0031308\n\t\t? ((1.055 * Math.pow(b, 1.0 / 2.4)) - 0.055)\n\t\t: b * 12.92;\n\n\tr = Math.min(Math.max(0, r), 1);\n\tg = Math.min(Math.max(0, g), 1);\n\tb = Math.min(Math.max(0, b), 1);\n\n\treturn [r * 255, g * 255, b * 255];\n};\n\nconvert.xyz.lab = function (xyz) {\n\tvar x = xyz[0];\n\tvar y = xyz[1];\n\tvar z = xyz[2];\n\tvar l;\n\tvar a;\n\tvar b;\n\n\tx /= 95.047;\n\ty /= 100;\n\tz /= 108.883;\n\n\tx = x > 0.008856 ? Math.pow(x, 1 / 3) : (7.787 * x) + (16 / 116);\n\ty = y > 0.008856 ? Math.pow(y, 1 / 3) : (7.787 * y) + (16 / 116);\n\tz = z > 0.008856 ? Math.pow(z, 1 / 3) : (7.787 * z) + (16 / 116);\n\n\tl = (116 * y) - 16;\n\ta = 500 * (x - y);\n\tb = 200 * (y - z);\n\n\treturn [l, a, b];\n};\n\nconvert.lab.xyz = function (lab) {\n\tvar l = lab[0];\n\tvar a = lab[1];\n\tvar b = lab[2];\n\tvar x;\n\tvar y;\n\tvar z;\n\n\ty = (l + 16) / 116;\n\tx = a / 500 + y;\n\tz = y - b / 200;\n\n\tvar y2 = Math.pow(y, 3);\n\tvar x2 = Math.pow(x, 3);\n\tvar z2 = Math.pow(z, 3);\n\ty = y2 > 0.008856 ? y2 : (y - 16 / 116) / 7.787;\n\tx = x2 > 0.008856 ? x2 : (x - 16 / 116) / 7.787;\n\tz = z2 > 0.008856 ? z2 : (z - 16 / 116) / 7.787;\n\n\tx *= 95.047;\n\ty *= 100;\n\tz *= 108.883;\n\n\treturn [x, y, z];\n};\n\nconvert.lab.lch = function (lab) {\n\tvar l = lab[0];\n\tvar a = lab[1];\n\tvar b = lab[2];\n\tvar hr;\n\tvar h;\n\tvar c;\n\n\thr = Math.atan2(b, a);\n\th = hr * 360 / 2 / Math.PI;\n\n\tif (h < 0) {\n\t\th += 360;\n\t}\n\n\tc = Math.sqrt(a * a + b * b);\n\n\treturn [l, c, h];\n};\n\nconvert.lch.lab = function (lch) {\n\tvar l = lch[0];\n\tvar c = lch[1];\n\tvar h = lch[2];\n\tvar a;\n\tvar b;\n\tvar hr;\n\n\thr = h / 360 * 2 * Math.PI;\n\ta = c * Math.cos(hr);\n\tb = c * Math.sin(hr);\n\n\treturn [l, a, b];\n};\n\nconvert.rgb.ansi16 = function (args) {\n\tvar r = args[0];\n\tvar g = args[1];\n\tvar b = args[2];\n\tvar value = 1 in arguments ? arguments[1] : convert.rgb.hsv(args)[2]; // hsv -> ansi16 optimization\n\n\tvalue = Math.round(value / 50);\n\n\tif (value === 0) {\n\t\treturn 30;\n\t}\n\n\tvar ansi = 30\n\t\t+ ((Math.round(b / 255) << 2)\n\t\t| (Math.round(g / 255) << 1)\n\t\t| Math.round(r / 255));\n\n\tif (value === 2) {\n\t\tansi += 60;\n\t}\n\n\treturn ansi;\n};\n\nconvert.hsv.ansi16 = function (args) {\n\t// optimization here; we already know the value and don't need to get\n\t// it converted for us.\n\treturn convert.rgb.ansi16(convert.hsv.rgb(args), args[2]);\n};\n\nconvert.rgb.ansi256 = function (args) {\n\tvar r = args[0];\n\tvar g = args[1];\n\tvar b = args[2];\n\n\t// we use the extended greyscale palette here, with the exception of\n\t// black and white. normal palette only has 4 greyscale shades.\n\tif (r === g && g === b) {\n\t\tif (r < 8) {\n\t\t\treturn 16;\n\t\t}\n\n\t\tif (r > 248) {\n\t\t\treturn 231;\n\t\t}\n\n\t\treturn Math.round(((r - 8) / 247) * 24) + 232;\n\t}\n\n\tvar ansi = 16\n\t\t+ (36 * Math.round(r / 255 * 5))\n\t\t+ (6 * Math.round(g / 255 * 5))\n\t\t+ Math.round(b / 255 * 5);\n\n\treturn ansi;\n};\n\nconvert.ansi16.rgb = function (args) {\n\tvar color = args % 10;\n\n\t// handle greyscale\n\tif (color === 0 || color === 7) {\n\t\tif (args > 50) {\n\t\t\tcolor += 3.5;\n\t\t}\n\n\t\tcolor = color / 10.5 * 255;\n\n\t\treturn [color, color, color];\n\t}\n\n\tvar mult = (~~(args > 50) + 1) * 0.5;\n\tvar r = ((color & 1) * mult) * 255;\n\tvar g = (((color >> 1) & 1) * mult) * 255;\n\tvar b = (((color >> 2) & 1) * mult) * 255;\n\n\treturn [r, g, b];\n};\n\nconvert.ansi256.rgb = function (args) {\n\t// handle greyscale\n\tif (args >= 232) {\n\t\tvar c = (args - 232) * 10 + 8;\n\t\treturn [c, c, c];\n\t}\n\n\targs -= 16;\n\n\tvar rem;\n\tvar r = Math.floor(args / 36) / 5 * 255;\n\tvar g = Math.floor((rem = args % 36) / 6) / 5 * 255;\n\tvar b = (rem % 6) / 5 * 255;\n\n\treturn [r, g, b];\n};\n\nconvert.rgb.hex = function (args) {\n\tvar integer = ((Math.round(args[0]) & 0xFF) << 16)\n\t\t+ ((Math.round(args[1]) & 0xFF) << 8)\n\t\t+ (Math.round(args[2]) & 0xFF);\n\n\tvar string = integer.toString(16).toUpperCase();\n\treturn '000000'.substring(string.length) + string;\n};\n\nconvert.hex.rgb = function (args) {\n\tvar match = args.toString(16).match(/[a-f0-9]{6}|[a-f0-9]{3}/i);\n\tif (!match) {\n\t\treturn [0, 0, 0];\n\t}\n\n\tvar colorString = match[0];\n\n\tif (match[0].length === 3) {\n\t\tcolorString = colorString.split('').map(function (char) {\n\t\t\treturn char + char;\n\t\t}).join('');\n\t}\n\n\tvar integer = parseInt(colorString, 16);\n\tvar r = (integer >> 16) & 0xFF;\n\tvar g = (integer >> 8) & 0xFF;\n\tvar b = integer & 0xFF;\n\n\treturn [r, g, b];\n};\n\nconvert.rgb.hcg = function (rgb) {\n\tvar r = rgb[0] / 255;\n\tvar g = rgb[1] / 255;\n\tvar b = rgb[2] / 255;\n\tvar max = Math.max(Math.max(r, g), b);\n\tvar min = Math.min(Math.min(r, g), b);\n\tvar chroma = (max - min);\n\tvar grayscale;\n\tvar hue;\n\n\tif (chroma < 1) {\n\t\tgrayscale = min / (1 - chroma);\n\t} else {\n\t\tgrayscale = 0;\n\t}\n\n\tif (chroma <= 0) {\n\t\thue = 0;\n\t} else\n\tif (max === r) {\n\t\thue = ((g - b) / chroma) % 6;\n\t} else\n\tif (max === g) {\n\t\thue = 2 + (b - r) / chroma;\n\t} else {\n\t\thue = 4 + (r - g) / chroma + 4;\n\t}\n\n\thue /= 6;\n\thue %= 1;\n\n\treturn [hue * 360, chroma * 100, grayscale * 100];\n};\n\nconvert.hsl.hcg = function (hsl) {\n\tvar s = hsl[1] / 100;\n\tvar l = hsl[2] / 100;\n\tvar c = 1;\n\tvar f = 0;\n\n\tif (l < 0.5) {\n\t\tc = 2.0 * s * l;\n\t} else {\n\t\tc = 2.0 * s * (1.0 - l);\n\t}\n\n\tif (c < 1.0) {\n\t\tf = (l - 0.5 * c) / (1.0 - c);\n\t}\n\n\treturn [hsl[0], c * 100, f * 100];\n};\n\nconvert.hsv.hcg = function (hsv) {\n\tvar s = hsv[1] / 100;\n\tvar v = hsv[2] / 100;\n\n\tvar c = s * v;\n\tvar f = 0;\n\n\tif (c < 1.0) {\n\t\tf = (v - c) / (1 - c);\n\t}\n\n\treturn [hsv[0], c * 100, f * 100];\n};\n\nconvert.hcg.rgb = function (hcg) {\n\tvar h = hcg[0] / 360;\n\tvar c = hcg[1] / 100;\n\tvar g = hcg[2] / 100;\n\n\tif (c === 0.0) {\n\t\treturn [g * 255, g * 255, g * 255];\n\t}\n\n\tvar pure = [0, 0, 0];\n\tvar hi = (h % 1) * 6;\n\tvar v = hi % 1;\n\tvar w = 1 - v;\n\tvar mg = 0;\n\n\tswitch (Math.floor(hi)) {\n\t\tcase 0:\n\t\t\tpure[0] = 1; pure[1] = v; pure[2] = 0; break;\n\t\tcase 1:\n\t\t\tpure[0] = w; pure[1] = 1; pure[2] = 0; break;\n\t\tcase 2:\n\t\t\tpure[0] = 0; pure[1] = 1; pure[2] = v; break;\n\t\tcase 3:\n\t\t\tpure[0] = 0; pure[1] = w; pure[2] = 1; break;\n\t\tcase 4:\n\t\t\tpure[0] = v; pure[1] = 0; pure[2] = 1; break;\n\t\tdefault:\n\t\t\tpure[0] = 1; pure[1] = 0; pure[2] = w;\n\t}\n\n\tmg = (1.0 - c) * g;\n\n\treturn [\n\t\t(c * pure[0] + mg) * 255,\n\t\t(c * pure[1] + mg) * 255,\n\t\t(c * pure[2] + mg) * 255\n\t];\n};\n\nconvert.hcg.hsv = function (hcg) {\n\tvar c = hcg[1] / 100;\n\tvar g = hcg[2] / 100;\n\n\tvar v = c + g * (1.0 - c);\n\tvar f = 0;\n\n\tif (v > 0.0) {\n\t\tf = c / v;\n\t}\n\n\treturn [hcg[0], f * 100, v * 100];\n};\n\nconvert.hcg.hsl = function (hcg) {\n\tvar c = hcg[1] / 100;\n\tvar g = hcg[2] / 100;\n\n\tvar l = g * (1.0 - c) + 0.5 * c;\n\tvar s = 0;\n\n\tif (l > 0.0 && l < 0.5) {\n\t\ts = c / (2 * l);\n\t} else\n\tif (l >= 0.5 && l < 1.0) {\n\t\ts = c / (2 * (1 - l));\n\t}\n\n\treturn [hcg[0], s * 100, l * 100];\n};\n\nconvert.hcg.hwb = function (hcg) {\n\tvar c = hcg[1] / 100;\n\tvar g = hcg[2] / 100;\n\tvar v = c + g * (1.0 - c);\n\treturn [hcg[0], (v - c) * 100, (1 - v) * 100];\n};\n\nconvert.hwb.hcg = function (hwb) {\n\tvar w = hwb[1] / 100;\n\tvar b = hwb[2] / 100;\n\tvar v = 1 - b;\n\tvar c = v - w;\n\tvar g = 0;\n\n\tif (c < 1) {\n\t\tg = (v - c) / (1 - c);\n\t}\n\n\treturn [hwb[0], c * 100, g * 100];\n};\n\nconvert.apple.rgb = function (apple) {\n\treturn [(apple[0] / 65535) * 255, (apple[1] / 65535) * 255, (apple[2] / 65535) * 255];\n};\n\nconvert.rgb.apple = function (rgb) {\n\treturn [(rgb[0] / 255) * 65535, (rgb[1] / 255) * 65535, (rgb[2] / 255) * 65535];\n};\n\nconvert.gray.rgb = function (args) {\n\treturn [args[0] / 100 * 255, args[0] / 100 * 255, args[0] / 100 * 255];\n};\n\nconvert.gray.hsl = convert.gray.hsv = function (args) {\n\treturn [0, 0, args[0]];\n};\n\nconvert.gray.hwb = function (gray) {\n\treturn [0, 100, gray[0]];\n};\n\nconvert.gray.cmyk = function (gray) {\n\treturn [0, 0, 0, gray[0]];\n};\n\nconvert.gray.lab = function (gray) {\n\treturn [gray[0], 0, 0];\n};\n\nconvert.gray.hex = function (gray) {\n\tvar val = Math.round(gray[0] / 100 * 255) & 0xFF;\n\tvar integer = (val << 16) + (val << 8) + val;\n\n\tvar string = integer.toString(16).toUpperCase();\n\treturn '000000'.substring(string.length) + string;\n};\n\nconvert.rgb.gray = function (rgb) {\n\tvar val = (rgb[0] + rgb[1] + rgb[2]) / 3;\n\treturn [val / 255 * 100];\n};\n", "var conversions = require('./conversions');\n\n/*\n\tthis function routes a model to all other models.\n\n\tall functions that are routed have a property `.conversion` attached\n\tto the returned synthetic function. This property is an array\n\tof strings, each with the steps in between the 'from' and 'to'\n\tcolor models (inclusive).\n\n\tconversions that are not possible simply are not included.\n*/\n\nfunction buildGraph() {\n\tvar graph = {};\n\t// https://jsperf.com/object-keys-vs-for-in-with-closure/3\n\tvar models = Object.keys(conversions);\n\n\tfor (var len = models.length, i = 0; i < len; i++) {\n\t\tgraph[models[i]] = {\n\t\t\t// http://jsperf.com/1-vs-infinity\n\t\t\t// micro-opt, but this is simple.\n\t\t\tdistance: -1,\n\t\t\tparent: null\n\t\t};\n\t}\n\n\treturn graph;\n}\n\n// https://en.wikipedia.org/wiki/Breadth-first_search\nfunction deriveBFS(fromModel) {\n\tvar graph = buildGraph();\n\tvar queue = [fromModel]; // unshift -> queue -> pop\n\n\tgraph[fromModel].distance = 0;\n\n\twhile (queue.length) {\n\t\tvar current = queue.pop();\n\t\tvar adjacents = Object.keys(conversions[current]);\n\n\t\tfor (var len = adjacents.length, i = 0; i < len; i++) {\n\t\t\tvar adjacent = adjacents[i];\n\t\t\tvar node = graph[adjacent];\n\n\t\t\tif (node.distance === -1) {\n\t\t\t\tnode.distance = graph[current].distance + 1;\n\t\t\t\tnode.parent = current;\n\t\t\t\tqueue.unshift(adjacent);\n\t\t\t}\n\t\t}\n\t}\n\n\treturn graph;\n}\n\nfunction link(from, to) {\n\treturn function (args) {\n\t\treturn to(from(args));\n\t};\n}\n\nfunction wrapConversion(toModel, graph) {\n\tvar path = [graph[toModel].parent, toModel];\n\tvar fn = conversions[graph[toModel].parent][toModel];\n\n\tvar cur = graph[toModel].parent;\n\twhile (graph[cur].parent) {\n\t\tpath.unshift(graph[cur].parent);\n\t\tfn = link(conversions[graph[cur].parent][cur], fn);\n\t\tcur = graph[cur].parent;\n\t}\n\n\tfn.conversion = path;\n\treturn fn;\n}\n\nmodule.exports = function (fromModel) {\n\tvar graph = deriveBFS(fromModel);\n\tvar conversion = {};\n\n\tvar models = Object.keys(graph);\n\tfor (var len = models.length, i = 0; i < len; i++) {\n\t\tvar toModel = models[i];\n\t\tvar node = graph[toModel];\n\n\t\tif (node.parent === null) {\n\t\t\t// no possible conversion, or this node is the source model.\n\t\t\tcontinue;\n\t\t}\n\n\t\tconversion[toModel] = wrapConversion(toModel, graph);\n\t}\n\n\treturn conversion;\n};\n\n", "var conversions = require('./conversions');\nvar route = require('./route');\n\nvar convert = {};\n\nvar models = Object.keys(conversions);\n\nfunction wrapRaw(fn) {\n\tvar wrappedFn = function (args) {\n\t\tif (args === undefined || args === null) {\n\t\t\treturn args;\n\t\t}\n\n\t\tif (arguments.length > 1) {\n\t\t\targs = Array.prototype.slice.call(arguments);\n\t\t}\n\n\t\treturn fn(args);\n\t};\n\n\t// preserve .conversion property if there is one\n\tif ('conversion' in fn) {\n\t\twrappedFn.conversion = fn.conversion;\n\t}\n\n\treturn wrappedFn;\n}\n\nfunction wrapRounded(fn) {\n\tvar wrappedFn = function (args) {\n\t\tif (args === undefined || args === null) {\n\t\t\treturn args;\n\t\t}\n\n\t\tif (arguments.length > 1) {\n\t\t\targs = Array.prototype.slice.call(arguments);\n\t\t}\n\n\t\tvar result = fn(args);\n\n\t\t// we're assuming the result is an array here.\n\t\t// see notice in conversions.js; don't use box types\n\t\t// in conversion functions.\n\t\tif (typeof result === 'object') {\n\t\t\tfor (var len = result.length, i = 0; i < len; i++) {\n\t\t\t\tresult[i] = Math.round(result[i]);\n\t\t\t}\n\t\t}\n\n\t\treturn result;\n\t};\n\n\t// preserve .conversion property if there is one\n\tif ('conversion' in fn) {\n\t\twrappedFn.conversion = fn.conversion;\n\t}\n\n\treturn wrappedFn;\n}\n\nmodels.forEach(function (fromModel) {\n\tconvert[fromModel] = {};\n\n\tObject.defineProperty(convert[fromModel], 'channels', {value: conversions[fromModel].channels});\n\tObject.defineProperty(convert[fromModel], 'labels', {value: conversions[fromModel].labels});\n\n\tvar routes = route(fromModel);\n\tvar routeModels = Object.keys(routes);\n\n\trouteModels.forEach(function (toModel) {\n\t\tvar fn = routes[toModel];\n\n\t\tconvert[fromModel][toModel] = wrapRounded(fn);\n\t\tconvert[fromModel][toModel].raw = wrapRaw(fn);\n\t});\n});\n\nmodule.exports = convert;\n", "'use strict';\n\nvar colorString = require('color-string');\nvar convert = require('color-convert');\n\nvar _slice = [].slice;\n\nvar skippedModels = [\n\t// to be honest, I don't really feel like keyword belongs in color convert, but eh.\n\t'keyword',\n\n\t// gray conflicts with some method names, and has its own method defined.\n\t'gray',\n\n\t// shouldn't really be in color-convert either...\n\t'hex'\n];\n\nvar hashedModelKeys = {};\nObject.keys(convert).forEach(function (model) {\n\thashedModelKeys[_slice.call(convert[model].labels).sort().join('')] = model;\n});\n\nvar limiters = {};\n\nfunction Color(obj, model) {\n\tif (!(this instanceof Color)) {\n\t\treturn new Color(obj, model);\n\t}\n\n\tif (model && model in skippedModels) {\n\t\tmodel = null;\n\t}\n\n\tif (model && !(model in convert)) {\n\t\tthrow new Error('Unknown model: ' + model);\n\t}\n\n\tvar i;\n\tvar channels;\n\n\tif (obj == null) { // eslint-disable-line no-eq-null,eqeqeq\n\t\tthis.model = 'rgb';\n\t\tthis.color = [0, 0, 0];\n\t\tthis.valpha = 1;\n\t} else if (obj instanceof Color) {\n\t\tthis.model = obj.model;\n\t\tthis.color = obj.color.slice();\n\t\tthis.valpha = obj.valpha;\n\t} else if (typeof obj === 'string') {\n\t\tvar result = colorString.get(obj);\n\t\tif (result === null) {\n\t\t\tthrow new Error('Unable to parse color from string: ' + obj);\n\t\t}\n\n\t\tthis.model = result.model;\n\t\tchannels = convert[this.model].channels;\n\t\tthis.color = result.value.slice(0, channels);\n\t\tthis.valpha = typeof result.value[channels] === 'number' ? result.value[channels] : 1;\n\t} else if (obj.length) {\n\t\tthis.model = model || 'rgb';\n\t\tchannels = convert[this.model].channels;\n\t\tvar newArr = _slice.call(obj, 0, channels);\n\t\tthis.color = zeroArray(newArr, channels);\n\t\tthis.valpha = typeof obj[channels] === 'number' ? obj[channels] : 1;\n\t} else if (typeof obj === 'number') {\n\t\t// this is always RGB - can be converted later on.\n\t\tobj &= 0xFFFFFF;\n\t\tthis.model = 'rgb';\n\t\tthis.color = [\n\t\t\t(obj >> 16) & 0xFF,\n\t\t\t(obj >> 8) & 0xFF,\n\t\t\tobj & 0xFF\n\t\t];\n\t\tthis.valpha = 1;\n\t} else {\n\t\tthis.valpha = 1;\n\n\t\tvar keys = Object.keys(obj);\n\t\tif ('alpha' in obj) {\n\t\t\tkeys.splice(keys.indexOf('alpha'), 1);\n\t\t\tthis.valpha = typeof obj.alpha === 'number' ? obj.alpha : 0;\n\t\t}\n\n\t\tvar hashedKeys = keys.sort().join('');\n\t\tif (!(hashedKeys in hashedModelKeys)) {\n\t\t\tthrow new Error('Unable to parse color from object: ' + JSON.stringify(obj));\n\t\t}\n\n\t\tthis.model = hashedModelKeys[hashedKeys];\n\n\t\tvar labels = convert[this.model].labels;\n\t\tvar color = [];\n\t\tfor (i = 0; i < labels.length; i++) {\n\t\t\tcolor.push(obj[labels[i]]);\n\t\t}\n\n\t\tthis.color = zeroArray(color);\n\t}\n\n\t// perform limitations (clamping, etc.)\n\tif (limiters[this.model]) {\n\t\tchannels = convert[this.model].channels;\n\t\tfor (i = 0; i < channels; i++) {\n\t\t\tvar limit = limiters[this.model][i];\n\t\t\tif (limit) {\n\t\t\t\tthis.color[i] = limit(this.color[i]);\n\t\t\t}\n\t\t}\n\t}\n\n\tthis.valpha = Math.max(0, Math.min(1, this.valpha));\n\n\tif (Object.freeze) {\n\t\tObject.freeze(this);\n\t}\n}\n\nColor.prototype = {\n\ttoString: function () {\n\t\treturn this.string();\n\t},\n\n\ttoJSON: function () {\n\t\treturn this[this.model]();\n\t},\n\n\tstring: function (places) {\n\t\tvar self = this.model in colorString.to ? this : this.rgb();\n\t\tself = self.round(typeof places === 'number' ? places : 1);\n\t\tvar args = self.valpha === 1 ? self.color : self.color.concat(this.valpha);\n\t\treturn colorString.to[self.model](args);\n\t},\n\n\tpercentString: function (places) {\n\t\tvar self = this.rgb().round(typeof places === 'number' ? places : 1);\n\t\tvar args = self.valpha === 1 ? self.color : self.color.concat(this.valpha);\n\t\treturn colorString.to.rgb.percent(args);\n\t},\n\n\tarray: function () {\n\t\treturn this.valpha === 1 ? this.color.slice() : this.color.concat(this.valpha);\n\t},\n\n\tobject: function () {\n\t\tvar result = {};\n\t\tvar channels = convert[this.model].channels;\n\t\tvar labels = convert[this.model].labels;\n\n\t\tfor (var i = 0; i < channels; i++) {\n\t\t\tresult[labels[i]] = this.color[i];\n\t\t}\n\n\t\tif (this.valpha !== 1) {\n\t\t\tresult.alpha = this.valpha;\n\t\t}\n\n\t\treturn result;\n\t},\n\n\tunitArray: function () {\n\t\tvar rgb = this.rgb().color;\n\t\trgb[0] /= 255;\n\t\trgb[1] /= 255;\n\t\trgb[2] /= 255;\n\n\t\tif (this.valpha !== 1) {\n\t\t\trgb.push(this.valpha);\n\t\t}\n\n\t\treturn rgb;\n\t},\n\n\tunitObject: function () {\n\t\tvar rgb = this.rgb().object();\n\t\trgb.r /= 255;\n\t\trgb.g /= 255;\n\t\trgb.b /= 255;\n\n\t\tif (this.valpha !== 1) {\n\t\t\trgb.alpha = this.valpha;\n\t\t}\n\n\t\treturn rgb;\n\t},\n\n\tround: function (places) {\n\t\tplaces = Math.max(places || 0, 0);\n\t\treturn new Color(this.color.map(roundToPlace(places)).concat(this.valpha), this.model);\n\t},\n\n\talpha: function (val) {\n\t\tif (arguments.length) {\n\t\t\treturn new Color(this.color.concat(Math.max(0, Math.min(1, val))), this.model);\n\t\t}\n\n\t\treturn this.valpha;\n\t},\n\n\t// rgb\n\tred: getset('rgb', 0, maxfn(255)),\n\tgreen: getset('rgb', 1, maxfn(255)),\n\tblue: getset('rgb', 2, maxfn(255)),\n\n\thue: getset(['hsl', 'hsv', 'hsl', 'hwb', 'hcg'], 0, function (val) { return ((val % 360) + 360) % 360; }), // eslint-disable-line brace-style\n\n\tsaturationl: getset('hsl', 1, maxfn(100)),\n\tlightness: getset('hsl', 2, maxfn(100)),\n\n\tsaturationv: getset('hsv', 1, maxfn(100)),\n\tvalue: getset('hsv', 2, maxfn(100)),\n\n\tchroma: getset('hcg', 1, maxfn(100)),\n\tgray: getset('hcg', 2, maxfn(100)),\n\n\twhite: getset('hwb', 1, maxfn(100)),\n\twblack: getset('hwb', 2, maxfn(100)),\n\n\tcyan: getset('cmyk', 0, maxfn(100)),\n\tmagenta: getset('cmyk', 1, maxfn(100)),\n\tyellow: getset('cmyk', 2, maxfn(100)),\n\tblack: getset('cmyk', 3, maxfn(100)),\n\n\tx: getset('xyz', 0, maxfn(100)),\n\ty: getset('xyz', 1, maxfn(100)),\n\tz: getset('xyz', 2, maxfn(100)),\n\n\tl: getset('lab', 0, maxfn(100)),\n\ta: getset('lab', 1),\n\tb: getset('lab', 2),\n\n\tkeyword: function (val) {\n\t\tif (arguments.length) {\n\t\t\treturn new Color(val);\n\t\t}\n\n\t\treturn convert[this.model].keyword(this.color);\n\t},\n\n\thex: function (val) {\n\t\tif (arguments.length) {\n\t\t\treturn new Color(val);\n\t\t}\n\n\t\treturn colorString.to.hex(this.rgb().round().color);\n\t},\n\n\trgbNumber: function () {\n\t\tvar rgb = this.rgb().color;\n\t\treturn ((rgb[0] & 0xFF) << 16) | ((rgb[1] & 0xFF) << 8) | (rgb[2] & 0xFF);\n\t},\n\n\tluminosity: function () {\n\t\t// http://www.w3.org/TR/WCAG20/#relativeluminancedef\n\t\tvar rgb = this.rgb().color;\n\n\t\tvar lum = [];\n\t\tfor (var i = 0; i < rgb.length; i++) {\n\t\t\tvar chan = rgb[i] / 255;\n\t\t\tlum[i] = (chan <= 0.03928) ? chan / 12.92 : Math.pow(((chan + 0.055) / 1.055), 2.4);\n\t\t}\n\n\t\treturn 0.2126 * lum[0] + 0.7152 * lum[1] + 0.0722 * lum[2];\n\t},\n\n\tcontrast: function (color2) {\n\t\t// http://www.w3.org/TR/WCAG20/#contrast-ratiodef\n\t\tvar lum1 = this.luminosity();\n\t\tvar lum2 = color2.luminosity();\n\n\t\tif (lum1 > lum2) {\n\t\t\treturn (lum1 + 0.05) / (lum2 + 0.05);\n\t\t}\n\n\t\treturn (lum2 + 0.05) / (lum1 + 0.05);\n\t},\n\n\tlevel: function (color2) {\n\t\tvar contrastRatio = this.contrast(color2);\n\t\tif (contrastRatio >= 7.1) {\n\t\t\treturn 'AAA';\n\t\t}\n\n\t\treturn (contrastRatio >= 4.5) ? 'AA' : '';\n\t},\n\n\tisDark: function () {\n\t\t// YIQ equation from http://24ways.org/2010/calculating-color-contrast\n\t\tvar rgb = this.rgb().color;\n\t\tvar yiq = (rgb[0] * 299 + rgb[1] * 587 + rgb[2] * 114) / 1000;\n\t\treturn yiq < 128;\n\t},\n\n\tisLight: function () {\n\t\treturn !this.isDark();\n\t},\n\n\tnegate: function () {\n\t\tvar rgb = this.rgb();\n\t\tfor (var i = 0; i < 3; i++) {\n\t\t\trgb.color[i] = 255 - rgb.color[i];\n\t\t}\n\t\treturn rgb;\n\t},\n\n\tlighten: function (ratio) {\n\t\tvar hsl = this.hsl();\n\t\thsl.color[2] += hsl.color[2] * ratio;\n\t\treturn hsl;\n\t},\n\n\tdarken: function (ratio) {\n\t\tvar hsl = this.hsl();\n\t\thsl.color[2] -= hsl.color[2] * ratio;\n\t\treturn hsl;\n\t},\n\n\tsaturate: function (ratio) {\n\t\tvar hsl = this.hsl();\n\t\thsl.color[1] += hsl.color[1] * ratio;\n\t\treturn hsl;\n\t},\n\n\tdesaturate: function (ratio) {\n\t\tvar hsl = this.hsl();\n\t\thsl.color[1] -= hsl.color[1] * ratio;\n\t\treturn hsl;\n\t},\n\n\twhiten: function (ratio) {\n\t\tvar hwb = this.hwb();\n\t\thwb.color[1] += hwb.color[1] * ratio;\n\t\treturn hwb;\n\t},\n\n\tblacken: function (ratio) {\n\t\tvar hwb = this.hwb();\n\t\thwb.color[2] += hwb.color[2] * ratio;\n\t\treturn hwb;\n\t},\n\n\tgrayscale: function () {\n\t\t// http://en.wikipedia.org/wiki/Grayscale#Converting_color_to_grayscale\n\t\tvar rgb = this.rgb().color;\n\t\tvar val = rgb[0] * 0.3 + rgb[1] * 0.59 + rgb[2] * 0.11;\n\t\treturn Color.rgb(val, val, val);\n\t},\n\n\tfade: function (ratio) {\n\t\treturn this.alpha(this.valpha - (this.valpha * ratio));\n\t},\n\n\topaquer: function (ratio) {\n\t\treturn this.alpha(this.valpha + (this.valpha * ratio));\n\t},\n\n\trotate: function (degrees) {\n\t\tvar hsl = this.hsl();\n\t\tvar hue = hsl.color[0];\n\t\thue = (hue + degrees) % 360;\n\t\thue = hue < 0 ? 360 + hue : hue;\n\t\thsl.color[0] = hue;\n\t\treturn hsl;\n\t},\n\n\tmix: function (mixinColor, weight) {\n\t\t// ported from sass implementation in C\n\t\t// https://github.com/sass/libsass/blob/0e6b4a2850092356aa3ece07c6b249f0221caced/functions.cpp#L209\n\t\tif (!mixinColor || !mixinColor.rgb) {\n\t\t\tthrow new Error('Argument to \"mix\" was not a Color instance, but rather an instance of ' + typeof mixinColor);\n\t\t}\n\t\tvar color1 = mixinColor.rgb();\n\t\tvar color2 = this.rgb();\n\t\tvar p = weight === undefined ? 0.5 : weight;\n\n\t\tvar w = 2 * p - 1;\n\t\tvar a = color1.alpha() - color2.alpha();\n\n\t\tvar w1 = (((w * a === -1) ? w : (w + a) / (1 + w * a)) + 1) / 2.0;\n\t\tvar w2 = 1 - w1;\n\n\t\treturn Color.rgb(\n\t\t\t\tw1 * color1.red() + w2 * color2.red(),\n\t\t\t\tw1 * color1.green() + w2 * color2.green(),\n\t\t\t\tw1 * color1.blue() + w2 * color2.blue(),\n\t\t\t\tcolor1.alpha() * p + color2.alpha() * (1 - p));\n\t}\n};\n\n// model conversion methods and static constructors\nObject.keys(convert).forEach(function (model) {\n\tif (skippedModels.indexOf(model) !== -1) {\n\t\treturn;\n\t}\n\n\tvar channels = convert[model].channels;\n\n\t// conversion methods\n\tColor.prototype[model] = function () {\n\t\tif (this.model === model) {\n\t\t\treturn new Color(this);\n\t\t}\n\n\t\tif (arguments.length) {\n\t\t\treturn new Color(arguments, model);\n\t\t}\n\n\t\tvar newAlpha = typeof arguments[channels] === 'number' ? channels : this.valpha;\n\t\treturn new Color(assertArray(convert[this.model][model].raw(this.color)).concat(newAlpha), model);\n\t};\n\n\t// 'static' construction methods\n\tColor[model] = function (color) {\n\t\tif (typeof color === 'number') {\n\t\t\tcolor = zeroArray(_slice.call(arguments), channels);\n\t\t}\n\t\treturn new Color(color, model);\n\t};\n});\n\nfunction roundTo(num, places) {\n\treturn Number(num.toFixed(places));\n}\n\nfunction roundToPlace(places) {\n\treturn function (num) {\n\t\treturn roundTo(num, places);\n\t};\n}\n\nfunction getset(model, channel, modifier) {\n\tmodel = Array.isArray(model) ? model : [model];\n\n\tmodel.forEach(function (m) {\n\t\t(limiters[m] || (limiters[m] = []))[channel] = modifier;\n\t});\n\n\tmodel = model[0];\n\n\treturn function (val) {\n\t\tvar result;\n\n\t\tif (arguments.length) {\n\t\t\tif (modifier) {\n\t\t\t\tval = modifier(val);\n\t\t\t}\n\n\t\t\tresult = this[model]();\n\t\t\tresult.color[channel] = val;\n\t\t\treturn result;\n\t\t}\n\n\t\tresult = this[model]().color[channel];\n\t\tif (modifier) {\n\t\t\tresult = modifier(result);\n\t\t}\n\n\t\treturn result;\n\t};\n}\n\nfunction maxfn(max) {\n\treturn function (v) {\n\t\treturn Math.max(0, Math.min(max, v));\n\t};\n}\n\nfunction assertArray(val) {\n\treturn Array.isArray(val) ? val : [val];\n}\n\nfunction zeroArray(arr, length) {\n\tfor (var i = 0; i < length; i++) {\n\t\tif (typeof arr[i] !== 'number') {\n\t\t\tarr[i] = 0;\n\t\t}\n\t}\n\n\treturn arr;\n}\n\nmodule.exports = Color;\n", "import Color from 'color';\n\nexport {Color};\n\nexport function lightenToContrastThreshold(foreground: string | Color, background: string | Color, contrastThreshold: number): Color {\n    const foregroundColor = Color(foreground);\n    const backgroundColor = Color(background);\n\n    const {h,s} = foregroundColor.hsl().object();\n\n    let newColor = foregroundColor;\n\n    while (newColor.contrast(backgroundColor) < contrastThreshold) {\n        if (newColor.lightness() >= 100) {\n            break;\n        }\n\n        newColor = Color({h, s, l: newColor.lightness() + 5});\n    }\n\n    return newColor;\n}\n\nexport function darkenToContrastThreshold(foreground: string | Color, background: string | Color, contrastThreshold: number): Color {\n    const foregroundColor = Color(foreground);\n    const backgroundColor = Color(background);\n\n    const {h,s} = foregroundColor.hsl().object();\n\n    let newColor = foregroundColor;\n\n    while (newColor.contrast(backgroundColor) < contrastThreshold) {\n        if (newColor.lightness() <= 0) {\n            break;\n        }\n\n        newColor = Color({h, s, l: newColor.lightness() - 5});\n    }\n\n    return newColor;\n}\n\nexport function textColorForBackgroundColor(background: string | Color): Color {\n    const backgroundColor = Color(background);\n\n    const white = Color({r: 255, g: 255, b: 255});\n    const black = Color({r: 0, g: 0, b: 0});\n\n    // shared with Portal https://github.com/TryGhost/Portal/blob/317876f20d22431df15e655ea6cc197fe636615e/src/utils/contrast-color.js#L26-L29\n    const yiq = (\n        backgroundColor.red() * 0.299 +\n        backgroundColor.green() * 0.587 +\n        backgroundColor.b() * 0.114\n    );\n\n    return (yiq >= 186) ? black : white;\n}\n"], "names": ["colorName", "aliceblue", "antiquewhite", "aqua", "aquamarine", "azure", "beige", "bisque", "black", "blanche<PERSON><PERSON>", "blue", "blueviolet", "brown", "burlywood", "cadetblue", "chartreuse", "chocolate", "coral", "cornflowerblue", "cornsilk", "crimson", "cyan", "darkblue", "dark<PERSON>an", "darkgoldenrod", "darkgray", "darkgreen", "<PERSON><PERSON>rey", "<PERSON><PERSON><PERSON>", "darkmagenta", "darkolivegreen", "darkorange", "darkorchid", "darkred", "<PERSON><PERSON><PERSON>", "darkseagreen", "darkslateblue", "darkslategray", "darkslateg<PERSON>", "darkturquoise", "darkviolet", "deeppink", "deepskyblue", "dimgray", "<PERSON><PERSON><PERSON>", "dodgerblue", "firebrick", "<PERSON><PERSON><PERSON><PERSON>", "forestgreen", "fuchsia", "gainsboro", "ghostwhite", "gold", "goldenrod", "gray", "green", "greenyellow", "grey", "honeydew", "hotpink", "indianred", "indigo", "ivory", "khaki", "lavender", "lavenderblush", "lawngreen", "lemon<PERSON>ffon", "lightblue", "lightcoral", "lightcyan", "lightgoldenrodyellow", "lightgray", "lightgreen", "<PERSON><PERSON>rey", "lightpink", "<PERSON><PERSON><PERSON>", "lightseagreen", "lightskyblue", "lightslategray", "lightslategrey", "lightsteelblue", "lightyellow", "lime", "limegreen", "linen", "magenta", "maroon", "mediumaquamarine", "mediumblue", "mediumorchid", "mediumpurple", "mediumseagreen", "mediumslateblue", "mediumspringgreen", "mediumturquoise", "mediumvioletred", "midnightblue", "mintcream", "mistyrose", "moccasin", "navajowhite", "navy", "oldlace", "olive", "<PERSON><PERSON><PERSON>", "orange", "orangered", "orchid", "palegoldenrod", "palegreen", "paleturquoise", "palevioletred", "papayawhip", "peachpuff", "peru", "pink", "plum", "powderblue", "purple", "rebeccapurple", "red", "rosybrown", "royalblue", "saddlebrown", "salmon", "sandybrown", "seagreen", "seashell", "sienna", "silver", "skyblue", "slateblue", "slategray", "<PERSON><PERSON><PERSON>", "snow", "springgreen", "steelblue", "tan", "teal", "thistle", "tomato", "turquoise", "violet", "wheat", "white", "whitesmoke", "yellow", "yellowgreen", "concat", "Array", "prototype", "slice", "swizzle", "module", "exports", "args", "obj", "results", "i", "len", "length", "arg", "isArray", "splice", "Function", "Object", "getOwnPropertyDescriptor", "constructor", "name", "call", "push", "wrap", "fn", "arguments", "hasOwnProperty", "reverseNames", "create", "colorNames", "cs", "to", "get", "clamp", "num", "min", "max", "Math", "hexDouble", "str", "round", "toString", "toUpperCase", "string", "val", "model", "substring", "toLowerCase", "hsl", "hwb", "rgb", "value", "match", "hexAlpha", "i2", "parseInt", "parseFloat", "alpha", "isNaN", "hex", "rgba", "percent", "r", "g", "b", "hsla", "hwba", "a", "keyword", "reverseKeywords", "key", "cssKeywords", "convert", "channels", "labels", "hsv", "cmyk", "xyz", "lab", "lch", "ansi16", "ansi256", "hcg", "apple", "Error", "defineProperty", "h", "l", "delta", "rdif", "gdif", "bdif", "s", "v", "diff", "diffc", "c", "k", "reversed", "currentClosestKeyword", "x", "y", "currentClosestDistance", "Infinity", "distance", "pow", "z", "t1", "t2", "t3", "smin", "lmin", "hi", "floor", "f", "p", "q", "t", "sl", "vmin", "n", "wh", "bl", "ratio", "m", "y2", "x2", "z2", "atan2", "PI", "sqrt", "hr", "cos", "sin", "ansi", "color", "mult", "rem", "colorString", "split", "map", "char", "join", "integer", "hue", "chroma", "mg", "pure", "w", "deriveBFS", "fromModel", "graph", "models", "keys", "conversions", "parent", "buildGraph", "queue", "current", "pop", "adjacents", "adjacent", "node", "unshift", "link", "from", "wrapConversion", "toModel", "path", "cur", "conversion", "for<PERSON>ach", "routes", "route", "wrappedFn", "result", "wrapRounded", "raw", "wrapRaw", "colorConvert", "_slice", "skippedModels", "hashedModelKeys", "sort", "limiters", "Color", "this", "valpha", "newArr", "zeroArray", "indexOf", "hashedKeys", "JSON", "stringify", "limit", "freeze", "getset", "channel", "modifier", "maxfn", "arr", "toJSON", "places", "self", "percentString", "array", "object", "unitArray", "unitObject", "Number", "toFixed", "roundTo", "roundToPlace", "saturationl", "lightness", "saturationv", "wblack", "rgbNumber", "luminosity", "lum", "chan", "contrast", "color2", "lum1", "lum2", "level", "contrastRatio", "isDark", "isLight", "negate", "lighten", "darken", "saturate", "desaturate", "whiten", "blacken", "grayscale", "fade", "opaquer", "rotate", "degrees", "mix", "mixinColor", "weight", "color1", "undefined", "w1", "w2", "newAlpha", "foreground", "background", "contrastThreshold", "foregroundColor", "backgroundColor", "newColor"], "mappings": "oTAEA,IAAAA,EAAiB,CAChBC,UAAa,CAAC,IAAK,IAAK,KACxBC,aAAgB,CAAC,IAAK,IAAK,KAC3BC,KAAQ,CAAC,EAAG,IAAK,KACjBC,WAAc,CAAC,IAAK,IAAK,KACzBC,MAAS,CAAC,IAAK,IAAK,KACpBC,MAAS,CAAC,IAAK,IAAK,KACpBC,OAAU,CAAC,IAAK,IAAK,KACrBC,MAAS,CAAC,EAAG,EAAG,GAChBC,eAAkB,CAAC,IAAK,IAAK,KAC7BC,KAAQ,CAAC,EAAG,EAAG,KACfC,WAAc,CAAC,IAAK,GAAI,KACxBC,MAAS,CAAC,IAAK,GAAI,IACnBC,UAAa,CAAC,IAAK,IAAK,KACxBC,UAAa,CAAC,GAAI,IAAK,KACvBC,WAAc,CAAC,IAAK,IAAK,GACzBC,UAAa,CAAC,IAAK,IAAK,IACxBC,MAAS,CAAC,IAAK,IAAK,IACpBC,eAAkB,CAAC,IAAK,IAAK,KAC7BC,SAAY,CAAC,IAAK,IAAK,KACvBC,QAAW,CAAC,IAAK,GAAI,IACrBC,KAAQ,CAAC,EAAG,IAAK,KACjBC,SAAY,CAAC,EAAG,EAAG,KACnBC,SAAY,CAAC,EAAG,IAAK,KACrBC,cAAiB,CAAC,IAAK,IAAK,IAC5BC,SAAY,CAAC,IAAK,IAAK,KACvBC,UAAa,CAAC,EAAG,IAAK,GACtBC,SAAY,CAAC,IAAK,IAAK,KACvBC,UAAa,CAAC,IAAK,IAAK,KACxBC,YAAe,CAAC,IAAK,EAAG,KACxBC,eAAkB,CAAC,GAAI,IAAK,IAC5BC,WAAc,CAAC,IAAK,IAAK,GACzBC,WAAc,CAAC,IAAK,GAAI,KACxBC,QAAW,CAAC,IAAK,EAAG,GACpBC,WAAc,CAAC,IAAK,IAAK,KACzBC,aAAgB,CAAC,IAAK,IAAK,KAC3BC,cAAiB,CAAC,GAAI,GAAI,KAC1BC,cAAiB,CAAC,GAAI,GAAI,IAC1BC,cAAiB,CAAC,GAAI,GAAI,IAC1BC,cAAiB,CAAC,EAAG,IAAK,KAC1BC,WAAc,CAAC,IAAK,EAAG,KACvBC,SAAY,CAAC,IAAK,GAAI,KACtBC,YAAe,CAAC,EAAG,IAAK,KACxBC,QAAW,CAAC,IAAK,IAAK,KACtBC,QAAW,CAAC,IAAK,IAAK,KACtBC,WAAc,CAAC,GAAI,IAAK,KACxBC,UAAa,CAAC,IAAK,GAAI,IACvBC,YAAe,CAAC,IAAK,IAAK,KAC1BC,YAAe,CAAC,GAAI,IAAK,IACzBC,QAAW,CAAC,IAAK,EAAG,KACpBC,UAAa,CAAC,IAAK,IAAK,KACxBC,WAAc,CAAC,IAAK,IAAK,KACzBC,KAAQ,CAAC,IAAK,IAAK,GACnBC,UAAa,CAAC,IAAK,IAAK,IACxBC,KAAQ,CAAC,IAAK,IAAK,KACnBC,MAAS,CAAC,EAAG,IAAK,GAClBC,YAAe,CAAC,IAAK,IAAK,IAC1BC,KAAQ,CAAC,IAAK,IAAK,KACnBC,SAAY,CAAC,IAAK,IAAK,KACvBC,QAAW,CAAC,IAAK,IAAK,KACtBC,UAAa,CAAC,IAAK,GAAI,IACvBC,OAAU,CAAC,GAAI,EAAG,KAClBC,MAAS,CAAC,IAAK,IAAK,KACpBC,MAAS,CAAC,IAAK,IAAK,KACpBC,SAAY,CAAC,IAAK,IAAK,KACvBC,cAAiB,CAAC,IAAK,IAAK,KAC5BC,UAAa,CAAC,IAAK,IAAK,GACxBC,aAAgB,CAAC,IAAK,IAAK,KAC3BC,UAAa,CAAC,IAAK,IAAK,KACxBC,WAAc,CAAC,IAAK,IAAK,KACzBC,UAAa,CAAC,IAAK,IAAK,KACxBC,qBAAwB,CAAC,IAAK,IAAK,KACnCC,UAAa,CAAC,IAAK,IAAK,KACxBC,WAAc,CAAC,IAAK,IAAK,KACzBC,UAAa,CAAC,IAAK,IAAK,KACxBC,UAAa,CAAC,IAAK,IAAK,KACxBC,YAAe,CAAC,IAAK,IAAK,KAC1BC,cAAiB,CAAC,GAAI,IAAK,KAC3BC,aAAgB,CAAC,IAAK,IAAK,KAC3BC,eAAkB,CAAC,IAAK,IAAK,KAC7BC,eAAkB,CAAC,IAAK,IAAK,KAC7BC,eAAkB,CAAC,IAAK,IAAK,KAC7BC,YAAe,CAAC,IAAK,IAAK,KAC1BC,KAAQ,CAAC,EAAG,IAAK,GACjBC,UAAa,CAAC,GAAI,IAAK,IACvBC,MAAS,CAAC,IAAK,IAAK,KACpBC,QAAW,CAAC,IAAK,EAAG,KACpBC,OAAU,CAAC,IAAK,EAAG,GACnBC,iBAAoB,CAAC,IAAK,IAAK,KAC/BC,WAAc,CAAC,EAAG,EAAG,KACrBC,aAAgB,CAAC,IAAK,GAAI,KAC1BC,aAAgB,CAAC,IAAK,IAAK,KAC3BC,eAAkB,CAAC,GAAI,IAAK,KAC5BC,gBAAmB,CAAC,IAAK,IAAK,KAC9BC,kBAAqB,CAAC,EAAG,IAAK,KAC9BC,gBAAmB,CAAC,GAAI,IAAK,KAC7BC,gBAAmB,CAAC,IAAK,GAAI,KAC7BC,aAAgB,CAAC,GAAI,GAAI,KACzBC,UAAa,CAAC,IAAK,IAAK,KACxBC,UAAa,CAAC,IAAK,IAAK,KACxBC,SAAY,CAAC,IAAK,IAAK,KACvBC,YAAe,CAAC,IAAK,IAAK,KAC1BC,KAAQ,CAAC,EAAG,EAAG,KACfC,QAAW,CAAC,IAAK,IAAK,KACtBC,MAAS,CAAC,IAAK,IAAK,GACpBC,UAAa,CAAC,IAAK,IAAK,IACxBC,OAAU,CAAC,IAAK,IAAK,GACrBC,UAAa,CAAC,IAAK,GAAI,GACvBC,OAAU,CAAC,IAAK,IAAK,KACrBC,cAAiB,CAAC,IAAK,IAAK,KAC5BC,UAAa,CAAC,IAAK,IAAK,KACxBC,cAAiB,CAAC,IAAK,IAAK,KAC5BC,cAAiB,CAAC,IAAK,IAAK,KAC5BC,WAAc,CAAC,IAAK,IAAK,KACzBC,UAAa,CAAC,IAAK,IAAK,KACxBC,KAAQ,CAAC,IAAK,IAAK,IACnBC,KAAQ,CAAC,IAAK,IAAK,KACnBC,KAAQ,CAAC,IAAK,IAAK,KACnBC,WAAc,CAAC,IAAK,IAAK,KACzBC,OAAU,CAAC,IAAK,EAAG,KACnBC,cAAiB,CAAC,IAAK,GAAI,KAC3BC,IAAO,CAAC,IAAK,EAAG,GAChBC,UAAa,CAAC,IAAK,IAAK,KACxBC,UAAa,CAAC,GAAI,IAAK,KACvBC,YAAe,CAAC,IAAK,GAAI,IACzBC,OAAU,CAAC,IAAK,IAAK,KACrBC,WAAc,CAAC,IAAK,IAAK,IACzBC,SAAY,CAAC,GAAI,IAAK,IACtBC,SAAY,CAAC,IAAK,IAAK,KACvBC,OAAU,CAAC,IAAK,GAAI,IACpBC,OAAU,CAAC,IAAK,IAAK,KACrBC,QAAW,CAAC,IAAK,IAAK,KACtBC,UAAa,CAAC,IAAK,GAAI,KACvBC,UAAa,CAAC,IAAK,IAAK,KACxBC,UAAa,CAAC,IAAK,IAAK,KACxBC,KAAQ,CAAC,IAAK,IAAK,KACnBC,YAAe,CAAC,EAAG,IAAK,KACxBC,UAAa,CAAC,GAAI,IAAK,KACvBC,IAAO,CAAC,IAAK,IAAK,KAClBC,KAAQ,CAAC,EAAG,IAAK,KACjBC,QAAW,CAAC,IAAK,IAAK,KACtBC,OAAU,CAAC,IAAK,GAAI,IACpBC,UAAa,CAAC,GAAI,IAAK,KACvBC,OAAU,CAAC,IAAK,IAAK,KACrBC,MAAS,CAAC,IAAK,IAAK,KACpBC,MAAS,CAAC,IAAK,IAAK,KACpBC,WAAc,CAAC,IAAK,IAAK,KACzBC,OAAU,CAAC,IAAK,IAAK,GACrBC,YAAe,CAAC,IAAK,IAAK,sBClJ3B,IAAIC,EAASC,MAAMC,UAAUF,OACzBG,EAAQF,MAAMC,UAAUC,MAExBC,EAAUC,EAAcC,QAAG,SAAiBC,GAG/C,IAFA,ICRoCC,EDQhCC,EAAU,GAELC,EAAI,EAAGC,EAAMJ,EAAKK,OAAQF,EAAIC,EAAKD,IAAK,CAChD,IAAIG,EAAMN,EAAKG,ICXoBF,EDapBK,ICZW,iBAARL,IAIZA,aAAeP,OAASA,MAAMa,QAAQN,IAC3CA,EAAII,QAAU,IAAMJ,EAAIO,kBAAkBC,UACzCC,OAAOC,yBAAyBV,EAAMA,EAAII,OAAS,IAAgC,WAAzBJ,EAAIW,YAAYC,ODQ3EX,EAAUT,EAAOqB,KAAKZ,EAASN,EAAMkB,KAAKR,IAE1CJ,EAAQa,KAAKT,EAEd,CAED,OAAOJ,CACR,EAEAL,EAAQmB,KAAO,SAAUC,GACxB,OAAO,WACN,OAAOA,EAAGpB,EAAQqB,WACpB,CACA,sBEzBA,IAAIC,EAAiBT,OAAOS,eAExBC,EAAeV,OAAOW,OAAO,MAGjC,IAAK,IAAIR,KAAQS,EACZH,EAAeL,KAAKQ,EAAYT,KACnCO,EAAaE,EAAWT,IAASA,GAInC,IAAIU,EAAKzB,EAAAC,QAAiB,CACzByB,GAAI,CAAE,EACNC,IAAK,CAAE,GA0NR,SAASC,EAAMC,EAAKC,EAAKC,GACxB,OAAOC,KAAKF,IAAIE,KAAKD,IAAID,EAAKD,GAAME,EACrC,CAEA,SAASE,EAAUJ,GAClB,IAAIK,EAAMF,KAAKG,MAAMN,GAAKO,SAAS,IAAIC,cACvC,OAAQH,EAAI3B,OAAS,EAAK,IAAM2B,EAAMA,CACvC,CA9NAT,EAAGE,IAAM,SAAUW,GAClB,IACIC,EACAC,EACJ,OAHaF,EAAOG,UAAU,EAAG,GAAGC,eAInC,IAAK,MACJH,EAAMd,EAAGE,IAAIgB,IAAIL,GACjBE,EAAQ,MACR,MACD,IAAK,MACJD,EAAMd,EAAGE,IAAIiB,IAAIN,GACjBE,EAAQ,MACR,MACD,QACCD,EAAMd,EAAGE,IAAIkB,IAAIP,GACjBE,EAAQ,MAIV,OAAKD,EAIE,CAACC,MAAOA,EAAOM,MAAOP,GAHrB,IAIT,EAEAd,EAAGE,IAAIkB,IAAM,SAAUP,GACtB,IAAKA,EACJ,OAAO,KAGR,IAOIS,EACA1C,EACA2C,EAHAH,EAAM,CAAC,EAAG,EAAG,EAAG,GAKpB,GAAIE,EAAQT,EAAOS,MAVT,mCAUqB,CAI9B,IAHAC,EAAWD,EAAM,GACjBA,EAAQA,EAAM,GAET1C,EAAI,EAAGA,EAAI,EAAGA,IAAK,CAEvB,IAAI4C,EAAS,EAAJ5C,EACTwC,EAAIxC,GAAK6C,SAASH,EAAMjD,MAAMmD,EAAIA,EAAK,GAAI,GAC3C,CAEGD,IACHH,EAAI,GAAKK,SAASF,EAAU,IAAM,IAEnC,MAAM,GAAID,EAAQT,EAAOS,MAxBf,uBAwB4B,CAItC,IAFAC,GADAD,EAAQA,EAAM,IACG,GAEZ1C,EAAI,EAAGA,EAAI,EAAGA,IAClBwC,EAAIxC,GAAK6C,SAASH,EAAM1C,GAAK0C,EAAM1C,GAAI,IAGpC2C,IACHH,EAAI,GAAKK,SAASF,EAAWA,EAAU,IAAM,IAE9C,MAAM,GAAID,EAAQT,EAAOS,MAjCf,gIAiC4B,CACtC,IAAK1C,EAAI,EAAGA,EAAI,EAAGA,IAClBwC,EAAIxC,GAAK6C,SAASH,EAAM1C,EAAI,GAAI,GAG7B0C,EAAM,KACLA,EAAM,GACTF,EAAI,GAA4B,IAAvBM,WAAWJ,EAAM,IAE1BF,EAAI,GAAKM,WAAWJ,EAAM,IAG5B,KAAM,MAAIA,EAAQT,EAAOS,MA5ChB,yHAwDH,OAAIA,EAAQT,EAAOS,MAvDZ,YAwDI,gBAAbA,EAAM,GACF,CAAC,EAAG,EAAG,EAAG,GAGb1B,EAAeL,KAAKQ,EAAYuB,EAAM,MAI3CF,EAAMrB,EAAWuB,EAAM,KACnB,GAAK,EAEFF,GANC,KAQD,KAzBP,IAAKxC,EAAI,EAAGA,EAAI,EAAGA,IAClBwC,EAAIxC,GAAK2B,KAAKG,MAAiC,KAA3BgB,WAAWJ,EAAM1C,EAAI,KAGtC0C,EAAM,KACLA,EAAM,GACTF,EAAI,GAA4B,IAAvBM,WAAWJ,EAAM,IAE1BF,EAAI,GAAKM,WAAWJ,EAAM,IAkB5B,CAED,IAAK1C,EAAI,EAAGA,EAAI,EAAGA,IAClBwC,EAAIxC,GAAKuB,EAAMiB,EAAIxC,GAAI,EAAG,KAI3B,OAFAwC,EAAI,GAAKjB,EAAMiB,EAAI,GAAI,EAAG,GAEnBA,CACR,EAEApB,EAAGE,IAAIgB,IAAM,SAAUL,GACtB,IAAKA,EACJ,OAAO,KAGR,IACIS,EAAQT,EAAOS,MADT,gLAGV,GAAIA,EAAO,CACV,IAAIK,EAAQD,WAAWJ,EAAM,IAM7B,MAAO,EALGI,WAAWJ,EAAM,IAAM,IAAO,KAAO,IACvCnB,EAAMuB,WAAWJ,EAAM,IAAK,EAAG,KAC/BnB,EAAMuB,WAAWJ,EAAM,IAAK,EAAG,KAC/BnB,EAAMyB,MAAMD,GAAS,EAAIA,EAAO,EAAG,GAG3C,CAED,OAAO,IACR,EAEA3B,EAAGE,IAAIiB,IAAM,SAAUN,GACtB,IAAKA,EACJ,OAAO,KAGR,IACIS,EAAQT,EAAOS,MADT,uKAGV,GAAIA,EAAO,CACV,IAAIK,EAAQD,WAAWJ,EAAM,IAK7B,MAAO,EAJGI,WAAWJ,EAAM,IAAM,IAAO,KAAO,IACvCnB,EAAMuB,WAAWJ,EAAM,IAAK,EAAG,KAC/BnB,EAAMuB,WAAWJ,EAAM,IAAK,EAAG,KAC/BnB,EAAMyB,MAAMD,GAAS,EAAIA,EAAO,EAAG,GAE3C,CAED,OAAO,IACR,EAEA3B,EAAGC,GAAG4B,IAAM,WACX,IAAIC,EAAOxD,EAAQqB,WAEnB,MACC,IACAa,EAAUsB,EAAK,IACftB,EAAUsB,EAAK,IACftB,EAAUsB,EAAK,KACdA,EAAK,GAAK,EACPtB,EAAUD,KAAKG,MAAgB,IAAVoB,EAAK,KAC3B,GAEL,EAEA9B,EAAGC,GAAGmB,IAAM,WACX,IAAIU,EAAOxD,EAAQqB,WAEnB,OAAOmC,EAAKhD,OAAS,GAAiB,IAAZgD,EAAK,GAC5B,OAASvB,KAAKG,MAAMoB,EAAK,IAAM,KAAOvB,KAAKG,MAAMoB,EAAK,IAAM,KAAOvB,KAAKG,MAAMoB,EAAK,IAAM,IACzF,QAAUvB,KAAKG,MAAMoB,EAAK,IAAM,KAAOvB,KAAKG,MAAMoB,EAAK,IAAM,KAAOvB,KAAKG,MAAMoB,EAAK,IAAM,KAAOA,EAAK,GAAK,GAC/G,EAEA9B,EAAGC,GAAGmB,IAAIW,QAAU,WACnB,IAAID,EAAOxD,EAAQqB,WAEfqC,EAAIzB,KAAKG,MAAMoB,EAAK,GAAK,IAAM,KAC/BG,EAAI1B,KAAKG,MAAMoB,EAAK,GAAK,IAAM,KAC/BI,EAAI3B,KAAKG,MAAMoB,EAAK,GAAK,IAAM,KAEnC,OAAOA,EAAKhD,OAAS,GAAiB,IAAZgD,EAAK,GAC5B,OAASE,EAAI,MAAQC,EAAI,MAAQC,EAAI,KACrC,QAAUF,EAAI,MAAQC,EAAI,MAAQC,EAAI,MAAQJ,EAAK,GAAK,GAC5D,EAEA9B,EAAGC,GAAGiB,IAAM,WACX,IAAIiB,EAAO7D,EAAQqB,WACnB,OAAOwC,EAAKrD,OAAS,GAAiB,IAAZqD,EAAK,GAC5B,OAASA,EAAK,GAAK,KAAOA,EAAK,GAAK,MAAQA,EAAK,GAAK,KACtD,QAAUA,EAAK,GAAK,KAAOA,EAAK,GAAK,MAAQA,EAAK,GAAK,MAAQA,EAAK,GAAK,GAC7E,EAIAnC,EAAGC,GAAGkB,IAAM,WACX,IAAIiB,EAAO9D,EAAQqB,WAEf0C,EAAI,GAKR,OAJID,EAAKtD,QAAU,GAAiB,IAAZsD,EAAK,KAC5BC,EAAI,KAAOD,EAAK,IAGV,OAASA,EAAK,GAAK,KAAOA,EAAK,GAAK,MAAQA,EAAK,GAAK,IAAMC,EAAI,GACxE,EAEArC,EAAGC,GAAGqC,QAAU,SAAUlB,GACzB,OAAOvB,EAAauB,EAAI/C,MAAM,EAAG,GAClC,gBCrOA,IAAAxJ,EAAiB,CAChBC,UAAa,CAAC,IAAK,IAAK,KACxBC,aAAgB,CAAC,IAAK,IAAK,KAC3BC,KAAQ,CAAC,EAAG,IAAK,KACjBC,WAAc,CAAC,IAAK,IAAK,KACzBC,MAAS,CAAC,IAAK,IAAK,KACpBC,MAAS,CAAC,IAAK,IAAK,KACpBC,OAAU,CAAC,IAAK,IAAK,KACrBC,MAAS,CAAC,EAAG,EAAG,GAChBC,eAAkB,CAAC,IAAK,IAAK,KAC7BC,KAAQ,CAAC,EAAG,EAAG,KACfC,WAAc,CAAC,IAAK,GAAI,KACxBC,MAAS,CAAC,IAAK,GAAI,IACnBC,UAAa,CAAC,IAAK,IAAK,KACxBC,UAAa,CAAC,GAAI,IAAK,KACvBC,WAAc,CAAC,IAAK,IAAK,GACzBC,UAAa,CAAC,IAAK,IAAK,IACxBC,MAAS,CAAC,IAAK,IAAK,IACpBC,eAAkB,CAAC,IAAK,IAAK,KAC7BC,SAAY,CAAC,IAAK,IAAK,KACvBC,QAAW,CAAC,IAAK,GAAI,IACrBC,KAAQ,CAAC,EAAG,IAAK,KACjBC,SAAY,CAAC,EAAG,EAAG,KACnBC,SAAY,CAAC,EAAG,IAAK,KACrBC,cAAiB,CAAC,IAAK,IAAK,IAC5BC,SAAY,CAAC,IAAK,IAAK,KACvBC,UAAa,CAAC,EAAG,IAAK,GACtBC,SAAY,CAAC,IAAK,IAAK,KACvBC,UAAa,CAAC,IAAK,IAAK,KACxBC,YAAe,CAAC,IAAK,EAAG,KACxBC,eAAkB,CAAC,GAAI,IAAK,IAC5BC,WAAc,CAAC,IAAK,IAAK,GACzBC,WAAc,CAAC,IAAK,GAAI,KACxBC,QAAW,CAAC,IAAK,EAAG,GACpBC,WAAc,CAAC,IAAK,IAAK,KACzBC,aAAgB,CAAC,IAAK,IAAK,KAC3BC,cAAiB,CAAC,GAAI,GAAI,KAC1BC,cAAiB,CAAC,GAAI,GAAI,IAC1BC,cAAiB,CAAC,GAAI,GAAI,IAC1BC,cAAiB,CAAC,EAAG,IAAK,KAC1BC,WAAc,CAAC,IAAK,EAAG,KACvBC,SAAY,CAAC,IAAK,GAAI,KACtBC,YAAe,CAAC,EAAG,IAAK,KACxBC,QAAW,CAAC,IAAK,IAAK,KACtBC,QAAW,CAAC,IAAK,IAAK,KACtBC,WAAc,CAAC,GAAI,IAAK,KACxBC,UAAa,CAAC,IAAK,GAAI,IACvBC,YAAe,CAAC,IAAK,IAAK,KAC1BC,YAAe,CAAC,GAAI,IAAK,IACzBC,QAAW,CAAC,IAAK,EAAG,KACpBC,UAAa,CAAC,IAAK,IAAK,KACxBC,WAAc,CAAC,IAAK,IAAK,KACzBC,KAAQ,CAAC,IAAK,IAAK,GACnBC,UAAa,CAAC,IAAK,IAAK,IACxBC,KAAQ,CAAC,IAAK,IAAK,KACnBC,MAAS,CAAC,EAAG,IAAK,GAClBC,YAAe,CAAC,IAAK,IAAK,IAC1BC,KAAQ,CAAC,IAAK,IAAK,KACnBC,SAAY,CAAC,IAAK,IAAK,KACvBC,QAAW,CAAC,IAAK,IAAK,KACtBC,UAAa,CAAC,IAAK,GAAI,IACvBC,OAAU,CAAC,GAAI,EAAG,KAClBC,MAAS,CAAC,IAAK,IAAK,KACpBC,MAAS,CAAC,IAAK,IAAK,KACpBC,SAAY,CAAC,IAAK,IAAK,KACvBC,cAAiB,CAAC,IAAK,IAAK,KAC5BC,UAAa,CAAC,IAAK,IAAK,GACxBC,aAAgB,CAAC,IAAK,IAAK,KAC3BC,UAAa,CAAC,IAAK,IAAK,KACxBC,WAAc,CAAC,IAAK,IAAK,KACzBC,UAAa,CAAC,IAAK,IAAK,KACxBC,qBAAwB,CAAC,IAAK,IAAK,KACnCC,UAAa,CAAC,IAAK,IAAK,KACxBC,WAAc,CAAC,IAAK,IAAK,KACzBC,UAAa,CAAC,IAAK,IAAK,KACxBC,UAAa,CAAC,IAAK,IAAK,KACxBC,YAAe,CAAC,IAAK,IAAK,KAC1BC,cAAiB,CAAC,GAAI,IAAK,KAC3BC,aAAgB,CAAC,IAAK,IAAK,KAC3BC,eAAkB,CAAC,IAAK,IAAK,KAC7BC,eAAkB,CAAC,IAAK,IAAK,KAC7BC,eAAkB,CAAC,IAAK,IAAK,KAC7BC,YAAe,CAAC,IAAK,IAAK,KAC1BC,KAAQ,CAAC,EAAG,IAAK,GACjBC,UAAa,CAAC,GAAI,IAAK,IACvBC,MAAS,CAAC,IAAK,IAAK,KACpBC,QAAW,CAAC,IAAK,EAAG,KACpBC,OAAU,CAAC,IAAK,EAAG,GACnBC,iBAAoB,CAAC,IAAK,IAAK,KAC/BC,WAAc,CAAC,EAAG,EAAG,KACrBC,aAAgB,CAAC,IAAK,GAAI,KAC1BC,aAAgB,CAAC,IAAK,IAAK,KAC3BC,eAAkB,CAAC,GAAI,IAAK,KAC5BC,gBAAmB,CAAC,IAAK,IAAK,KAC9BC,kBAAqB,CAAC,EAAG,IAAK,KAC9BC,gBAAmB,CAAC,GAAI,IAAK,KAC7BC,gBAAmB,CAAC,IAAK,GAAI,KAC7BC,aAAgB,CAAC,GAAI,GAAI,KACzBC,UAAa,CAAC,IAAK,IAAK,KACxBC,UAAa,CAAC,IAAK,IAAK,KACxBC,SAAY,CAAC,IAAK,IAAK,KACvBC,YAAe,CAAC,IAAK,IAAK,KAC1BC,KAAQ,CAAC,EAAG,EAAG,KACfC,QAAW,CAAC,IAAK,IAAK,KACtBC,MAAS,CAAC,IAAK,IAAK,GACpBC,UAAa,CAAC,IAAK,IAAK,IACxBC,OAAU,CAAC,IAAK,IAAK,GACrBC,UAAa,CAAC,IAAK,GAAI,GACvBC,OAAU,CAAC,IAAK,IAAK,KACrBC,cAAiB,CAAC,IAAK,IAAK,KAC5BC,UAAa,CAAC,IAAK,IAAK,KACxBC,cAAiB,CAAC,IAAK,IAAK,KAC5BC,cAAiB,CAAC,IAAK,IAAK,KAC5BC,WAAc,CAAC,IAAK,IAAK,KACzBC,UAAa,CAAC,IAAK,IAAK,KACxBC,KAAQ,CAAC,IAAK,IAAK,IACnBC,KAAQ,CAAC,IAAK,IAAK,KACnBC,KAAQ,CAAC,IAAK,IAAK,KACnBC,WAAc,CAAC,IAAK,IAAK,KACzBC,OAAU,CAAC,IAAK,EAAG,KACnBC,cAAiB,CAAC,IAAK,GAAI,KAC3BC,IAAO,CAAC,IAAK,EAAG,GAChBC,UAAa,CAAC,IAAK,IAAK,KACxBC,UAAa,CAAC,GAAI,IAAK,KACvBC,YAAe,CAAC,IAAK,GAAI,IACzBC,OAAU,CAAC,IAAK,IAAK,KACrBC,WAAc,CAAC,IAAK,IAAK,IACzBC,SAAY,CAAC,GAAI,IAAK,IACtBC,SAAY,CAAC,IAAK,IAAK,KACvBC,OAAU,CAAC,IAAK,GAAI,IACpBC,OAAU,CAAC,IAAK,IAAK,KACrBC,QAAW,CAAC,IAAK,IAAK,KACtBC,UAAa,CAAC,IAAK,GAAI,KACvBC,UAAa,CAAC,IAAK,IAAK,KACxBC,UAAa,CAAC,IAAK,IAAK,KACxBC,KAAQ,CAAC,IAAK,IAAK,KACnBC,YAAe,CAAC,EAAG,IAAK,KACxBC,UAAa,CAAC,GAAI,IAAK,KACvBC,IAAO,CAAC,IAAK,IAAK,KAClBC,KAAQ,CAAC,EAAG,IAAK,KACjBC,QAAW,CAAC,IAAK,IAAK,KACtBC,OAAU,CAAC,IAAK,GAAI,IACpBC,UAAa,CAAC,GAAI,IAAK,KACvBC,OAAU,CAAC,IAAK,IAAK,KACrBC,MAAS,CAAC,IAAK,IAAK,KACpBC,MAAS,CAAC,IAAK,IAAK,KACpBC,WAAc,CAAC,IAAK,IAAK,KACzBC,OAAU,CAAC,IAAK,IAAK,GACrBC,YAAe,CAAC,IAAK,IAAK,sBC/I3B,IAAIsE,EAAkB,CAAA,EACtB,IAAK,IAAIC,KAAOC,EACXA,EAAY7C,eAAe4C,KAC9BD,EAAgBE,EAAYD,IAAQA,GAItC,IAAIE,EAAUnE,EAAAC,QAAiB,CAC9B4C,IAAK,CAACuB,SAAU,EAAGC,OAAQ,OAC3B1B,IAAK,CAACyB,SAAU,EAAGC,OAAQ,OAC3BC,IAAK,CAACF,SAAU,EAAGC,OAAQ,OAC3BzB,IAAK,CAACwB,SAAU,EAAGC,OAAQ,OAC3BE,KAAM,CAACH,SAAU,EAAGC,OAAQ,QAC5BG,IAAK,CAACJ,SAAU,EAAGC,OAAQ,OAC3BI,IAAK,CAACL,SAAU,EAAGC,OAAQ,OAC3BK,IAAK,CAACN,SAAU,EAAGC,OAAQ,OAC3Bf,IAAK,CAACc,SAAU,EAAGC,OAAQ,CAAC,QAC5BN,QAAS,CAACK,SAAU,EAAGC,OAAQ,CAAC,YAChCM,OAAQ,CAACP,SAAU,EAAGC,OAAQ,CAAC,WAC/BO,QAAS,CAACR,SAAU,EAAGC,OAAQ,CAAC,YAChCQ,IAAK,CAACT,SAAU,EAAGC,OAAQ,CAAC,IAAK,IAAK,MACtCS,MAAO,CAACV,SAAU,EAAGC,OAAQ,CAAC,MAAO,MAAO,QAC5CzK,KAAM,CAACwK,SAAU,EAAGC,OAAQ,CAAC,UAI9B,IAAK,IAAI7B,KAAS2B,EACjB,GAAIA,EAAQ9C,eAAemB,GAAQ,CAClC,KAAM,aAAc2B,EAAQ3B,IAC3B,MAAM,IAAIuC,MAAM,8BAAgCvC,GAGjD,KAAM,WAAY2B,EAAQ3B,IACzB,MAAM,IAAIuC,MAAM,oCAAsCvC,GAGvD,GAAI2B,EAAQ3B,GAAO6B,OAAO9D,SAAW4D,EAAQ3B,GAAO4B,SACnD,MAAM,IAAIW,MAAM,sCAAwCvC,GAGzD,IAAI4B,EAAWD,EAAQ3B,GAAO4B,SAC1BC,EAASF,EAAQ3B,GAAO6B,cACrBF,EAAQ3B,GAAO4B,gBACfD,EAAQ3B,GAAO6B,OACtBzD,OAAOoE,eAAeb,EAAQ3B,GAAQ,WAAY,CAACM,MAAOsB,IAC1DxD,OAAOoE,eAAeb,EAAQ3B,GAAQ,SAAU,CAACM,MAAOuB,GACxD,CAGFF,EAAQtB,IAAIF,IAAM,SAAUE,GAC3B,IAMIoC,EAEAC,EARAzB,EAAIZ,EAAI,GAAK,IACba,EAAIb,EAAI,GAAK,IACbc,EAAId,EAAI,GAAK,IACbf,EAAME,KAAKF,IAAI2B,EAAGC,EAAGC,GACrB5B,EAAMC,KAAKD,IAAI0B,EAAGC,EAAGC,GACrBwB,EAAQpD,EAAMD,EA+BlB,OA1BIC,IAAQD,EACXmD,EAAI,EACMxB,IAAM1B,EAChBkD,GAAKvB,EAAIC,GAAKwB,EACJzB,IAAM3B,EAChBkD,EAAI,GAAKtB,EAAIF,GAAK0B,EACRxB,IAAM5B,IAChBkD,EAAI,GAAKxB,EAAIC,GAAKyB,IAGnBF,EAAIjD,KAAKF,IAAQ,GAAJmD,EAAQ,MAEb,IACPA,GAAK,KAGNC,GAAKpD,EAAMC,GAAO,EAUX,CAACkD,EAAO,KARXlD,IAAQD,EACP,EACMoD,GAAK,GACXC,GAASpD,EAAMD,GAEfqD,GAAS,EAAIpD,EAAMD,IAGA,IAAJoD,EACrB,EAEAf,EAAQtB,IAAIyB,IAAM,SAAUzB,GAC3B,IAAIuC,EACAC,EACAC,EACAL,EACAM,EAEA9B,EAAIZ,EAAI,GAAK,IACba,EAAIb,EAAI,GAAK,IACbc,EAAId,EAAI,GAAK,IACb2C,EAAIxD,KAAKD,IAAI0B,EAAGC,EAAGC,GACnB8B,EAAOD,EAAIxD,KAAKF,IAAI2B,EAAGC,EAAGC,GAC1B+B,EAAQ,SAAUC,GACrB,OAAQH,EAAIG,GAAK,EAAIF,EAAO,EAC9B,EAwBC,OAtBa,IAATA,EACHR,EAAIM,EAAI,GAERA,EAAIE,EAAOD,EACXJ,EAAOM,EAAMjC,GACb4B,EAAOK,EAAMhC,GACb4B,EAAOI,EAAM/B,GAETF,IAAM+B,EACTP,EAAIK,EAAOD,EACD3B,IAAM8B,EAChBP,EAAK,EAAI,EAAKG,EAAOE,EACX3B,IAAM6B,IAChBP,EAAK,EAAI,EAAKI,EAAOD,GAElBH,EAAI,EACPA,GAAK,EACKA,EAAI,IACdA,GAAK,IAIA,CACF,IAAJA,EACI,IAAJM,EACI,IAAJC,EAEF,EAEArB,EAAQtB,IAAID,IAAM,SAAUC,GAC3B,IAAIY,EAAIZ,EAAI,GACRa,EAAIb,EAAI,GACRc,EAAId,EAAI,GAMZ,MAAO,CALCsB,EAAQtB,IAAIF,IAAIE,GAAK,GAKd,KAJP,EAAI,IAAMb,KAAKF,IAAI2B,EAAGzB,KAAKF,IAAI4B,EAAGC,KAIlB,KAFxBA,EAAI,EAAI,EAAI,IAAM3B,KAAKD,IAAI0B,EAAGzB,KAAKD,IAAI2B,EAAGC,KAG3C,EAEAQ,EAAQtB,IAAI0B,KAAO,SAAU1B,GAC5B,IAMI+C,EANAnC,EAAIZ,EAAI,GAAK,IACba,EAAIb,EAAI,GAAK,IACbc,EAAId,EAAI,GAAK,IAWjB,MAAO,CAAK,MAJP,EAAIY,GADTmC,EAAI5D,KAAKF,IAAI,EAAI2B,EAAG,EAAIC,EAAG,EAAIC,MACZ,EAAIiC,IAAM,GAIR,MAHhB,EAAIlC,EAAIkC,IAAM,EAAIA,IAAM,GAGC,MAFzB,EAAIjC,EAAIiC,IAAM,EAAIA,IAAM,GAEU,IAAJA,EACpC,EAaAzB,EAAQtB,IAAIkB,QAAU,SAAUlB,GAC/B,IAAIgD,EAAW7B,EAAgBnB,GAC/B,GAAIgD,EACH,OAAOA,EAGR,IACIC,EAfwBC,EAAGC,EAc3BC,EAAyBC,IAG7B,IAAK,IAAInC,KAAWG,EACnB,GAAIA,EAAY7C,eAAe0C,GAAU,CACxC,IAAIjB,EAAQoB,EAAYH,GAGpBoC,GAtBsBJ,EAsBSlD,EAtBNmD,EAsBWlD,EApBzCd,KAAKoE,IAAIL,EAAE,GAAKC,EAAE,GAAI,GACtBhE,KAAKoE,IAAIL,EAAE,GAAKC,EAAE,GAAI,GACtBhE,KAAKoE,IAAIL,EAAE,GAAKC,EAAE,GAAI,IAqBjBG,EAAWF,IACdA,EAAyBE,EACzBL,EAAwB/B,EAEzB,CAGF,OAAO+B,CACR,EAEA3B,EAAQJ,QAAQlB,IAAM,SAAUkB,GAC/B,OAAOG,EAAYH,EACpB,EAEAI,EAAQtB,IAAI2B,IAAM,SAAU3B,GAC3B,IAAIY,EAAIZ,EAAI,GAAK,IACba,EAAIb,EAAI,GAAK,IACbc,EAAId,EAAI,GAAK,IAWjB,MAAO,CAAK,KAJC,OAJbY,EAAIA,EAAI,OAAUzB,KAAKoE,KAAM3C,EAAI,MAAS,MAAQ,KAAQA,EAAI,OAIlC,OAH5BC,EAAIA,EAAI,OAAU1B,KAAKoE,KAAM1C,EAAI,MAAS,MAAQ,KAAQA,EAAI,OAGnB,OAF3CC,EAAIA,EAAI,OAAU3B,KAAKoE,KAAMzC,EAAI,MAAS,MAAQ,KAAQA,EAAI,QAMzC,KAHR,MAAJF,EAAmB,MAAJC,EAAmB,MAAJC,GAGT,KAFjB,MAAJF,EAAmB,MAAJC,EAAmB,MAAJC,GAGxC,EAEAQ,EAAQtB,IAAI4B,IAAM,SAAU5B,GAC3B,IAAI2B,EAAML,EAAQtB,IAAI2B,IAAI3B,GACtBkD,EAAIvB,EAAI,GACRwB,EAAIxB,EAAI,GACR6B,EAAI7B,EAAI,GAiBZ,OAXAwB,GAAK,IACLK,GAAK,QAELN,GAJAA,GAAK,QAIG,QAAW/D,KAAKoE,IAAIL,EAAG,EAAI,GAAM,MAAQA,EAAM,GAAK,IAQrD,CAJF,KAHLC,EAAIA,EAAI,QAAWhE,KAAKoE,IAAIJ,EAAG,EAAI,GAAM,MAAQA,EAAM,GAAK,KAG5C,GACZ,KAAOD,EAAIC,GACX,KAAOA,GAJXK,EAAIA,EAAI,QAAWrE,KAAKoE,IAAIC,EAAG,EAAI,GAAM,MAAQA,EAAM,GAAK,MAO7D,EAEAlC,EAAQxB,IAAIE,IAAM,SAAUF,GAC3B,IAGI2D,EACAC,EACAC,EACA3D,EACAN,EAPA0C,EAAItC,EAAI,GAAK,IACb4C,EAAI5C,EAAI,GAAK,IACbuC,EAAIvC,EAAI,GAAK,IAOjB,GAAU,IAAN4C,EAEH,MAAO,CADPhD,EAAU,IAAJ2C,EACO3C,EAAKA,GASnB+D,EAAK,EAAIpB,GALRqB,EADGrB,EAAI,GACFA,GAAK,EAAIK,GAETL,EAAIK,EAAIL,EAAIK,GAKlB1C,EAAM,CAAC,EAAG,EAAG,GACb,IAAK,IAAIxC,EAAI,EAAGA,EAAI,EAAGA,KACtBmG,EAAKvB,EAAI,EAAI,IAAM5E,EAAI,IACd,GACRmG,IAEGA,EAAK,GACRA,IAIAjE,EADG,EAAIiE,EAAK,EACNF,EAAiB,GAAXC,EAAKD,GAAUE,EACjB,EAAIA,EAAK,EACbD,EACI,EAAIC,EAAK,EACbF,GAAMC,EAAKD,IAAO,EAAI,EAAIE,GAAM,EAEhCF,EAGPzD,EAAIxC,GAAW,IAANkC,EAGV,OAAOM,CACR,EAEAsB,EAAQxB,IAAI2B,IAAM,SAAU3B,GAC3B,IAAIsC,EAAItC,EAAI,GACR4C,EAAI5C,EAAI,GAAK,IACbuC,EAAIvC,EAAI,GAAK,IACb8D,EAAOlB,EACPmB,EAAO1E,KAAKD,IAAImD,EAAG,KAUvB,OALAK,IADAL,GAAK,IACM,EAAKA,EAAI,EAAIA,EACxBuB,GAAQC,GAAQ,EAAIA,EAAO,EAAIA,EAIxB,CAACzB,EAAQ,KAFL,IAANC,EAAW,EAAIuB,GAASC,EAAOD,GAAS,EAAIlB,GAAML,EAAIK,IAElC,MAHpBL,EAAIK,GAAK,GAIf,EAEApB,EAAQG,IAAIzB,IAAM,SAAUyB,GAC3B,IAAIW,EAAIX,EAAI,GAAK,GACbiB,EAAIjB,EAAI,GAAK,IACbkB,EAAIlB,EAAI,GAAK,IACbqC,EAAK3E,KAAK4E,MAAM3B,GAAK,EAErB4B,EAAI5B,EAAIjD,KAAK4E,MAAM3B,GACnB6B,EAAI,IAAMtB,GAAK,EAAID,GACnBwB,EAAI,IAAMvB,GAAK,EAAKD,EAAIsB,GACxBG,EAAI,IAAMxB,GAAK,EAAKD,GAAK,EAAIsB,IAGjC,OAFArB,GAAK,IAEGmB,GACP,KAAK,EACJ,MAAO,CAACnB,EAAGwB,EAAGF,GACf,KAAK,EACJ,MAAO,CAACC,EAAGvB,EAAGsB,GACf,KAAK,EACJ,MAAO,CAACA,EAAGtB,EAAGwB,GACf,KAAK,EACJ,MAAO,CAACF,EAAGC,EAAGvB,GACf,KAAK,EACJ,MAAO,CAACwB,EAAGF,EAAGtB,GACf,KAAK,EACJ,MAAO,CAACA,EAAGsB,EAAGC,GAEjB,EAEA5C,EAAQG,IAAI3B,IAAM,SAAU2B,GAC3B,IAIIoC,EACAO,EACA/B,EANAD,EAAIX,EAAI,GACRiB,EAAIjB,EAAI,GAAK,IACbkB,EAAIlB,EAAI,GAAK,IACb4C,EAAOlF,KAAKD,IAAIyD,EAAG,KAYvB,OAPAN,GAAK,EAAIK,GAAKC,EAEdyB,EAAK1B,EAAI2B,EAKF,CAACjC,EAAQ,KAHhBgC,GADAA,IAFAP,GAAQ,EAAInB,GAAK2B,IAEF,EAAKR,EAAO,EAAIA,IACpB,GAGc,KAFzBxB,GAAK,GAGN,EAGAf,EAAQvB,IAAIC,IAAM,SAAUD,GAC3B,IAIIvC,EACAmF,EACAqB,EACAM,EAkBA1D,EACAC,EACAC,EA3BAsB,EAAIrC,EAAI,GAAK,IACbwE,EAAKxE,EAAI,GAAK,IACdyE,EAAKzE,EAAI,GAAK,IACd0E,EAAQF,EAAKC,EAyBjB,OAlBIC,EAAQ,IACXF,GAAME,EACND,GAAMC,GAKPT,EAAI,EAAI5B,GAFR5E,EAAI2B,KAAK4E,MAAM,EAAI3B,IAIA,IAAV,EAAJ5E,KACJwG,EAAI,EAAIA,GAGTM,EAAIC,EAAKP,IAPTrB,EAAI,EAAI6B,GAOUD,GAKV/G,GACP,QACA,KAAK,EACL,KAAK,EAAGoD,EAAI+B,EAAG9B,EAAIyD,EAAGxD,EAAIyD,EAAI,MAC9B,KAAK,EAAG3D,EAAI0D,EAAGzD,EAAI8B,EAAG7B,EAAIyD,EAAI,MAC9B,KAAK,EAAG3D,EAAI2D,EAAI1D,EAAI8B,EAAG7B,EAAIwD,EAAG,MAC9B,KAAK,EAAG1D,EAAI2D,EAAI1D,EAAIyD,EAAGxD,EAAI6B,EAAG,MAC9B,KAAK,EAAG/B,EAAI0D,EAAGzD,EAAI0D,EAAIzD,EAAI6B,EAAG,MAC9B,KAAK,EAAG/B,EAAI+B,EAAG9B,EAAI0D,EAAIzD,EAAIwD,EAG5B,MAAO,CAAK,IAAJ1D,EAAa,IAAJC,EAAa,IAAJC,EAC3B,EAEAQ,EAAQI,KAAK1B,IAAM,SAAU0B,GAC5B,IAAIoB,EAAIpB,EAAK,GAAK,IACdgD,EAAIhD,EAAK,GAAK,IACdyB,EAAIzB,EAAK,GAAK,IACdqB,EAAIrB,EAAK,GAAK,IASlB,MAAO,CAAK,KAJR,EAAIvC,KAAKF,IAAI,EAAG6D,GAAK,EAAIC,GAAKA,IAIb,KAHjB,EAAI5D,KAAKF,IAAI,EAAGyF,GAAK,EAAI3B,GAAKA,IAGJ,KAF1B,EAAI5D,KAAKF,IAAI,EAAGkE,GAAK,EAAIJ,GAAKA,IAGnC,EAEAzB,EAAQK,IAAI3B,IAAM,SAAU2B,GAC3B,IAGIf,EACAC,EACAC,EALAoC,EAAIvB,EAAI,GAAK,IACbwB,EAAIxB,EAAI,GAAK,IACb6B,EAAI7B,EAAI,GAAK,IA0BjB,OApBAd,GAAU,MAALqC,EAAoB,OAAJC,EAAmB,MAAJK,EACpC1C,EAAS,MAAJoC,GAAoB,KAALC,EAAoB,MAAJK,EAGpC5C,GALAA,EAAS,OAAJsC,GAAoB,OAALC,GAAqB,MAALK,GAK5B,SACH,MAAQrE,KAAKoE,IAAI3C,EAAG,EAAM,KAAQ,KAChC,MAAJA,EAEHC,EAAIA,EAAI,SACH,MAAQ1B,KAAKoE,IAAI1C,EAAG,EAAM,KAAQ,KAChC,MAAJA,EAEHC,EAAIA,EAAI,SACH,MAAQ3B,KAAKoE,IAAIzC,EAAG,EAAM,KAAQ,KAChC,MAAJA,EAMI,CAAK,KAJZF,EAAIzB,KAAKF,IAAIE,KAAKD,IAAI,EAAG0B,GAAI,IAIR,KAHrBC,EAAI1B,KAAKF,IAAIE,KAAKD,IAAI,EAAG2B,GAAI,IAGC,KAF9BC,EAAI3B,KAAKF,IAAIE,KAAKD,IAAI,EAAG4B,GAAI,IAG9B,EAEAQ,EAAQK,IAAIC,IAAM,SAAUD,GAC3B,IAAIuB,EAAIvB,EAAI,GACRwB,EAAIxB,EAAI,GACR6B,EAAI7B,EAAI,GAiBZ,OAXAwB,GAAK,IACLK,GAAK,QAELN,GAJAA,GAAK,QAIG,QAAW/D,KAAKoE,IAAIL,EAAG,EAAI,GAAM,MAAQA,EAAM,GAAK,IAQrD,CAJF,KAHLC,EAAIA,EAAI,QAAWhE,KAAKoE,IAAIJ,EAAG,EAAI,GAAM,MAAQA,EAAM,GAAK,KAG5C,GACZ,KAAOD,EAAIC,GACX,KAAOA,GAJXK,EAAIA,EAAI,QAAWrE,KAAKoE,IAAIC,EAAG,EAAI,GAAM,MAAQA,EAAM,GAAK,MAO7D,EAEAlC,EAAQM,IAAID,IAAM,SAAUC,GAC3B,IAGIsB,EACAC,EACAK,EALAnB,EAAIT,EAAI,GAQZsB,EAPQtB,EAAI,GAOJ,KADRuB,GAAKd,EAAI,IAAM,KAEfmB,EAAIL,EAPIvB,EAAI,GAOA,IAEZ,IAAI+C,EAAKxF,KAAKoE,IAAIJ,EAAG,GACjByB,EAAKzF,KAAKoE,IAAIL,EAAG,GACjB2B,EAAK1F,KAAKoE,IAAIC,EAAG,GASrB,OARAL,EAAIwB,EAAK,QAAWA,GAAMxB,EAAI,GAAK,KAAO,MAC1CD,EAAI0B,EAAK,QAAWA,GAAM1B,EAAI,GAAK,KAAO,MAC1CM,EAAIqB,EAAK,QAAWA,GAAMrB,EAAI,GAAK,KAAO,MAMnC,CAJPN,GAAK,OACLC,GAAK,IACLK,GAAK,QAGN,EAEAlC,EAAQM,IAAIC,IAAM,SAAUD,GAC3B,IAIIQ,EAJAC,EAAIT,EAAI,GACRX,EAAIW,EAAI,GACRd,EAAIc,EAAI,GAcZ,OARAQ,EAAS,IADJjD,KAAK2F,MAAMhE,EAAGG,GACJ,EAAI9B,KAAK4F,IAEhB,IACP3C,GAAK,KAKC,CAACC,EAFJlD,KAAK6F,KAAK/D,EAAIA,EAAIH,EAAIA,GAEZsB,EACf,EAEAd,EAAQO,IAAID,IAAM,SAAUC,GAC3B,IAKIoD,EALA5C,EAAIR,EAAI,GACRiB,EAAIjB,EAAI,GAUZ,OAJAoD,EALQpD,EAAI,GAKH,IAAM,EAAI1C,KAAK4F,GAIjB,CAAC1C,EAHJS,EAAI3D,KAAK+F,IAAID,GACbnC,EAAI3D,KAAKgG,IAAIF,GAGlB,EAEA3D,EAAQtB,IAAI8B,OAAS,SAAUzE,GAC9B,IAAIuD,EAAIvD,EAAK,GACTwD,EAAIxD,EAAK,GACTyD,EAAIzD,EAAK,GACT4C,EAAQ,KAAK1B,UAAYA,UAAU,GAAK+C,EAAQtB,IAAIyB,IAAIpE,GAAM,GAIlE,GAAc,KAFd4C,EAAQd,KAAKG,MAAMW,EAAQ,KAG1B,OAAO,GAGR,IAAImF,EAAO,IACNjG,KAAKG,MAAMwB,EAAI,MAAQ,EACxB3B,KAAKG,MAAMuB,EAAI,MAAQ,EACxB1B,KAAKG,MAAMsB,EAAI,MAMlB,OAJc,IAAVX,IACHmF,GAAQ,IAGFA,CACR,EAEA9D,EAAQG,IAAIK,OAAS,SAAUzE,GAG9B,OAAOiE,EAAQtB,IAAI8B,OAAOR,EAAQG,IAAIzB,IAAI3C,GAAOA,EAAK,GACvD,EAEAiE,EAAQtB,IAAI+B,QAAU,SAAU1E,GAC/B,IAAIuD,EAAIvD,EAAK,GACTwD,EAAIxD,EAAK,GACTyD,EAAIzD,EAAK,GAIb,OAAIuD,IAAMC,GAAKA,IAAMC,EAChBF,EAAI,EACA,GAGJA,EAAI,IACA,IAGDzB,KAAKG,OAAQsB,EAAI,GAAK,IAAO,IAAM,IAGhC,GACP,GAAKzB,KAAKG,MAAMsB,EAAI,IAAM,GAC1B,EAAIzB,KAAKG,MAAMuB,EAAI,IAAM,GAC1B1B,KAAKG,MAAMwB,EAAI,IAAM,EAGzB,EAEAQ,EAAQQ,OAAO9B,IAAM,SAAU3C,GAC9B,IAAIgI,EAAQhI,EAAO,GAGnB,GAAc,IAAVgI,GAAyB,IAAVA,EAOlB,OANIhI,EAAO,KACVgI,GAAS,KAKH,CAFPA,EAAQA,EAAQ,KAAO,IAERA,EAAOA,GAGvB,IAAIC,EAA6B,IAAL,KAAbjI,EAAO,KAKtB,MAAO,EAJW,EAARgI,GAAaC,EAAQ,KACpBD,GAAS,EAAK,GAAKC,EAAQ,KAC3BD,GAAS,EAAK,GAAKC,EAAQ,IAGvC,EAEAhE,EAAQS,QAAQ/B,IAAM,SAAU3C,GAE/B,GAAIA,GAAQ,IAAK,CAChB,IAAIyF,EAAmB,IAAdzF,EAAO,KAAY,EAC5B,MAAO,CAACyF,EAAGA,EAAGA,EACd,CAID,IAAIyC,EAKJ,OAPAlI,GAAQ,GAOD,CAJC8B,KAAK4E,MAAM1G,EAAO,IAAM,EAAI,IAC5B8B,KAAK4E,OAAOwB,EAAMlI,EAAO,IAAM,GAAK,EAAI,IACvCkI,EAAM,EAAK,EAAI,IAGzB,EAEAjE,EAAQtB,IAAIS,IAAM,SAAUpD,GAC3B,IAIIoC,KAJkC,IAAtBN,KAAKG,MAAMjC,EAAK,MAAe,MACpB,IAAtB8B,KAAKG,MAAMjC,EAAK,MAAe,IACV,IAAtB8B,KAAKG,MAAMjC,EAAK,MAECkC,SAAS,IAAIC,cAClC,MAAO,SAASI,UAAUH,EAAO/B,QAAU+B,CAC5C,EAEA6B,EAAQb,IAAIT,IAAM,SAAU3C,GAC3B,IAAI6C,EAAQ7C,EAAKkC,SAAS,IAAIW,MAAM,4BACpC,IAAKA,EACJ,MAAO,CAAC,EAAG,EAAG,GAGf,IAAIsF,EAActF,EAAM,GAEA,IAApBA,EAAM,GAAGxC,SACZ8H,EAAcA,EAAYC,MAAM,IAAIC,KAAI,SAAUC,GACjD,OAAOA,EAAOA,CACjB,IAAKC,KAAK,KAGT,IAAIC,EAAUxF,SAASmF,EAAa,IAKpC,MAAO,CAJEK,GAAW,GAAM,IACjBA,GAAW,EAAK,IACP,IAAVA,EAGT,EAEAvE,EAAQtB,IAAIgC,IAAM,SAAUhC,GAC3B,IAOI8F,EAPAlF,EAAIZ,EAAI,GAAK,IACba,EAAIb,EAAI,GAAK,IACbc,EAAId,EAAI,GAAK,IACbd,EAAMC,KAAKD,IAAIC,KAAKD,IAAI0B,EAAGC,GAAIC,GAC/B7B,EAAME,KAAKF,IAAIE,KAAKF,IAAI2B,EAAGC,GAAIC,GAC/BiF,EAAU7G,EAAMD,EAyBpB,OAdC6G,EADGC,GAAU,EACP,EAEH7G,IAAQ0B,GACHC,EAAIC,GAAKiF,EAAU,EAExB7G,IAAQ2B,EACL,GAAKC,EAAIF,GAAKmF,EAEd,GAAKnF,EAAIC,GAAKkF,EAAS,EAG9BD,GAAO,EAGA,CAAO,KAFdA,GAAO,GAEqB,IAATC,EAA0B,KArBzCA,EAAS,EACA9G,GAAO,EAAI8G,GAEX,GAmBd,EAEAzE,EAAQxB,IAAIkC,IAAM,SAAUlC,GAC3B,IAAI4C,EAAI5C,EAAI,GAAK,IACbuC,EAAIvC,EAAI,GAAK,IACbgD,EAAI,EACJkB,EAAI,EAYR,OATClB,EADGT,EAAI,GACH,EAAMK,EAAIL,EAEV,EAAMK,GAAK,EAAML,IAGd,IACP2B,GAAK3B,EAAI,GAAMS,IAAM,EAAMA,IAGrB,CAAChD,EAAI,GAAQ,IAAJgD,EAAa,IAAJkB,EAC1B,EAEA1C,EAAQG,IAAIO,IAAM,SAAUP,GAC3B,IAAIiB,EAAIjB,EAAI,GAAK,IACbkB,EAAIlB,EAAI,GAAK,IAEbqB,EAAIJ,EAAIC,EACRqB,EAAI,EAMR,OAJIlB,EAAI,IACPkB,GAAKrB,EAAIG,IAAM,EAAIA,IAGb,CAACrB,EAAI,GAAQ,IAAJqB,EAAa,IAAJkB,EAC1B,EAEA1C,EAAQU,IAAIhC,IAAM,SAAUgC,GAC3B,IAAII,EAAIJ,EAAI,GAAK,IACbc,EAAId,EAAI,GAAK,IACbnB,EAAImB,EAAI,GAAK,IAEjB,GAAU,IAANc,EACH,MAAO,CAAK,IAAJjC,EAAa,IAAJA,EAAa,IAAJA,GAG3B,IAIImF,EAJAC,EAAO,CAAC,EAAG,EAAG,GACdnC,EAAM1B,EAAI,EAAK,EACfO,EAAImB,EAAK,EACToC,EAAI,EAAIvD,EAGZ,OAAQxD,KAAK4E,MAAMD,IAClB,KAAK,EACJmC,EAAK,GAAK,EAAGA,EAAK,GAAKtD,EAAGsD,EAAK,GAAK,EAAG,MACxC,KAAK,EACJA,EAAK,GAAKC,EAAGD,EAAK,GAAK,EAAGA,EAAK,GAAK,EAAG,MACxC,KAAK,EACJA,EAAK,GAAK,EAAGA,EAAK,GAAK,EAAGA,EAAK,GAAKtD,EAAG,MACxC,KAAK,EACJsD,EAAK,GAAK,EAAGA,EAAK,GAAKC,EAAGD,EAAK,GAAK,EAAG,MACxC,KAAK,EACJA,EAAK,GAAKtD,EAAGsD,EAAK,GAAK,EAAGA,EAAK,GAAK,EAAG,MACxC,QACCA,EAAK,GAAK,EAAGA,EAAK,GAAK,EAAGA,EAAK,GAAKC,EAKtC,OAFAF,GAAM,EAAMlD,GAAKjC,EAEV,CACe,KAApBiC,EAAImD,EAAK,GAAKD,GACM,KAApBlD,EAAImD,EAAK,GAAKD,GACM,KAApBlD,EAAImD,EAAK,GAAKD,GAEjB,EAEA1E,EAAQU,IAAIP,IAAM,SAAUO,GAC3B,IAAIc,EAAId,EAAI,GAAK,IAGbW,EAAIG,EAFAd,EAAI,GAAK,KAEA,EAAMc,GACnBkB,EAAI,EAMR,OAJIrB,EAAI,IACPqB,EAAIlB,EAAIH,GAGF,CAACX,EAAI,GAAQ,IAAJgC,EAAa,IAAJrB,EAC1B,EAEArB,EAAQU,IAAIlC,IAAM,SAAUkC,GAC3B,IAAIc,EAAId,EAAI,GAAK,IAGbK,EAFIL,EAAI,GAAK,KAEJ,EAAMc,GAAK,GAAMA,EAC1BJ,EAAI,EASR,OAPIL,EAAI,GAAOA,EAAI,GAClBK,EAAII,GAAK,EAAIT,GAEVA,GAAK,IAAOA,EAAI,IACnBK,EAAII,GAAK,GAAK,EAAIT,KAGZ,CAACL,EAAI,GAAQ,IAAJU,EAAa,IAAJL,EAC1B,EAEAf,EAAQU,IAAIjC,IAAM,SAAUiC,GAC3B,IAAIc,EAAId,EAAI,GAAK,IAEbW,EAAIG,EADAd,EAAI,GAAK,KACA,EAAMc,GACvB,MAAO,CAACd,EAAI,GAAc,KAATW,EAAIG,GAAoB,KAAT,EAAIH,GACrC,EAEArB,EAAQvB,IAAIiC,IAAM,SAAUjC,GAC3B,IAAImG,EAAInG,EAAI,GAAK,IAEb4C,EAAI,EADA5C,EAAI,GAAK,IAEb+C,EAAIH,EAAIuD,EACRrF,EAAI,EAMR,OAJIiC,EAAI,IACPjC,GAAK8B,EAAIG,IAAM,EAAIA,IAGb,CAAC/C,EAAI,GAAQ,IAAJ+C,EAAa,IAAJjC,EAC1B,EAEAS,EAAQW,MAAMjC,IAAM,SAAUiC,GAC7B,MAAO,CAAEA,EAAM,GAAK,MAAS,IAAMA,EAAM,GAAK,MAAS,IAAMA,EAAM,GAAK,MAAS,IAClF,EAEAX,EAAQtB,IAAIiC,MAAQ,SAAUjC,GAC7B,MAAO,CAAEA,EAAI,GAAK,IAAO,MAAQA,EAAI,GAAK,IAAO,MAAQA,EAAI,GAAK,IAAO,MAC1E,EAEAsB,EAAQvK,KAAKiJ,IAAM,SAAU3C,GAC5B,MAAO,CAACA,EAAK,GAAK,IAAM,IAAKA,EAAK,GAAK,IAAM,IAAKA,EAAK,GAAK,IAAM,IACnE,EAEAiE,EAAQvK,KAAK+I,IAAMwB,EAAQvK,KAAK0K,IAAM,SAAUpE,GAC/C,MAAO,CAAC,EAAG,EAAGA,EAAK,GACpB,EAEAiE,EAAQvK,KAAKgJ,IAAM,SAAUhJ,GAC5B,MAAO,CAAC,EAAG,IAAKA,EAAK,GACtB,EAEAuK,EAAQvK,KAAK2K,KAAO,SAAU3K,GAC7B,MAAO,CAAC,EAAG,EAAG,EAAGA,EAAK,GACvB,EAEAuK,EAAQvK,KAAK6K,IAAM,SAAU7K,GAC5B,MAAO,CAACA,EAAK,GAAI,EAAG,EACrB,EAEAuK,EAAQvK,KAAK0J,IAAM,SAAU1J,GAC5B,IAAI2I,EAAwC,IAAlCP,KAAKG,MAAMvI,EAAK,GAAK,IAAM,KAGjC0I,IAFWC,GAAO,KAAOA,GAAO,GAAKA,GAEpBH,SAAS,IAAIC,cAClC,MAAO,SAASI,UAAUH,EAAO/B,QAAU+B,CAC5C,EAEA6B,EAAQtB,IAAIjJ,KAAO,SAAUiJ,GAE5B,MAAO,EADIA,EAAI,GAAKA,EAAI,GAAKA,EAAI,IAAM,EACzB,IAAM,IACrB,KCp0BA,SAASmG,EAAUC,GAClB,IAAIC,EAnBL,WAKC,IAJA,IAAIA,EAAQ,CAAA,EAERC,EAASvI,OAAOwI,KAAKC,GAEhB/I,EAAM6I,EAAO5I,OAAQF,EAAI,EAAGA,EAAIC,EAAKD,IAC7C6I,EAAMC,EAAO9I,IAAM,CAGlB8F,UAAW,EACXmD,OAAQ,MAIV,OAAOJ,CACR,CAIaK,GACRC,EAAQ,CAACP,GAIb,IAFAC,EAAMD,GAAW9C,SAAW,EAErBqD,EAAMjJ,QAIZ,IAHA,IAAIkJ,EAAUD,EAAME,MAChBC,EAAY/I,OAAOwI,KAAKC,EAAYI,IAE/BnJ,EAAMqJ,EAAUpJ,OAAQF,EAAI,EAAGA,EAAIC,EAAKD,IAAK,CACrD,IAAIuJ,EAAWD,EAAUtJ,GACrBwJ,EAAOX,EAAMU,IAEM,IAAnBC,EAAK1D,WACR0D,EAAK1D,SAAW+C,EAAMO,GAAStD,SAAW,EAC1C0D,EAAKP,OAASG,EACdD,EAAMM,QAAQF,GAEf,CAGF,OAAOV,CACR,CAEA,SAASa,EAAKC,EAAMtI,GACnB,OAAO,SAAUxB,GAChB,OAAOwB,EAAGsI,EAAK9J,GACjB,CACA,CAEA,SAAS+J,EAAeC,EAAShB,GAKhC,IAJA,IAAIiB,EAAO,CAACjB,EAAMgB,GAASZ,OAAQY,GAC/B/I,EAAKkI,EAAYH,EAAMgB,GAASZ,QAAQY,GAExCE,EAAMlB,EAAMgB,GAASZ,OAClBJ,EAAMkB,GAAKd,QACjBa,EAAKL,QAAQZ,EAAMkB,GAAKd,QACxBnI,EAAK4I,EAAKV,EAAYH,EAAMkB,GAAKd,QAAQc,GAAMjJ,GAC/CiJ,EAAMlB,EAAMkB,GAAKd,OAIlB,OADAnI,EAAGkJ,WAAaF,EACThJ,CACR,0GAEA,IC1EIgD,EAAU,CAAA,EAEDvD,OAAOwI,KAAKC,GAuDlBiB,SAAQ,SAAUrB,GACxB9E,EAAQ8E,GAAa,GAErBrI,OAAOoE,eAAeb,EAAQ8E,GAAY,WAAY,CAACnG,MAAOuG,EAAYJ,GAAW7E,WACrFxD,OAAOoE,eAAeb,EAAQ8E,GAAY,SAAU,CAACnG,MAAOuG,EAAYJ,GAAW5E,SAEnF,IAAIkG,EDWY,SAAUtB,GAK1B,IAJA,IAAIC,EAAQF,EAAUC,GAClBoB,EAAa,CAAA,EAEblB,EAASvI,OAAOwI,KAAKF,GAChB5I,EAAM6I,EAAO5I,OAAQF,EAAI,EAAGA,EAAIC,EAAKD,IAAK,CAClD,IAAI6J,EAAUf,EAAO9I,GAGD,OAFT6I,EAAMgB,GAERZ,SAKTe,EAAWH,GAAWD,EAAeC,EAAShB,GAC9C,CAED,OAAOmB,CACR,CC7BcG,CAAMvB,GACDrI,OAAOwI,KAAKmB,GAElBD,SAAQ,SAAUJ,GAC7B,IAAI/I,EAAKoJ,EAAOL,GAEhB/F,EAAQ8E,GAAWiB,GA5CrB,SAAqB/I,GACpB,IAAIsJ,EAAY,SAAUvK,GACzB,GAAIA,QACH,OAAOA,EAGJkB,UAAUb,OAAS,IACtBL,EAAON,MAAMC,UAAUC,MAAMkB,KAAKI,YAGnC,IAAIsJ,EAASvJ,EAAGjB,GAKhB,GAAsB,iBAAXwK,EACV,IAAK,IAAIpK,EAAMoK,EAAOnK,OAAQF,EAAI,EAAGA,EAAIC,EAAKD,IAC7CqK,EAAOrK,GAAK2B,KAAKG,MAAMuI,EAAOrK,IAIhC,OAAOqK,CACT,EAOC,MAJI,eAAgBvJ,IACnBsJ,EAAUJ,WAAalJ,EAAGkJ,YAGpBI,CACR,CAcgCE,CAAYxJ,GAC1CgD,EAAQ8E,GAAWiB,GAASU,IAlE9B,SAAiBzJ,GAChB,IAAIsJ,EAAY,SAAUvK,GACzB,OAAIA,QACIA,GAGJkB,UAAUb,OAAS,IACtBL,EAAON,MAAMC,UAAUC,MAAMkB,KAAKI,YAG5BD,EAAGjB,GACZ,EAOC,MAJI,eAAgBiB,IACnBsJ,EAAUJ,WAAalJ,EAAGkJ,YAGpBI,CACR,CA+CoCI,CAAQ1J,EAC5C,GACA,IAEA,IAAA2J,EAAiB3G,ECxEb4G,EAAS,GAAGjL,MAEZkL,EAAgB,CAEnB,UAGA,OAGA,OAGGC,EAAkB,CAAA,EACtBrK,OAAOwI,KAAKjF,GAASmG,SAAQ,SAAU9H,GACtCyI,EAAgBF,EAAO/J,KAAKmD,EAAQ3B,GAAO6B,QAAQ6G,OAAOzC,KAAK,KAAOjG,CACvE,IAEA,IAAI2I,EAAW,CAAA,EAEf,SAASC,EAAMjL,EAAKqC,GACnB,KAAM6I,gBAAgBD,GACrB,OAAO,IAAIA,EAAMjL,EAAKqC,GAOvB,GAJIA,GAASA,KAASwI,IACrBxI,EAAQ,MAGLA,KAAWA,KAAS2B,GACvB,MAAM,IAAIY,MAAM,kBAAoBvC,GAGrC,IAAInC,EACA+D,EAEJ,GAAW,MAAPjE,EACHkL,KAAK7I,MAAQ,MACb6I,KAAKnD,MAAQ,CAAC,EAAG,EAAG,GACpBmD,KAAKC,OAAS,OACR,GAAInL,aAAeiL,EACzBC,KAAK7I,MAAQrC,EAAIqC,MACjB6I,KAAKnD,MAAQ/H,EAAI+H,MAAMpI,QACvBuL,KAAKC,OAASnL,EAAImL,YACZ,GAAmB,iBAARnL,EAAkB,CACnC,IAAIuK,EAASrC,EAAY1G,IAAIxB,GAC7B,GAAe,OAAXuK,EACH,MAAM,IAAI3F,MAAM,sCAAwC5E,GAGzDkL,KAAK7I,MAAQkI,EAAOlI,MACpB4B,EAAWD,EAAQkH,KAAK7I,OAAO4B,SAC/BiH,KAAKnD,MAAQwC,EAAO5H,MAAMhD,MAAM,EAAGsE,GACnCiH,KAAKC,OAA2C,iBAA3BZ,EAAO5H,MAAMsB,GAAyBsG,EAAO5H,MAAMsB,GAAY,CACtF,MAAQ,GAAIjE,EAAII,OAAQ,CACtB8K,KAAK7I,MAAQA,GAAS,MACtB4B,EAAWD,EAAQkH,KAAK7I,OAAO4B,SAC/B,IAAImH,EAASR,EAAO/J,KAAKb,EAAK,EAAGiE,GACjCiH,KAAKnD,MAAQsD,EAAUD,EAAQnH,GAC/BiH,KAAKC,OAAkC,iBAAlBnL,EAAIiE,GAAyBjE,EAAIiE,GAAY,CACpE,MAAQ,GAAmB,iBAARjE,EAEjBA,GAAO,SACPkL,KAAK7I,MAAQ,MACb6I,KAAKnD,MAAQ,CACX/H,GAAO,GAAM,IACbA,GAAO,EAAK,IACP,IAANA,GAEDkL,KAAKC,OAAS,MACR,CACND,KAAKC,OAAS,EAEd,IAAIlC,EAAOxI,OAAOwI,KAAKjJ,GACnB,UAAWA,IACdiJ,EAAK1I,OAAO0I,EAAKqC,QAAQ,SAAU,GACnCJ,KAAKC,OAA8B,iBAAdnL,EAAIiD,MAAqBjD,EAAIiD,MAAQ,GAG3D,IAAIsI,EAAatC,EAAK8B,OAAOzC,KAAK,IAClC,KAAMiD,KAAcT,GACnB,MAAM,IAAIlG,MAAM,sCAAwC4G,KAAKC,UAAUzL,IAGxEkL,KAAK7I,MAAQyI,EAAgBS,GAE7B,IAAIrH,EAASF,EAAQkH,KAAK7I,OAAO6B,OAC7B6D,EAAQ,GACZ,IAAK7H,EAAI,EAAGA,EAAIgE,EAAO9D,OAAQF,IAC9B6H,EAAMjH,KAAKd,EAAIkE,EAAOhE,KAGvBgL,KAAKnD,MAAQsD,EAAUtD,EACvB,CAGD,GAAIiD,EAASE,KAAK7I,OAEjB,IADA4B,EAAWD,EAAQkH,KAAK7I,OAAO4B,SAC1B/D,EAAI,EAAGA,EAAI+D,EAAU/D,IAAK,CAC9B,IAAIwL,EAAQV,EAASE,KAAK7I,OAAOnC,GAC7BwL,IACHR,KAAKnD,MAAM7H,GAAKwL,EAAMR,KAAKnD,MAAM7H,IAElC,CAGFgL,KAAKC,OAAStJ,KAAKD,IAAI,EAAGC,KAAKF,IAAI,EAAGuJ,KAAKC,SAEvC1K,OAAOkL,QACVlL,OAAOkL,OAAOT,KAEhB,CA0TA,SAASU,EAAOvJ,EAAOwJ,EAASC,GAS/B,OARAzJ,EAAQ5C,MAAMa,QAAQ+B,GAASA,EAAQ,CAACA,IAElC8H,SAAQ,SAAU/C,IACtB4D,EAAS5D,KAAO4D,EAAS5D,GAAK,KAAKyE,GAAWC,CACjD,IAECzJ,EAAQA,EAAM,GAEP,SAAUD,GAChB,IAAImI,EAEJ,OAAItJ,UAAUb,QACT0L,IACH1J,EAAM0J,EAAS1J,KAGhBmI,EAASW,KAAK7I,MACP0F,MAAM8D,GAAWzJ,EACjBmI,IAGRA,EAASW,KAAK7I,KAAS0F,MAAM8D,GACzBC,IACHvB,EAASuB,EAASvB,IAGZA,EACT,CACA,CAEA,SAASwB,EAAMnK,GACd,OAAO,SAAUyD,GAChB,OAAOxD,KAAKD,IAAI,EAAGC,KAAKF,IAAIC,EAAKyD,GACnC,CACA,CAMA,SAASgG,EAAUW,EAAK5L,GACvB,IAAK,IAAIF,EAAI,EAAGA,EAAIE,EAAQF,IACL,iBAAX8L,EAAI9L,KACd8L,EAAI9L,GAAK,GAIX,OAAO8L,CACR,CAzWAf,EAAMvL,UAAY,CACjBuC,SAAU,WACT,OAAOiJ,KAAK/I,QACZ,EAED8J,OAAQ,WACP,OAAOf,KAAKA,KAAK7I,QACjB,EAEDF,OAAQ,SAAU+J,GACjB,IAAIC,EAAOjB,KAAK7I,SAAS6F,EAAY3G,GAAK2J,KAAOA,KAAKxI,MAElD3C,EAAuB,KAD3BoM,EAAOA,EAAKnK,MAAwB,iBAAXkK,EAAsBA,EAAS,IACxCf,OAAegB,EAAKpE,MAAQoE,EAAKpE,MAAMvI,OAAO0L,KAAKC,QACnE,OAAOjD,EAAY3G,GAAG4K,EAAK9J,OAAOtC,EAClC,EAEDqM,cAAe,SAAUF,GACxB,IAAIC,EAAOjB,KAAKxI,MAAMV,MAAwB,iBAAXkK,EAAsBA,EAAS,GAC9DnM,EAAuB,IAAhBoM,EAAKhB,OAAegB,EAAKpE,MAAQoE,EAAKpE,MAAMvI,OAAO0L,KAAKC,QACnE,OAAOjD,EAAY3G,GAAGmB,IAAIW,QAAQtD,EAClC,EAEDsM,MAAO,WACN,OAAuB,IAAhBnB,KAAKC,OAAeD,KAAKnD,MAAMpI,QAAUuL,KAAKnD,MAAMvI,OAAO0L,KAAKC,OACvE,EAEDmB,OAAQ,WAKP,IAJA,IAAI/B,EAAS,CAAA,EACTtG,EAAWD,EAAQkH,KAAK7I,OAAO4B,SAC/BC,EAASF,EAAQkH,KAAK7I,OAAO6B,OAExBhE,EAAI,EAAGA,EAAI+D,EAAU/D,IAC7BqK,EAAOrG,EAAOhE,IAAMgL,KAAKnD,MAAM7H,GAOhC,OAJoB,IAAhBgL,KAAKC,SACRZ,EAAOtH,MAAQiI,KAAKC,QAGdZ,CACP,EAEDgC,UAAW,WACV,IAAI7J,EAAMwI,KAAKxI,MAAMqF,MASrB,OARArF,EAAI,IAAM,IACVA,EAAI,IAAM,IACVA,EAAI,IAAM,IAEU,IAAhBwI,KAAKC,QACRzI,EAAI5B,KAAKoK,KAAKC,QAGRzI,CACP,EAED8J,WAAY,WACX,IAAI9J,EAAMwI,KAAKxI,MAAM4J,SASrB,OARA5J,EAAIY,GAAK,IACTZ,EAAIa,GAAK,IACTb,EAAIc,GAAK,IAEW,IAAhB0H,KAAKC,SACRzI,EAAIO,MAAQiI,KAAKC,QAGXzI,CACP,EAEDV,MAAO,SAAUkK,GAEhB,OADAA,EAASrK,KAAKD,IAAIsK,GAAU,EAAG,GACxB,IAAIjB,EAAMC,KAAKnD,MAAMK,IA4O9B,SAAsB8D,GACrB,OAAO,SAAUxK,GAChB,OANF,SAAiBA,EAAKwK,GACrB,OAAOO,OAAO/K,EAAIgL,QAAQR,GAC3B,CAISS,CAAQjL,EAAKwK,EACtB,CACA,CAhPkCU,CAAaV,IAAS1M,OAAO0L,KAAKC,QAASD,KAAK7I,MAChF,EAEDY,MAAO,SAAUb,GAChB,OAAInB,UAAUb,OACN,IAAI6K,EAAMC,KAAKnD,MAAMvI,OAAOqC,KAAKD,IAAI,EAAGC,KAAKF,IAAI,EAAGS,KAAQ8I,KAAK7I,OAGlE6I,KAAKC,MACZ,EAGDvN,IAAKgO,EAAO,MAAO,EAAGG,EAAM,MAC5BrS,MAAOkS,EAAO,MAAO,EAAGG,EAAM,MAC9BlV,KAAM+U,EAAO,MAAO,EAAGG,EAAM,MAE7BvD,IAAKoD,EAAO,CAAC,MAAO,MAAO,MAAO,MAAO,OAAQ,GAAG,SAAUxJ,GAAO,OAASA,EAAM,IAAO,KAAO,OAElGyK,YAAajB,EAAO,MAAO,EAAGG,EAAM,MACpCe,UAAWlB,EAAO,MAAO,EAAGG,EAAM,MAElCgB,YAAanB,EAAO,MAAO,EAAGG,EAAM,MACpCpJ,MAAOiJ,EAAO,MAAO,EAAGG,EAAM,MAE9BtD,OAAQmD,EAAO,MAAO,EAAGG,EAAM,MAC/BtS,KAAMmS,EAAO,MAAO,EAAGG,EAAM,MAE7B3M,MAAOwM,EAAO,MAAO,EAAGG,EAAM,MAC9BiB,OAAQpB,EAAO,MAAO,EAAGG,EAAM,MAE/BvU,KAAMoU,EAAO,OAAQ,EAAGG,EAAM,MAC9BtQ,QAASmQ,EAAO,OAAQ,EAAGG,EAAM,MACjCzM,OAAQsM,EAAO,OAAQ,EAAGG,EAAM,MAChCpV,MAAOiV,EAAO,OAAQ,EAAGG,EAAM,MAE/BnG,EAAGgG,EAAO,MAAO,EAAGG,EAAM,MAC1BlG,EAAG+F,EAAO,MAAO,EAAGG,EAAM,MAC1B7F,EAAG0F,EAAO,MAAO,EAAGG,EAAM,MAE1BhH,EAAG6G,EAAO,MAAO,EAAGG,EAAM,MAC1BpI,EAAGiI,EAAO,MAAO,GACjBpI,EAAGoI,EAAO,MAAO,GAEjBhI,QAAS,SAAUxB,GAClB,OAAInB,UAAUb,OACN,IAAI6K,EAAM7I,GAGX4B,EAAQkH,KAAK7I,OAAOuB,QAAQsH,KAAKnD,MACxC,EAED5E,IAAK,SAAUf,GACd,OAAInB,UAAUb,OACN,IAAI6K,EAAM7I,GAGX8F,EAAY3G,GAAG4B,IAAI+H,KAAKxI,MAAMV,QAAQ+F,MAC7C,EAEDkF,UAAW,WACV,IAAIvK,EAAMwI,KAAKxI,MAAMqF,MACrB,OAAkB,IAATrF,EAAI,KAAc,IAAiB,IAATA,EAAI,KAAc,EAAe,IAATA,EAAI,EAC/D,EAEDwK,WAAY,WAKX,IAHA,IAAIxK,EAAMwI,KAAKxI,MAAMqF,MAEjBoF,EAAM,GACDjN,EAAI,EAAGA,EAAIwC,EAAItC,OAAQF,IAAK,CACpC,IAAIkN,EAAO1K,EAAIxC,GAAK,IACpBiN,EAAIjN,GAAMkN,GAAQ,OAAWA,EAAO,MAAQvL,KAAKoE,KAAMmH,EAAO,MAAS,MAAQ,IAC/E,CAED,MAAO,MAASD,EAAI,GAAK,MAASA,EAAI,GAAK,MAASA,EAAI,EACxD,EAEDE,SAAU,SAAUC,GAEnB,IAAIC,EAAOrC,KAAKgC,aACZM,EAAOF,EAAOJ,aAElB,OAAIK,EAAOC,GACFD,EAAO,MAASC,EAAO,MAGxBA,EAAO,MAASD,EAAO,IAC/B,EAEDE,MAAO,SAAUH,GAChB,IAAII,EAAgBxC,KAAKmC,SAASC,GAClC,OAAII,GAAiB,IACb,MAGAA,GAAiB,IAAO,KAAO,EACvC,EAEDC,OAAQ,WAEP,IAAIjL,EAAMwI,KAAKxI,MAAMqF,MAErB,OADoB,IAATrF,EAAI,GAAoB,IAATA,EAAI,GAAoB,IAATA,EAAI,IAAY,IAC5C,GACb,EAEDkL,QAAS,WACR,OAAQ1C,KAAKyC,QACb,EAEDE,OAAQ,WAEP,IADA,IAAInL,EAAMwI,KAAKxI,MACNxC,EAAI,EAAGA,EAAI,EAAGA,IACtBwC,EAAIqF,MAAM7H,GAAK,IAAMwC,EAAIqF,MAAM7H,GAEhC,OAAOwC,CACP,EAEDoL,QAAS,SAAU3G,GAClB,IAAI3E,EAAM0I,KAAK1I,MAEf,OADAA,EAAIuF,MAAM,IAAMvF,EAAIuF,MAAM,GAAKZ,EACxB3E,CACP,EAEDuL,OAAQ,SAAU5G,GACjB,IAAI3E,EAAM0I,KAAK1I,MAEf,OADAA,EAAIuF,MAAM,IAAMvF,EAAIuF,MAAM,GAAKZ,EACxB3E,CACP,EAEDwL,SAAU,SAAU7G,GACnB,IAAI3E,EAAM0I,KAAK1I,MAEf,OADAA,EAAIuF,MAAM,IAAMvF,EAAIuF,MAAM,GAAKZ,EACxB3E,CACP,EAEDyL,WAAY,SAAU9G,GACrB,IAAI3E,EAAM0I,KAAK1I,MAEf,OADAA,EAAIuF,MAAM,IAAMvF,EAAIuF,MAAM,GAAKZ,EACxB3E,CACP,EAED0L,OAAQ,SAAU/G,GACjB,IAAI1E,EAAMyI,KAAKzI,MAEf,OADAA,EAAIsF,MAAM,IAAMtF,EAAIsF,MAAM,GAAKZ,EACxB1E,CACP,EAED0L,QAAS,SAAUhH,GAClB,IAAI1E,EAAMyI,KAAKzI,MAEf,OADAA,EAAIsF,MAAM,IAAMtF,EAAIsF,MAAM,GAAKZ,EACxB1E,CACP,EAED2L,UAAW,WAEV,IAAI1L,EAAMwI,KAAKxI,MAAMqF,MACjB3F,EAAe,GAATM,EAAI,GAAoB,IAATA,EAAI,GAAqB,IAATA,EAAI,GAC7C,OAAOuI,EAAMvI,IAAIN,EAAKA,EAAKA,EAC3B,EAEDiM,KAAM,SAAUlH,GACf,OAAO+D,KAAKjI,MAAMiI,KAAKC,OAAUD,KAAKC,OAAShE,EAC/C,EAEDmH,QAAS,SAAUnH,GAClB,OAAO+D,KAAKjI,MAAMiI,KAAKC,OAAUD,KAAKC,OAAShE,EAC/C,EAEDoH,OAAQ,SAAUC,GACjB,IAAIhM,EAAM0I,KAAK1I,MACXgG,EAAMhG,EAAIuF,MAAM,GAIpB,OAFAS,GADAA,GAAOA,EAAMgG,GAAW,KACZ,EAAI,IAAMhG,EAAMA,EAC5BhG,EAAIuF,MAAM,GAAKS,EACRhG,CACP,EAEDiM,IAAK,SAAUC,EAAYC,GAG1B,IAAKD,IAAeA,EAAWhM,IAC9B,MAAM,IAAIkC,MAAM,gFAAkF8J,GAEnG,IAAIE,EAASF,EAAWhM,MACpB4K,EAASpC,KAAKxI,MACdiE,OAAekI,IAAXF,EAAuB,GAAMA,EAEjC/F,EAAI,EAAIjC,EAAI,EACZhD,EAAIiL,EAAO3L,QAAUqK,EAAOrK,QAE5B6L,IAAQlG,EAAIjF,IAAO,EAAKiF,GAAKA,EAAIjF,IAAM,EAAIiF,EAAIjF,IAAM,GAAK,EAC1DoL,EAAK,EAAID,EAEb,OAAO7D,EAAMvI,IACXoM,EAAKF,EAAOhR,MAAQmR,EAAKzB,EAAO1P,MAChCkR,EAAKF,EAAOlV,QAAUqV,EAAKzB,EAAO5T,QAClCoV,EAAKF,EAAO/X,OAASkY,EAAKzB,EAAOzW,OACjC+X,EAAO3L,QAAU0D,EAAI2G,EAAOrK,SAAW,EAAI0D,GAC7C,GAIFlG,OAAOwI,KAAKjF,GAASmG,SAAQ,SAAU9H,GACtC,IAAsC,IAAlCwI,EAAcS,QAAQjJ,GAA1B,CAIA,IAAI4B,EAAWD,EAAQ3B,GAAO4B,SAG9BgH,EAAMvL,UAAU2C,GAAS,WACxB,GAAI6I,KAAK7I,QAAUA,EAClB,OAAO,IAAI4I,EAAMC,MAGlB,GAAIjK,UAAUb,OACb,OAAO,IAAI6K,EAAMhK,UAAWoB,GAG7B,IA4DmBD,EA5Df4M,EAA0C,iBAAxB/N,UAAUgD,GAAyBA,EAAWiH,KAAKC,OACzE,OAAO,IAAIF,GA2DQ7I,EA3DU4B,EAAQkH,KAAK7I,OAAOA,GAAOoI,IAAIS,KAAKnD,OA4D3DtI,MAAMa,QAAQ8B,GAAOA,EAAM,CAACA,IA5DuC5C,OAAOwP,GAAW3M,EAC7F,EAGC4I,EAAM5I,GAAS,SAAU0F,GAIxB,MAHqB,iBAAVA,IACVA,EAAQsD,EAAUT,EAAO/J,KAAKI,WAAYgD,IAEpC,IAAIgH,EAAMlD,EAAO1F,EAC1B,CAxBE,CAyBF,IA+DA,IAAA0F,EAAiBkD,iDC1cyBgE,EAA4BC,EAA4BC,GAC9F,MAAMC,EAAkBnE,EAAMgE,GACxBI,EAAkBpE,EAAMiE,IAExBpK,EAACA,EAACM,EAACA,GAAKgK,EAAgB5M,MAAM8J,SAEpC,IAAIgD,EAAWF,EAEf,KAAOE,EAASjC,SAASgC,GAAmBF,KACpCG,EAASxC,aAAe,IAI5BwC,EAAWrE,EAAM,CAACnG,IAAGM,IAAGL,EAAGuK,EAASxC,YAAc,IAGtD,OAAOwC,CACX,wCApC2CL,EAA4BC,EAA4BC,GAC/F,MAAMC,EAAkBnE,EAAMgE,GACxBI,EAAkBpE,EAAMiE,IAExBpK,EAACA,EAACM,EAACA,GAAKgK,EAAgB5M,MAAM8J,SAEpC,IAAIgD,EAAWF,EAEf,KAAOE,EAASjC,SAASgC,GAAmBF,KACpCG,EAASxC,aAAe,MAI5BwC,EAAWrE,EAAM,CAACnG,IAAGM,IAAGL,EAAGuK,EAASxC,YAAc,IAGtD,OAAOwC,CACX,gCAqBM,SAAsCJ,GACxC,MAAMG,EAAkBpE,EAAMiE,GAExB9P,EAAQ6L,EAAM,CAAC3H,EAAG,IAAKC,EAAG,IAAKC,EAAG,MAClC7M,EAAQsU,EAAM,CAAC3H,EAAG,EAAGC,EAAG,EAAGC,EAAG,IASpC,MAL4B,KAAxB6L,EAAgBzR,MACU,KAA1ByR,EAAgB3V,QACM,KAAtB2V,EAAgB7L,KAGL,IAAO7M,EAAQyI,CAClC"}