{"name": "@tryghost/color-utils", "version": "0.2.10", "repository": {"type": "git", "url": "git+https://github.com/TryGhost/SDK.git", "directory": "packages/color-utils"}, "author": "Ghost Foundation", "license": "MIT", "main": "cjs/color-utils.js", "umd:main": "umd/color-utils.min.js", "unpkg": "umd/color-utils.min.js", "module": "es/color-utils.js", "source": "src/color-utils.ts", "types": "types/color-utils.d.ts", "scripts": {"dev": "echo \"Implement me!\"", "test": "NODE_ENV=testing mocha './test/**/*.test.js'", "build": "rollup -c && tsc --declaration --emitDeclarationOnly --declarationDir ./types", "lint": "eslint . --ext .js --cache", "prepare": "NODE_ENV=production yarn build", "posttest": "yarn lint"}, "files": ["LICENSE", "README.md", "cjs/", "src/", "umd/", "es/", "types/"], "publishConfig": {"access": "public"}, "devDependencies": {"@rollup/plugin-typescript": "12.1.4", "mocha": "11.2.2", "rollup": "2.79.2", "rollup-plugin-commonjs": "10.1.0", "rollup-plugin-node-resolve": "5.2.0", "rollup-plugin-replace": "2.2.0", "rollup-plugin-terser": "7.0.2", "should": "13.2.3", "sinon": "21.0.0", "typescript": "5.9.2"}, "dependencies": {"@types/color": "3.0.6", "color": "^3.2.1"}}