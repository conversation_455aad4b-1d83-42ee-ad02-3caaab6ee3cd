{"name": "@tryghost/social-urls", "version": "0.1.54", "repository": {"type": "git", "url": "git+https://github.com/TryGhost/SDK.git", "directory": "packages/social-urls"}, "author": "Ghost Foundation", "license": "MIT", "main": "lib/index.js", "scripts": {"dev": "echo \"Implement me!\"", "test": "NODE_ENV=testing c8 --all --reporter text --reporter co<PERSON><PERSON> mocha './test/**/*.test.js'", "lint": "eslint . --ext .js --cache", "posttest": "yarn lint"}, "files": ["lib/"], "publishConfig": {"access": "public"}, "devDependencies": {"c8": "10.1.3", "mocha": "11.2.2", "should": "13.2.3", "sinon": "21.0.0"}}