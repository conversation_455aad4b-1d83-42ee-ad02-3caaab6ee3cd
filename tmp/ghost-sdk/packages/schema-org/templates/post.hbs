{
    "@context": "https://schema.org",
    "@type": "Article",
    {{> "publisher"}}
    {{> "main_entity"}}
    {{#if author}}
    "author": {
        "@type": "Person",
        "name": "{{author.name}}",
        "url": "{{author.url}}",
        "sameAs": {{array author.sameAs}},
        "image": {{> "image" image=author.image}},
        "description": "{{author.description}}"
    },
    {{/if}}
    "headline": "{{meta.title}}",
    "url": "{{meta.url}}",
    "datePublished": "{{meta.datePublished}}",
    "dateModified": "{{meta.dateModified}}",
    "image": {{> "image" image=meta.image}},
    "keywords": "{{join meta.keywords}}",
    "description": "{{meta.description}}"
}
