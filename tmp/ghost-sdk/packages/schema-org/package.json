{"name": "@tryghost/schema-org", "version": "0.1.45", "repository": {"type": "git", "url": "git+https://github.com/TryGhost/SDK.git", "directory": "packages/schema-org"}, "author": "Ghost Foundation", "license": "MIT", "main": "index.js", "scripts": {"dev": "echo \"Implement me!\"", "test": "NODE_ENV=testing c8 --all --reporter text --reporter co<PERSON><PERSON> mocha './test/**/*.test.js'", "lint": "eslint . --ext .js --cache", "posttest": "yarn lint", "coverage": "NODE_ENV=testing istanbul cover --dir test/coverage _mocha './test/**/*.test.js'"}, "files": ["*.js", "lib", "templates", "partials"], "publishConfig": {"access": "public"}, "devDependencies": {"c8": "10.1.3", "istanbul": "0.4.5", "mocha": "11.2.2", "should": "13.2.3", "sinon": "21.0.0"}, "dependencies": {"glob": "^9.0.0", "handlebars": "^4.7.7", "lodash": "^4.17.11"}}