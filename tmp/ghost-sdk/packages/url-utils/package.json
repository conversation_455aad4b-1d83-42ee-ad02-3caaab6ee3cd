{"name": "@tryghost/url-utils", "version": "4.4.15", "repository": {"type": "git", "url": "git+https://github.com/TryGhost/SDK.git", "directory": "packages/url-utils"}, "author": "Ghost Foundation", "license": "MIT", "main": "index.js", "scripts": {"dev": "echo \"Implement me!\"", "test": "NODE_ENV=testing c8 --all --reporter text --reporter co<PERSON><PERSON> --reporter html mocha './test/**/*.test.js'", "lint": "eslint . --ext .js --cache", "posttest": "yarn lint"}, "files": ["lib/", "index.js"], "publishConfig": {"access": "public"}, "devDependencies": {"@tryghost/config-url-helpers": "^1.0.17", "c8": "10.1.3", "mocha": "11.2.2", "rewire": "9.0.0", "should": "13.2.3", "sinon": "21.0.0"}, "dependencies": {"cheerio": "^0.22.0", "lodash": "^4.17.21", "moment": "^2.27.0", "moment-timezone": "^0.5.31", "remark": "^11.0.2", "remark-footnotes": "^1.0.0", "unist-util-visit": "^2.0.0"}}