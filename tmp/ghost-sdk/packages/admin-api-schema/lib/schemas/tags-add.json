{"$schema": "http://json-schema.org/draft-07/schema#", "$id": "tags.add", "title": "tags.add", "description": "Schema for tags.add", "type": "object", "additionalProperties": false, "properties": {"tags": {"type": "array", "minItems": 1, "maxItems": 1, "additionalProperties": false, "items": {"type": "object", "allOf": [{"$ref": "tags#/definitions/tag"}], "required": ["name"]}}}, "required": ["tags"]}