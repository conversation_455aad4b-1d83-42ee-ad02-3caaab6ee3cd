{"$schema": "http://json-schema.org/draft-07/schema#", "$id": "products", "title": "products", "description": "Base products definitions", "definitions": {"product": {"description": "A Ghost Product", "type": "object", "additionalProperties": false, "properties": {"name": {"type": "string", "maxLength": 191}, "slug": {"type": ["string", "null"], "maxLength": 191}, "welcome_page_url": {"type": ["string", "null"], "maxLength": 2000}, "active": {"type": "boolean"}, "stripe_prices": {"type": ["array", "null"], "items": {"type": "producs#/definitions/stripe_price"}}, "benefits": {"type": ["array", "null"], "items": {"type": "producs#/definitions/product-benefit"}}}}, "stripe_price": {"description": "A Stripe Price associated with a Product", "type": "object", "properties": {"id": {"type": "string", "maxLength": 24}, "stripe_product_id": {"type": ["string", "null"], "maxLength": 255}, "stripe_price_id": {"type": "string", "maxLength": 255}, "nickname": {"type": "string", "maxLength": 50}, "currency": {"type": "string", "maxLength": 3}, "amount": {"type": "number"}, "active": {"type": "boolean", "default": true}, "type": {"type": "string", "enum": ["recurring", "one_time"]}, "interval": {"type": ["string", "null"], "enum": ["year", "month", "week", "day"]}}}, "product-benefit": {"description": "A Benefit associated with a Product", "type": "object", "properties": {"id": {"type": "string", "maxLength": 24}, "name": {"type": "string", "maxLength": 191}, "slug": {"type": "string", "maxLength": 191}}, "anyOf": [{"required": ["id"]}, {"required": ["name"]}, {"required": ["slug"]}]}}}