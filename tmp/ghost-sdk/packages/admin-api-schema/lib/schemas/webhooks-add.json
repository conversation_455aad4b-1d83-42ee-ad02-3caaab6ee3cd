{"$schema": "http://json-schema.org/draft-07/schema#", "$id": "webhooks.add", "title": "webhooks.add", "description": "Schema for webhooks.add", "type": "object", "additionalProperties": false, "properties": {"webhooks": {"type": "array", "minItems": 1, "maxItems": 1, "items": {"type": "object", "allOf": [{"$ref": "webhooks#/definitions/webhook"}], "required": ["event", "target_url"]}}}, "required": ["webhooks"]}