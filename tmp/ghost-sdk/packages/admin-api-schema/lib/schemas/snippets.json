{"$schema": "http://json-schema.org/draft-07/schema#", "$id": "snippets", "title": "snippets", "description": "Base snippets definitions", "definitions": {"snippet": {"type": "object", "additionalProperties": false, "properties": {"name": {"type": "string", "minLength": 1, "maxLength": 191}, "mobiledoc": {"type": "string", "format": "json-string", "maxLength": 1000000000}, "lexical": {"type": ["string", "null"], "format": "json-string", "maxLength": 1000000000}}}}}