{"$schema": "http://json-schema.org/draft-07/schema#", "$id": "webhooks.edit", "title": "webhooks.edit", "description": "Schema for webhooks.edit", "type": "object", "additionalProperties": false, "properties": {"webhooks": {"type": "array", "minItems": 1, "maxItems": 1, "items": {"type": "object", "additionalProperties": false, "properties": {"event": {"type": "string", "maxLength": 50, "isLowercase": true}, "target_url": {"type": "string", "format": "uri-reference", "maxLength": 2000}, "name": {"type": ["string", "null"], "maxLength": 191}, "secret": {"type": ["string", "null"], "maxLength": 191}, "api_version": {"type": ["string", "null"], "maxLength": 50}}}}}, "required": ["webhooks"]}