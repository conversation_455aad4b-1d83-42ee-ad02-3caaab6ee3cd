{"name": "@tryghost/admin-api-schema", "version": "4.5.10", "repository": {"type": "git", "url": "git+https://github.com/TryGhost/SDK.git", "directory": "packages/admin-api-schema"}, "author": "Ghost Foundation", "license": "MIT", "main": "index.js", "scripts": {"dev": "echo \"Implement me!\"", "test": "NODE_ENV=testing c8 --all --reporter text --reporter co<PERSON><PERSON> mocha './test/**/*.test.js'", "lint": "eslint . --ext .js --cache", "posttest": "yarn lint"}, "files": ["lib/*", "index.js"], "publishConfig": {"access": "public"}, "devDependencies": {"c8": "10.1.3", "mocha": "11.2.2", "should": "13.2.3", "sinon": "21.0.0"}, "dependencies": {"@tryghost/errors": "^1.2.26", "ajv": "^6.12.6", "lodash": "^4.17.11"}}