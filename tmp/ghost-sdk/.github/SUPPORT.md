# How to get support for Ghost 👨‍👩‍👧‍👦

For **help**, **support**, **questions** and **ideas** please use **[our forum](https://forum.ghost.org)**  🚑.

Please **_do not_** raise an issue on GitHub.

We have a **help** category in our **[forum](https://forum.ghost.org/)** where you can get quick answers,
help with debugging weird issues, and general help with any aspect of <PERSON>. There's also an **ideas** category for feature requests.

Our extensive **documentation** can be found at https://ghost.org/docs/.

Please go to https://forum.ghost.org and signup to join our community.
You can create a new account, or signup using Google, Twitter or Facebook.

Issues which are not bug reports will be closed.

## Using Ghost(Pro)?

**Ghost(Pro)** users have access to email support via the support at ghost dot org address.

## Why not GitHub?

GitHub is our office, it's the place where our development team does its work. We use the issue list
to keep track of bugs and the features that we are working on. We do this openly for transparency.

With the forum, you can leverage the knowledge of our wider community to get help with any problems you are
having with <PERSON>. Please keep in mind that <PERSON> is FLOSS, and free support is provided by the goodwill
of our wonderful community members.
