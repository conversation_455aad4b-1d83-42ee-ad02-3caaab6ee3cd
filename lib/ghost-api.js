#!/usr/bin/env node

const fs = require('fs');
const path = require('path');
const yaml = require('js-yaml');
const GhostAdminAPI = require('@tryghost/admin-api');
const { marked } = require('marked');
const crypto = require('crypto');

// Configure marked for better HTML output
marked.setOptions({
  gfm: true,
  breaks: false,
  sanitize: false,
  smartypants: true
});

/**
 * Ghost API wrapper class that provides high-level methods for interacting with Ghost.io
 */
class GhostAPI {
  constructor(config, options = {}) {
    this.config = config;
    this.options = {
      verbose: false,
      dryRun: false,
      ...options
    };
    this.api = this.initGhostAPI();
  }

  /**
   * Load Ghost configuration from YAML file
   */
  static loadConfig(configPath = 'config/ghost.yml') {
    const resolvedPath = path.resolve(configPath);

    if (!fs.existsSync(resolvedPath)) {
      throw new Error(`Ghost config file not found: ${resolvedPath}`);
    }

    try {
      const configContent = fs.readFileSync(resolvedPath, 'utf8');
      const config = yaml.load(configContent);

      if (!config.integrations || !config.integrations.page_sync) {
        throw new Error('Missing page_sync integration in Ghost config');
      }

      const integration = config.integrations.page_sync;

      if (!integration.admin_api_key) {
        throw new Error('Missing admin_api_key in Ghost config');
      }

      return integration;
    } catch (error) {
      throw new Error(`Error loading Ghost config: ${error.message}`);
    }
  }

  /**
   * Initialize Ghost Admin API
   */
  initGhostAPI() {
    if (this.options.verbose) {
      console.log('🔧 API Configuration:');
      console.log(`   URL: https://solnic.ghost.io`);
      console.log(`   Key: ${this.config.admin_api_key.substring(0, 20)}...`);
      console.log(`   Version: v6.0`);
    }

    // Validate API key format (should be id:secret)
    if (!this.config.admin_api_key.includes(':')) {
      throw new Error('Invalid Admin API key format. Expected format: id:secret');
    }

    return new GhostAdminAPI({
      url: 'https://solnic.ghost.io',
      key: this.config.admin_api_key,
      version: 'v6.0'
    });
  }

  /**
   * Find posts in Ghost by title or get all posts
   */
  async findPosts(title = null) {
    try {
      let posts = [];

      if (title) {
        // Search for specific post by title
        if (this.options.verbose) {
          console.log(`🔍 Searching for post with title: "${title}"`);
        }

        // Ghost API doesn't have exact title search, so we'll browse and filter
        let page = 1;
        const limit = 100;
        let found = false;

        while (!found) {
          const pagePosts = await this.api.posts.browse({
            limit: limit,
            page: page,
            include: 'tags,authors',
            formats: 'html,lexical'
          });

          if (pagePosts.length === 0) break;

          // Look for exact title match (case insensitive)
          const matchingPost = pagePosts.find(post =>
            post.title.toLowerCase() === title.toLowerCase()
          );

          if (matchingPost) {
            posts = [matchingPost];
            found = true;
          } else {
            page++;
          }
        }

        if (!found) {
          console.error(`❌ Post with title "${title}" not found in Ghost`);
          return [];
        }

      } else {
        // Get all posts
        console.log('🔍 Fetching all posts from Ghost...');

        let page = 1;
        const limit = 100;

        while (true) {
          const pagePosts = await this.api.posts.browse({
            limit: limit,
            page: page,
            include: 'tags,authors',
            formats: 'html,lexical'
          });

          if (pagePosts.length === 0) break;

          posts = posts.concat(pagePosts);
          console.log(`📄 Fetched ${posts.length} posts so far...`);
          page++;
        }
      }

      return posts;

    } catch (error) {
      console.error('❌ Error fetching posts from Ghost:', error.message);
      throw error;
    }
  }

  /**
   * Find page by slug
   */
  async findPageBySlug(slug) {
    try {
      const pages = await this.api.pages.browse({
        filter: `slug:${slug}`,
        limit: 1
      });

      return pages.length > 0 ? pages[0] : null;
    } catch (error) {
      console.error(`❌ Error finding page with slug "${slug}":`, error.message);
      throw error;
    }
  }

  /**
   * Find post by slug
   */
  async findPostBySlug(slug) {
    try {
      if (this.options.verbose) {
        console.log(`🔍 Searching for post with slug: "${slug}"`);
      }

      const posts = await this.api.posts.browse({
        filter: `slug:${slug}`,
        limit: 1
      });

      if (this.options.verbose) {
        console.log(`   Found ${posts.length} posts matching slug "${slug}"`);
      }

      return posts.length > 0 ? posts[0] : null;
    } catch (error) {
      console.error(`Error finding post with slug "${slug}":`, error.message);
      return null;
    }
  }

  /**
   * Get the current user (author) from Ghost
   */
  async getCurrentUser() {
    try {
      const users = await this.api.users.browse({
        limit: 1
      });

      if (users.length === 0) {
        throw new Error('No users found in Ghost');
      }

      // Return the first user (should be the owner/admin)
      return users[0];
    } catch (error) {
      console.error('Error getting current user:', error.message);
      throw error;
    }
  }

  /**
   * Create or update a post
   */
  async createOrUpdatePost(postData, existingPost = null) {
    try {
      const isNewPost = !existingPost;

      if (this.options.dryRun) {
        const action = isNewPost ? 'create' : 'update';
        console.log(`🔍 Would ${action}: "${postData.title}" (${postData.slug})`);
        return { action, post: postData };
      }

      let result;
      if (isNewPost) {
        result = await this.api.posts.add(postData);
        console.log(`✅ Created: "${postData.title}"`);
      } else {
        // Add required fields for updates
        postData.id = existingPost.id;
        postData.updated_at = existingPost.updated_at;

        result = await this.api.posts.edit(postData);
        console.log(`✅ Updated: "${postData.title}"`);
      }

      return { action: isNewPost ? 'created' : 'updated', post: result };
    } catch (error) {
      const action = existingPost ? 'updating' : 'creating';
      console.error(`❌ Error ${action} "${postData.title}":`, error.message);
      throw error;
    }
  }

  /**
   * Update a page
   */
  async updatePage(pageData, existingPage) {
    try {
      if (this.options.dryRun) {
        console.log(`🔍 Would update page: "${existingPage.title}"`);
        return { action: 'dry-run', page: pageData };
      }

      // Add required fields for updates
      pageData.id = existingPage.id;
      pageData.updated_at = existingPage.updated_at;

      const result = await this.api.pages.edit(pageData);
      console.log(`✅ Updated page: "${existingPage.title}"`);

      return { action: 'updated', page: result };
    } catch (error) {
      console.error(`❌ Error updating page "${existingPage.title}":`, error.message);
      throw error;
    }
  }

  /**
   * Delete a post
   */
  async deletePost(postId) {
    try {
      if (this.options.dryRun) {
        console.log(`🔍 Would delete post ID: ${postId}`);
        return { action: 'dry-run', postId };
      }

      await this.api.posts.delete({ id: postId });
      console.log(`✅ Deleted post ID: ${postId}`);

      return { action: 'deleted', postId };
    } catch (error) {
      console.error(`❌ Error deleting post ID ${postId}:`, error.message);
      throw error;
    }
  }
}

module.exports = { GhostAPI };
