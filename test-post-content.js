#!/usr/bin/env node

const GhostAdminAPI = require('@tryghost/admin-api');
const yaml = require('js-yaml');
const fs = require('fs');

// Load Ghost config
const configPath = 'config/ghost.yml';
const config = yaml.load(fs.readFileSync(configPath, 'utf8'));

// Initialize Ghost Admin API
const api = new GhostAdminAPI({
  url: 'https://solnic.ghost.io',
  key: config.integrations.page_sync.admin_api_key,
  version: 'v6.0'
});

async function inspectPost() {
  try {
    // Find the specific post
    const posts = await api.posts.browse({
      filter: 'slug:announcing-textparser-for-elixir',
      include: 'tags,authors',
      limit: 1
    });

    if (posts.length === 0) {
      console.log('❌ Post not found');
      return;
    }

    const post = posts[0];
    console.log('📄 Post found:', post.title);
    console.log('📄 Post ID:', post.id);
    console.log('📄 Status:', post.status);
    console.log('📄 Last updated:', post.updated_at);
    console.log('📄 Published at:', post.published_at || 'not published');

    console.log('\n🔍 Content formats:');
    console.log('📄 Has HTML:', !!post.html);
    console.log('📄 Has mobiledoc:', !!post.mobiledoc);
    console.log('📄 Has lexical:', !!post.lexical);

    if (post.mobiledoc) {
      console.log('\n⚠️ MOBILEDOC CONTENT FOUND:');
      console.log('📄 Mobiledoc length:', post.mobiledoc.length);
      console.log('📄 Mobiledoc preview:', post.mobiledoc.substring(0, 200) + '...');
    } else {
      console.log('\n✅ NO MOBILEDOC CONTENT');
    }

    if (post.lexical) {
      console.log('\n✅ LEXICAL CONTENT FOUND:');
      console.log('📄 Lexical length:', post.lexical.length);
      console.log('📄 Lexical preview:', post.lexical.substring(0, 300) + '...');

      try {
        const lexicalDoc = JSON.parse(post.lexical);
        console.log('📄 Lexical structure:');
        console.log('   Root type:', lexicalDoc.root?.type);
        console.log('   Children count:', lexicalDoc.root?.children?.length);
        if (lexicalDoc.root?.children?.length > 0) {
          console.log('   First child type:', lexicalDoc.root.children[0].type);
          if (lexicalDoc.root.children[0].type === 'markdown') {
            console.log('   ⚠️ Contains raw markdown card (old format)');
            console.log('   Markdown length:', lexicalDoc.root.children[0].markdown?.length);
          } else {
            console.log('   ✅ Contains proper lexical nodes (new format)');
            console.log('   First child details:', JSON.stringify(lexicalDoc.root.children[0], null, 2).substring(0, 200) + '...');
          }
        }
      } catch (error) {
        console.log('   ❌ Failed to parse lexical JSON:', error.message);
      }
    } else {
      console.log('\n❌ NO LEXICAL CONTENT');
    }

  } catch (error) {
    console.error('❌ Error:', error.message);
  }
}

inspectPost();
